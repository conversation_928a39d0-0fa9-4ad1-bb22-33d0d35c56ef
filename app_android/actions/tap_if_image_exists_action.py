from .base_action import BaseAction
from airtest.core.api import Template, wait, touch, exists
from airtest.core.error import TargetNotFoundError
from airtest.core.helper import G
import os
import base64
import traceback

class TapIfImageExistsAction(BaseAction):
    """Handler for tap if image exists actions - EXACT copy of TapAction logic"""

    def scale_ios_coordinates(self, coordinates):
        """
        Scale coordinates for iOS devices if needed (EXACT same as TapAction)

        Args:
            coordinates: Tuple of (x, y) coordinates

        Returns:
            tuple: Scaled coordinates
        """
        try:
            x, y = coordinates

            # Get device dimensions
            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                device_width = self.controller.device_dimensions.get('width')
                device_height = self.controller.device_dimensions.get('height')

                if device_width and device_height:
                    # Check if we need to scale (if image dimensions don't match device dimensions)
                    if hasattr(self.controller, 'airtest_device') and self.controller.airtest_device:
                        try:
                            # Get the screen resolution from Airtest device
                            airtest_resolution = self.controller.airtest_device.get_current_resolution()
                            if airtest_resolution and len(airtest_resolution) == 2:
                                airtest_width, airtest_height = airtest_resolution

                                # Only scale if dimensions are different
                                if airtest_width != device_width or airtest_height != device_height:
                                    scale_x = device_width / airtest_width
                                    scale_y = device_height / airtest_height

                                    self.logger.info(f"Scaling coordinates by factors: x={scale_x}, y={scale_y}")
                                    return (int(x * scale_x), int(y * scale_y))
                        except Exception as e:
                            self.logger.warning(f"Error getting Airtest resolution: {e}")

            # Return original coordinates if no scaling needed or if scaling failed
            return coordinates
        except Exception as e:
            self.logger.warning(f"Error in scale_ios_coordinates: {e}")
            return coordinates
    def execute(self, params):
        """
        Execute tap if image exists action - EXACT copy of TapAction image method logic
        The only difference: returns success when image is not found instead of error

        Args:
            params: Dictionary containing:
                - image_filename: Reference image to tap on
                - threshold: (Optional) Matching threshold (default: 0.7)
                - timeout: (Optional) Maximum time to wait in seconds (default: 5)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "No device controller available - step passed (if exists behavior)"}

        # Extract parameters
        image_filename = params.get('image_filename')
        threshold = float(params.get('threshold', 0.7))
        timeout = int(params.get('timeout', 5))

        if not image_filename:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "Image filename is required - step passed (if exists behavior)"}

        self.logger.info(f"Executing tap if image exists action for image: {image_filename} with threshold: {threshold} and timeout: {timeout}s")

        # Get the absolute path to the reference image (EXACT same as TapAction)
        # Resolve the image path properly
        abs_path = image_filename
        if not os.path.exists(image_filename):
            # Try to resolve from reference_images directory
            try:
                from config import DIRECTORIES
                reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                if reference_dir:
                    full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                    if os.path.exists(full_path):
                        abs_path = full_path
                        self.logger.info(f"Resolved image path to: {abs_path}")
                    else:
                        # Try directly in reference_images folder
                        ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                        if os.path.exists(ref_path):
                            abs_path = ref_path
                            self.logger.info(f"Resolved image path to: {abs_path}")
            except (ImportError, Exception) as e:
                self.logger.warning(f"Could not resolve image path from config: {e}")
                # Fallback to reference_images folder
                ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                if os.path.exists(ref_path):
                    abs_path = ref_path
                    self.logger.info(f"Fallback resolved image path to: {abs_path}")
        else:
            abs_path = image_filename

        if not os.path.exists(abs_path):
            self.logger.error(f"Reference image not found: {abs_path}")
            # Always return success for "if exists" behavior
            return {"status": "success", "message": f"Reference image not found - step passed (if exists behavior): {image_filename}"}

        # EXACT COPY of TapAction image method logic starts here
        try:
            # Log the device info to help with debugging
            if hasattr(G, 'DEVICE') and G.DEVICE:
                self.logger.info(f"Current Airtest device: {G.DEVICE}")

            # Use Airtest ONLY for finding the image coordinates (EXACT same as TapAction)
            self.logger.info(f"Using Airtest only for image recognition, not for tapping")

            # Create template and find its position with timeout compliance (EXACT same as TapAction)
            template_image = Template(abs_path, threshold=threshold)

            def wait_operation():
                return wait(template_image, timeout=timeout)

            # Use timeout-aware retry mechanism
            wait_result = self.execute_with_retry(
                wait_operation,
                max_retries=1,  # Image finding typically doesn't need many retries
                retry_delay=1,
                operation_name=f"wait_for_image({os.path.basename(image_filename)})",
                action_timeout=timeout
            )

            # Handle timeout compliance errors
            if isinstance(wait_result, dict) and wait_result.get('status') == 'error':
                if 'timeout' in wait_result.get('message', '').lower():
                    self.logger.info(f"Image '{os.path.basename(abs_path)}' not found within {timeout}s timeout - step passed (if exists behavior)")
                    return {"status": "success", "message": f"Image '{image_filename}' not found within {timeout}s timeout - step passed (if exists behavior)"}
                else:
                    # Other errors during wait operation
                    return {"status": "success", "message": f"Image search failed - step passed (if exists behavior): {wait_result.get('message')}"}

            match_pos = wait_result
            self.logger.info(f"Found image at position: {match_pos}, now using Appium to tap")

            # Skip all Airtest touch methods and go directly to Appium (EXACT same as TapAction)
            if hasattr(self.controller, 'driver') and self.controller.driver:
                # For iOS, apply scaling if needed (EXACT same as TapAction)
                if hasattr(self.controller, 'platform_name') and self.controller.platform_name == 'iOS':
                    original_pos = match_pos
                    match_pos = self.scale_ios_coordinates(match_pos)
                    self.logger.info(f"Applied iOS scaling: {original_pos} -> {match_pos}")

                # Extract coordinates (EXACT same as TapAction)
                x, y = match_pos

                # Try using mobile: tap command first (EXACT same as TapAction)
                try:
                    self.logger.info(f"Trying mobile: tap command at ({x}, {y})")
                    self.controller.driver.execute_script("mobile: tap", {"x": int(x), "y": int(y)})
                    return {"status": "success", "message": f"Tapped at {match_pos} using mobile: tap after finding image with Airtest"}
                except Exception as mobile_tap_err:
                    self.logger.warning(f"mobile: tap failed: {mobile_tap_err}")
                    # Try using Appium's TouchAction (EXACT same as TapAction)
                    try:
                        from appium.webdriver.common.touch_action import TouchAction
                        actions = TouchAction(self.controller.driver)
                        actions.tap(x=int(x), y=int(y)).perform()
                        return {"status": "success", "message": f"Tapped at {match_pos} using Appium TouchAction after finding image with Airtest"}
                    except Exception as touch_action_err:
                        self.logger.warning(f"Appium TouchAction failed: {touch_action_err}")

                        # One last attempt with W3C actions if available (EXACT same as TapAction)
                        try:
                            self.logger.info("Trying W3C Actions API")
                            from selenium.webdriver.common.action_chains import ActionChains
                            from selenium.webdriver.common.actions.pointer_input import PointerInput
                            from selenium.webdriver.common.actions.action_builder import ActionBuilder

                            # Create pointer input
                            pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                            # Create action chains
                            actions = ActionBuilder(self.controller.driver, mouse=pointer)
                            # Add pointer move and pointer down (tap)
                            actions.pointer_action.move_to_location(int(x), int(y))
                            actions.pointer_action.click()
                            # Perform the action
                            actions.perform()
                            return {"status": "success", "message": f"Tapped at {match_pos} using W3C Actions after finding image with Airtest"}
                        except Exception as w3c_err:
                            self.logger.error(f"All Appium tap methods failed: {w3c_err}")

                            # Only now try the Airtest touch as a last resort (EXACT same as TapAction)
                            self.logger.warning("All Appium methods failed, falling back to Airtest touch as last resort")
                            touch(template_image)
                            return {"status": "success", "message": f"Tapped on image using Airtest touch as fallback"}
            else:
                self.logger.warning("No Appium driver available, using Airtest touch")
                touch(template_image)
                return {"status": "success", "message": f"Tapped on image using Airtest touch (no Appium driver)"}
        except Exception as e:
            # Check if it's a TargetNotFoundError specifically
            is_target_not_found = False
            try:
                # Try to import and check the exception type
                from airtest.core.error import TargetNotFoundError as TNF
                is_target_not_found = isinstance(e, TNF)
            except ImportError:
                # Fallback to string checking if import fails
                is_target_not_found = 'TargetNotFoundError' in str(type(e)) or 'TargetNotFoundError' in str(e.__class__.__name__)

            if is_target_not_found:
                self.logger.info(f"Image '{os.path.basename(abs_path)}' not found within {timeout} seconds with threshold {threshold}")
                # Try exists() to see if we can find it with a lower threshold (EXACT same as TapAction)
                try:
                    match_result = exists(template_image)
                    if match_result:
                        self.logger.info(f"Found with exists() but below threshold: {match_result}")
                        # If we found it with exists but not wait, try to tap on it anyway
                        try:
                            self.logger.info(f"Attempting to tap on image found with exists(): {match_result}")
                            touch(match_result)
                            return {"status": "success", "message": f"Tapped on image found with exists() at {match_result}"}
                        except Exception as touch_err:
                            self.logger.warning(f"Failed to tap on image found with exists(): {touch_err}")
                    else:
                        self.logger.info(f"Image not found even with exists() method")
                except Exception as exists_err:
                    self.logger.warning(f"exists() method also failed: {exists_err}")

                # KEY DIFFERENCE: Return success instead of error for "if exists" behavior
                self.logger.info(f"Image '{image_filename}' not found - marking as passed (if exists behavior)")
                return {"status": "success", "message": f"Image '{image_filename}' not found - step passed (if exists behavior)"}
            else:
                # For any other error, still return success for "if exists" behavior
                self.logger.error(f"Error in image recognition: {e}")
                return {"status": "success", "message": f"Image '{image_filename}' not found due to error - step passed (if exists behavior)"}


