Action Log - 2025-07-26 11:22:02
================================================================================

[[11:22:02]] [INFO] Generating execution report...
[[11:22:02]] [SUCCESS] All tests passed successfully!
[[11:22:02]] [INFO] N9lUf5Qlrv=fail
[[11:22:02]] [ERROR] Action 44 failed: No Appium driver available
[[11:17:55]] [SUCCESS] Screenshot refreshed successfully
[[11:17:55]] [SUCCESS] Screenshot refreshed successfully
[[11:17:54]] [INFO] N9lUf5Qlrv=running
[[11:17:54]] [INFO] Executing action 44/44: Action: ifThenSteps
[[11:17:53]] [SUCCESS] Screenshot refreshed
[[11:17:53]] [INFO] Refreshing screenshot...
[[11:17:53]] [INFO] OyUowAaBzD=pass
[[11:17:10]] [SUCCESS] Screenshot refreshed successfully
[[11:17:10]] [SUCCESS] Screenshot refreshed successfully
[[11:17:09]] [INFO] OyUowAaBzD=running
[[11:17:09]] [INFO] Executing action 43/44: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[11:17:09]] [SUCCESS] Screenshot refreshed
[[11:17:09]] [INFO] Refreshing screenshot...
[[11:17:09]] [INFO] Ob26qqcA0p=pass
[[11:17:04]] [SUCCESS] Screenshot refreshed successfully
[[11:17:04]] [SUCCESS] Screenshot refreshed successfully
[[11:17:04]] [INFO] Ob26qqcA0p=running
[[11:17:04]] [INFO] Executing action 42/44: Swipe from (50%, 70%) to (50%, 30%)
[[11:17:03]] [SUCCESS] Screenshot refreshed
[[11:17:03]] [INFO] Refreshing screenshot...
[[11:17:03]] [INFO] F1olhgKhUt=pass
[[11:17:00]] [SUCCESS] Screenshot refreshed successfully
[[11:17:00]] [SUCCESS] Screenshot refreshed successfully
[[11:17:00]] [INFO] F1olhgKhUt=running
[[11:17:00]] [INFO] Executing action 41/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[11:17:00]] [SUCCESS] Screenshot refreshed
[[11:17:00]] [INFO] Refreshing screenshot...
[[11:17:00]] [INFO] yhmzeynQyu=pass
[[11:16:57]] [SUCCESS] Screenshot refreshed successfully
[[11:16:57]] [SUCCESS] Screenshot refreshed successfully
[[11:16:56]] [INFO] yhmzeynQyu=running
[[11:16:56]] [INFO] Executing action 40/44: Tap on Text: "Remove"
[[11:16:56]] [SUCCESS] Screenshot refreshed
[[11:16:56]] [INFO] Refreshing screenshot...
[[11:16:56]] [INFO] AJXVWhoBUt=pass
[[11:16:53]] [SUCCESS] Screenshot refreshed successfully
[[11:16:53]] [SUCCESS] Screenshot refreshed successfully
[[11:16:53]] [INFO] AJXVWhoBUt=running
[[11:16:53]] [INFO] Executing action 39/44: Action: ifThenSteps
[[11:16:52]] [SUCCESS] Screenshot refreshed
[[11:16:52]] [INFO] Refreshing screenshot...
[[11:16:52]] [INFO] yhmzeynQyu=pass
[[11:16:21]] [SUCCESS] Screenshot refreshed successfully
[[11:16:21]] [SUCCESS] Screenshot refreshed successfully
[[11:16:20]] [INFO] yhmzeynQyu=running
[[11:16:20]] [INFO] Executing action 38/44: Tap on Text: "Remove"
[[11:16:20]] [SUCCESS] Screenshot refreshed
[[11:16:20]] [INFO] Refreshing screenshot...
[[11:16:20]] [INFO] LCxISjrRBu=pass
[[11:16:17]] [SUCCESS] Screenshot refreshed successfully
[[11:16:17]] [SUCCESS] Screenshot refreshed successfully
[[11:16:17]] [INFO] LCxISjrRBu=running
[[11:16:17]] [INFO] Executing action 37/44: Action: ifThenSteps
[[11:16:16]] [SUCCESS] Screenshot refreshed
[[11:16:16]] [INFO] Refreshing screenshot...
[[11:16:16]] [INFO] F1olhgKhUt=pass
[[11:16:14]] [SUCCESS] Screenshot refreshed successfully
[[11:16:14]] [SUCCESS] Screenshot refreshed successfully
[[11:16:14]] [INFO] F1olhgKhUt=running
[[11:16:14]] [INFO] Executing action 36/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[11:16:13]] [SUCCESS] Screenshot refreshed
[[11:16:13]] [INFO] Refreshing screenshot...
[[11:16:13]] [INFO] lWIRxRm6HE=pass
[[11:16:10]] [SUCCESS] Screenshot refreshed successfully
[[11:16:10]] [SUCCESS] Screenshot refreshed successfully
[[11:16:09]] [INFO] lWIRxRm6HE=running
[[11:16:09]] [INFO] Executing action 35/44: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[11:16:09]] [SUCCESS] Screenshot refreshed
[[11:16:09]] [INFO] Refreshing screenshot...
[[11:16:09]] [INFO] uOt2cFGhGr=pass
[[11:16:05]] [INFO] uOt2cFGhGr=running
[[11:16:05]] [INFO] Executing action 34/44: Tap on element with xpath: //android.widget.Button[@text="Move to wishlist"]
[[11:16:05]] [INFO] bGqhW1Kciz=fail
[[11:16:05]] [ERROR] Action 33 failed: Unsupported action type: tapIfLocatorExists
[[11:16:04]] [SUCCESS] Screenshot refreshed successfully
[[11:16:04]] [SUCCESS] Screenshot refreshed successfully
[[11:16:04]] [INFO] bGqhW1Kciz=running
[[11:16:04]] [INFO] Executing action 33/44: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[11:16:03]] [SUCCESS] Screenshot refreshed
[[11:16:03]] [INFO] Refreshing screenshot...
[[11:16:03]] [INFO] F1olhgKhUt=pass
[[11:16:00]] [SUCCESS] Screenshot refreshed successfully
[[11:16:00]] [SUCCESS] Screenshot refreshed successfully
[[11:16:00]] [INFO] F1olhgKhUt=running
[[11:16:00]] [INFO] Executing action 32/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[11:16:00]] [SUCCESS] Screenshot refreshed
[[11:16:00]] [INFO] Refreshing screenshot...
[[11:16:00]] [INFO] yhmzeynQyu=pass
[[11:15:56]] [SUCCESS] Screenshot refreshed successfully
[[11:15:56]] [SUCCESS] Screenshot refreshed successfully
[[11:15:56]] [INFO] yhmzeynQyu=running
[[11:15:56]] [INFO] Executing action 31/44: Tap on Text: "Remove"
[[11:15:56]] [SUCCESS] Screenshot refreshed
[[11:15:56]] [INFO] Refreshing screenshot...
[[11:15:56]] [INFO] Q0fomJIDoQ=pass
[[11:15:53]] [SUCCESS] Screenshot refreshed successfully
[[11:15:53]] [SUCCESS] Screenshot refreshed successfully
[[11:15:53]] [INFO] Q0fomJIDoQ=running
[[11:15:53]] [INFO] Executing action 30/44: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[11:15:52]] [SUCCESS] Screenshot refreshed
[[11:15:52]] [INFO] Refreshing screenshot...
[[11:15:52]] [INFO] y4i304JeJj=pass
[[11:15:11]] [SUCCESS] Screenshot refreshed successfully
[[11:15:11]] [SUCCESS] Screenshot refreshed successfully
[[11:15:11]] [INFO] y4i304JeJj=running
[[11:15:11]] [INFO] Executing action 29/44: Tap on Text: "Move"
[[11:15:10]] [SUCCESS] Screenshot refreshed
[[11:15:10]] [INFO] Refreshing screenshot...
[[11:15:10]] [INFO] Q0fomJIDoQ=pass
[[11:15:08]] [SUCCESS] Screenshot refreshed successfully
[[11:15:08]] [SUCCESS] Screenshot refreshed successfully
[[11:15:07]] [INFO] Q0fomJIDoQ=running
[[11:15:07]] [INFO] Executing action 28/44: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[11:15:07]] [SUCCESS] Screenshot refreshed
[[11:15:07]] [INFO] Refreshing screenshot...
[[11:15:07]] [INFO] F1olhgKhUt=pass
[[11:15:05]] [SUCCESS] Screenshot refreshed successfully
[[11:15:05]] [SUCCESS] Screenshot refreshed successfully
[[11:15:04]] [INFO] F1olhgKhUt=running
[[11:15:04]] [INFO] Executing action 27/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[11:15:04]] [SUCCESS] Screenshot refreshed
[[11:15:04]] [INFO] Refreshing screenshot...
[[11:15:04]] [INFO] WbxRVpWtjw=pass
[[11:15:02]] [SUCCESS] Screenshot refreshed successfully
[[11:15:02]] [SUCCESS] Screenshot refreshed successfully
[[11:15:01]] [INFO] WbxRVpWtjw=running
[[11:15:01]] [INFO] Executing action 26/44: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[11:15:01]] [SUCCESS] Screenshot refreshed
[[11:15:01]] [INFO] Refreshing screenshot...
[[11:15:01]] [INFO] H3IAmq3r3i=pass
[[11:14:55]] [SUCCESS] Screenshot refreshed successfully
[[11:14:55]] [SUCCESS] Screenshot refreshed successfully
[[11:14:55]] [INFO] H3IAmq3r3i=running
[[11:14:55]] [INFO] Executing action 25/44: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[11:14:54]] [SUCCESS] Screenshot refreshed
[[11:14:54]] [INFO] Refreshing screenshot...
[[11:14:54]] [INFO] eLxHVWKeDQ=pass
[[11:14:52]] [SUCCESS] Screenshot refreshed successfully
[[11:14:52]] [SUCCESS] Screenshot refreshed successfully
[[11:14:51]] [INFO] eLxHVWKeDQ=running
[[11:14:51]] [INFO] Executing action 24/44: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[11:14:51]] [SUCCESS] Screenshot refreshed
[[11:14:51]] [INFO] Refreshing screenshot...
[[11:14:51]] [INFO] nAB6Q8LAdv=pass
[[11:14:40]] [SUCCESS] Screenshot refreshed successfully
[[11:14:40]] [SUCCESS] Screenshot refreshed successfully
[[11:14:39]] [SUCCESS] Screenshot refreshed
[[11:14:39]] [INFO] Refreshing screenshot...
[[11:14:08]] [SUCCESS] Screenshot refreshed successfully
[[11:14:08]] [SUCCESS] Screenshot refreshed successfully
[[11:14:07]] [INFO] nAB6Q8LAdv=running
[[11:14:07]] [INFO] Executing action 23/44: Wait till xpath=//android.widget.Button[@text="Filter"]
[[11:14:07]] [SUCCESS] Screenshot refreshed
[[11:14:07]] [INFO] Refreshing screenshot...
[[11:14:07]] [INFO] H3IAmq3r3i=pass
[[11:14:05]] [SUCCESS] Screenshot refreshed successfully
[[11:14:05]] [SUCCESS] Screenshot refreshed successfully
[[11:14:04]] [INFO] H3IAmq3r3i=running
[[11:14:04]] [INFO] Executing action 22/44: Input text: "P_42999157"
[[11:14:04]] [SUCCESS] Screenshot refreshed
[[11:14:04]] [INFO] Refreshing screenshot...
[[11:14:04]] [INFO] wzxrm7WwXv=pass
[[11:13:54]] [SUCCESS] Screenshot refreshed successfully
[[11:13:54]] [SUCCESS] Screenshot refreshed successfully
[[11:13:53]] [INFO] wzxrm7WwXv=running
[[11:13:53]] [INFO] Executing action 21/44: Tap on image: search-glassimage-android.png
[[11:13:53]] [SUCCESS] Screenshot refreshed
[[11:13:53]] [INFO] Refreshing screenshot...
[[11:13:53]] [INFO] F1olhgKhUt=pass
[[11:13:12]] [SUCCESS] Screenshot refreshed successfully
[[11:13:12]] [SUCCESS] Screenshot refreshed successfully
[[11:13:12]] [INFO] F1olhgKhUt=running
[[11:13:12]] [INFO] Executing action 20/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[11:13:11]] [SUCCESS] Screenshot refreshed
[[11:13:11]] [INFO] Refreshing screenshot...
[[11:13:11]] [INFO] SxKlojIFRq=pass
[[11:13:09]] [SUCCESS] Screenshot refreshed successfully
[[11:13:09]] [SUCCESS] Screenshot refreshed successfully
[[11:13:09]] [INFO] SxKlojIFRq=running
[[11:13:09]] [INFO] Executing action 19/44: Android Function: send_key_event - Key Event: BACK
[[11:13:08]] [SUCCESS] Screenshot refreshed
[[11:13:08]] [INFO] Refreshing screenshot...
[[11:13:08]] [INFO] WbxRVpWtjw=pass
[[11:13:06]] [SUCCESS] Screenshot refreshed successfully
[[11:13:06]] [SUCCESS] Screenshot refreshed successfully
[[11:13:06]] [INFO] WbxRVpWtjw=running
[[11:13:06]] [INFO] Executing action 18/44: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[11:13:05]] [SUCCESS] Screenshot refreshed
[[11:13:05]] [INFO] Refreshing screenshot...
[[11:13:05]] [INFO] H3IAmq3r3i=pass
[[11:13:01]] [SUCCESS] Screenshot refreshed successfully
[[11:13:01]] [SUCCESS] Screenshot refreshed successfully
[[11:13:01]] [INFO] H3IAmq3r3i=running
[[11:13:01]] [INFO] Executing action 17/44: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[11:13:01]] [SUCCESS] Screenshot refreshed
[[11:13:01]] [INFO] Refreshing screenshot...
[[11:13:01]] [INFO] ITHvSyXXmu=pass
[[11:12:57]] [SUCCESS] Screenshot refreshed successfully
[[11:12:57]] [SUCCESS] Screenshot refreshed successfully
[[11:12:57]] [INFO] ITHvSyXXmu=running
[[11:12:57]] [INFO] Executing action 16/44: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[11:12:56]] [SUCCESS] Screenshot refreshed
[[11:12:56]] [INFO] Refreshing screenshot...
[[11:12:56]] [INFO] WbxRVpWtjw=pass
[[11:12:54]] [SUCCESS] Screenshot refreshed successfully
[[11:12:54]] [SUCCESS] Screenshot refreshed successfully
[[11:12:54]] [INFO] WbxRVpWtjw=running
[[11:12:54]] [INFO] Executing action 15/44: Tap on element with xpath: //android.widget.ListView[contains(@resource-id,"swiper-wrapper")]/android.view.View[1]//android.widget.TextView
[[11:12:53]] [SUCCESS] Screenshot refreshed
[[11:12:53]] [INFO] Refreshing screenshot...
[[11:12:53]] [INFO] H3IAmq3r3i=pass
[[11:12:24]] [SUCCESS] Screenshot refreshed successfully
[[11:12:24]] [SUCCESS] Screenshot refreshed successfully
[[11:12:24]] [INFO] H3IAmq3r3i=running
[[11:12:24]] [INFO] Executing action 14/44: Swipe up till element xpath: "//android.widget.ListView[contains(@resource-id,"swiper-wrapper")]/android.view.View[1]//android.widget.TextView" is visible
[[11:12:23]] [SUCCESS] Screenshot refreshed
[[11:12:23]] [INFO] Refreshing screenshot...
[[11:12:23]] [INFO] Ob26qqcA0p=pass
[[11:12:18]] [SUCCESS] Screenshot refreshed successfully
[[11:12:18]] [SUCCESS] Screenshot refreshed successfully
[[11:12:18]] [INFO] Ob26qqcA0p=running
[[11:12:18]] [INFO] Executing action 13/44: Swipe from (50%, 70%) to (50%, 30%)
[[11:12:17]] [SUCCESS] Screenshot refreshed
[[11:12:17]] [INFO] Refreshing screenshot...
[[11:12:17]] [INFO] WbxRVpWtjw=pass
[[11:12:15]] [SUCCESS] Screenshot refreshed successfully
[[11:12:15]] [SUCCESS] Screenshot refreshed successfully
[[11:12:15]] [INFO] WbxRVpWtjw=running
[[11:12:15]] [INFO] Executing action 12/44: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[11:12:14]] [SUCCESS] Screenshot refreshed
[[11:12:14]] [INFO] Refreshing screenshot...
[[11:12:14]] [INFO] H3IAmq3r3i=pass
[[11:12:08]] [SUCCESS] Screenshot refreshed successfully
[[11:12:08]] [SUCCESS] Screenshot refreshed successfully
[[11:12:08]] [INFO] H3IAmq3r3i=running
[[11:12:08]] [INFO] Executing action 11/44: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[11:12:08]] [SUCCESS] Screenshot refreshed
[[11:12:08]] [INFO] Refreshing screenshot...
[[11:12:08]] [INFO] ITHvSyXXmu=pass
[[11:12:03]] [SUCCESS] Screenshot refreshed successfully
[[11:12:03]] [SUCCESS] Screenshot refreshed successfully
[[11:12:03]] [INFO] ITHvSyXXmu=running
[[11:12:03]] [INFO] Executing action 10/44: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[11:12:03]] [SUCCESS] Screenshot refreshed
[[11:12:03]] [INFO] Refreshing screenshot...
[[11:12:03]] [INFO] eLxHVWKeDQ=pass
[[11:11:58]] [SUCCESS] Screenshot refreshed successfully
[[11:11:58]] [SUCCESS] Screenshot refreshed successfully
[[11:11:57]] [INFO] eLxHVWKeDQ=running
[[11:11:57]] [INFO] Executing action 9/44: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[11:11:57]] [SUCCESS] Screenshot refreshed
[[11:11:57]] [INFO] Refreshing screenshot...
[[11:11:57]] [INFO] nAB6Q8LAdv=pass
[[11:11:49]] [SUCCESS] Screenshot refreshed successfully
[[11:11:49]] [SUCCESS] Screenshot refreshed successfully
[[11:11:49]] [INFO] nAB6Q8LAdv=running
[[11:11:49]] [INFO] Executing action 8/44: Wait till xpath=//android.widget.Button[@text="Filter"]
[[11:11:48]] [SUCCESS] Screenshot refreshed
[[11:11:48]] [INFO] Refreshing screenshot...
[[11:11:48]] [INFO] sc2KH9bG6H=pass
[[11:11:47]] [SUCCESS] Screenshot refreshed successfully
[[11:11:47]] [SUCCESS] Screenshot refreshed successfully
[[11:11:46]] [INFO] sc2KH9bG6H=running
[[11:11:46]] [INFO] Executing action 7/44: Input text: "Uno card"
[[11:11:46]] [SUCCESS] Screenshot refreshed
[[11:11:46]] [INFO] Refreshing screenshot...
[[11:11:46]] [INFO] rqLJpAP0mA=pass
[[11:11:41]] [SUCCESS] Screenshot refreshed successfully
[[11:11:41]] [SUCCESS] Screenshot refreshed successfully
[[11:11:40]] [INFO] rqLJpAP0mA=running
[[11:11:40]] [INFO] Executing action 6/44: Tap on Text: "Find"
[[11:11:39]] [SUCCESS] Screenshot refreshed
[[11:11:39]] [INFO] Refreshing screenshot...
[[11:11:39]] [INFO] yiKyF5FJwN=pass
[[11:11:37]] [SUCCESS] Screenshot refreshed successfully
[[11:11:37]] [SUCCESS] Screenshot refreshed successfully
[[11:11:33]] [INFO] yiKyF5FJwN=running
[[11:11:33]] [INFO] Executing action 5/44: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[11:11:33]] [SUCCESS] Screenshot refreshed
[[11:11:33]] [INFO] Refreshing screenshot...
[[11:11:32]] [SUCCESS] Screenshot refreshed
[[11:11:32]] [INFO] Refreshing screenshot...
[[11:11:30]] [SUCCESS] Screenshot refreshed successfully
[[11:11:30]] [SUCCESS] Screenshot refreshed successfully
[[11:11:30]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[11:11:29]] [SUCCESS] Screenshot refreshed
[[11:11:29]] [INFO] Refreshing screenshot...
[[11:11:14]] [SUCCESS] Screenshot refreshed successfully
[[11:11:14]] [SUCCESS] Screenshot refreshed successfully
[[11:11:14]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[11:11:14]] [SUCCESS] Screenshot refreshed
[[11:11:14]] [INFO] Refreshing screenshot...
[[11:11:11]] [SUCCESS] Screenshot refreshed successfully
[[11:11:11]] [SUCCESS] Screenshot refreshed successfully
[[11:11:11]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[11:11:10]] [SUCCESS] Screenshot refreshed
[[11:11:10]] [INFO] Refreshing screenshot...
[[11:11:08]] [SUCCESS] Screenshot refreshed successfully
[[11:11:08]] [SUCCESS] Screenshot refreshed successfully
[[11:11:08]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[11:11:08]] [SUCCESS] Screenshot refreshed
[[11:11:08]] [INFO] Refreshing screenshot...
[[11:11:06]] [SUCCESS] Screenshot refreshed successfully
[[11:11:06]] [SUCCESS] Screenshot refreshed successfully
[[11:11:05]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[11:11:05]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[11:11:05]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[11:11:05]] [INFO] rUH3kvaEH9=running
[[11:11:05]] [INFO] Executing action 4/44: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[11:11:05]] [SUCCESS] Screenshot refreshed
[[11:11:05]] [INFO] Refreshing screenshot...
[[11:11:05]] [INFO] rkL0oz4kiL=pass
[[11:11:00]] [SUCCESS] Screenshot refreshed successfully
[[11:11:00]] [SUCCESS] Screenshot refreshed successfully
[[11:11:00]] [INFO] rkL0oz4kiL=running
[[11:11:00]] [INFO] Executing action 3/44: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[11:11:00]] [SUCCESS] Screenshot refreshed
[[11:11:00]] [INFO] Refreshing screenshot...
[[11:11:00]] [INFO] HotUJOd6oB=pass
[[11:10:58]] [SUCCESS] Screenshot refreshed successfully
[[11:10:58]] [SUCCESS] Screenshot refreshed successfully
[[11:10:57]] [INFO] HotUJOd6oB=running
[[11:10:57]] [INFO] Executing action 2/44: Launch app: au.com.kmart
[[11:10:57]] [SUCCESS] Screenshot refreshed
[[11:10:57]] [INFO] Refreshing screenshot...
[[11:10:57]] [INFO] HotUJOd6oB=pass
[[11:10:53]] [INFO] HotUJOd6oB=running
[[11:10:53]] [INFO] Executing action 1/44: Terminate app: au.com.kmart
[[11:10:53]] [INFO] ExecutionManager: Starting execution of 44 actions...
[[11:10:53]] [SUCCESS] Cleared 0 screenshots from database
[[11:10:53]] [INFO] Clearing screenshots from database before execution...
[[11:10:53]] [SUCCESS] All screenshots deleted successfully
[[11:10:53]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[11:10:53]] [INFO] Skipping report initialization - single test case execution
[[11:10:49]] [SUCCESS] All screenshots deleted successfully
[[11:10:49]] [SUCCESS] Loaded test case "WishList_AU-Android" with 44 actions
[[11:10:49]] [SUCCESS] Added action: ifThenSteps
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: swipe
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tapOnText
[[11:10:49]] [SUCCESS] Added action: ifThenSteps
[[11:10:49]] [SUCCESS] Added action: tapOnText
[[11:10:49]] [SUCCESS] Added action: ifThenSteps
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tapIfLocatorExists
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tapOnText
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tapOnText
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: swipeTillVisible
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: waitTill
[[11:10:49]] [SUCCESS] Added action: text
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: androidFunctions
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: swipeTillVisible
[[11:10:49]] [SUCCESS] Added action: waitTill
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: swipeTillVisible
[[11:10:49]] [SUCCESS] Added action: swipe
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: swipeTillVisible
[[11:10:49]] [SUCCESS] Added action: waitTill
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: waitTill
[[11:10:49]] [SUCCESS] Added action: text
[[11:10:49]] [SUCCESS] Added action: tapOnText
[[11:10:49]] [SUCCESS] Added action: exists
[[11:10:49]] [SUCCESS] Added action: multiStep
[[11:10:49]] [SUCCESS] Added action: tap
[[11:10:49]] [SUCCESS] Added action: launchApp
[[11:10:49]] [SUCCESS] Added action: terminateApp
[[11:10:49]] [INFO] All actions cleared
[[11:10:49]] [INFO] Cleaning up screenshots...
[[11:10:40]] [SUCCESS] Screenshot refreshed successfully
[[11:10:39]] [SUCCESS] Screenshot refreshed
[[11:10:39]] [INFO] Refreshing screenshot...
[[11:10:38]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[11:10:38]] [INFO] Device info updated: RMX2151
[[11:10:32]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[11:10:29]] [SUCCESS] Found 1 device(s)
[[11:10:29]] [INFO] Refreshing device list...
