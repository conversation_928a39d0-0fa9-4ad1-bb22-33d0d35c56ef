{"name": "UI Execution 26/07/2025, 08:07:51", "testCases": [{"name": "Browse & PDP\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            56 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3517ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2615ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1806ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2344ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1507ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1505ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2594ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2823ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "2641ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2075ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2643ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "status": "passed", "duration": "2369ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Show (\")]", "status": "passed", "duration": "2394ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1694ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "2674ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3144ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10016ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2197ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2762ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "2669ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "2225ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2942ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Wait for 3 ms", "status": "passed", "duration": "3013ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Swipe up till element accessibility_id: \"Learn more about AfterPay\" is visible", "status": "passed", "duration": "7677ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Learn more about AfterPay", "status": "passed", "duration": "5576ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]\" exists", "status": "unknown", "duration": "13773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2253ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Tap on element with accessibility_id: Learn more about Zip", "status": "passed", "duration": "5623ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"What is Zip?\"]\" exists", "status": "passed", "duration": "1541ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2415ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on element with accessibility_id: Learn more about PayPal Pay in 4", "status": "passed", "duration": "5661ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[paypal-close-img]", "status": "passed", "duration": "2547ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "status": "passed", "duration": "3223ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]\" exists", "status": "passed", "duration": "1478ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2206ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3262ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2313ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2310ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Kid toy\"", "status": "passed", "duration": "2650ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2140ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2720ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2706ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2353ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"mat\"", "status": "passed", "duration": "2553ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2134ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2722ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2736ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "16207ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3514ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11138ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "5764ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2754ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2686ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1057ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Delivery & CNC\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            32 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2191ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5421ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1160ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2174ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "12580ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3507ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2698ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2065ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add", "status": "passed", "duration": "4959ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1705ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2681ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11184ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Click & Collect\"]\" exists", "status": "passed", "duration": "1751ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2764ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3147ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Tap on Text: \"Tarneit\"", "status": "passed", "duration": "3021ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10014ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2513ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2663ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11268ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2792ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10018ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "6725ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2790ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2260ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2682ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5492ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2427ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2548ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery  Buy (36 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Sign ins\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            96 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3295ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5425ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1222ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2765ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2767ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3318ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2743ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3059ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "2229ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2581ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5469ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2531ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2572ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "2318ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1206ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2400ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2774ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3302ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2714ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3063ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2398ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "2037ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2608ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5500ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3218ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3458ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2654ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2113ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2697ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2653ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeStaticText[@name=\"Already a member?\"]\" is visible", "status": "passed", "duration": "15932ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3519ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1218ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2356ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2938ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname1]\"", "status": "passed", "duration": "3428ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2889ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3195ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "5366ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3256ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5473ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2413ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3224ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2618ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2319ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1191ms", "action_id": "screenshot_20250726_072437", "screenshot_filename": "screenshot_20250726_072437.png", "report_screenshot": "screenshot_20250726_072437.png", "resolved_screenshot": "screenshots/screenshot_20250726_072437.png", "clean_action_id": "screenshot_20250726_072437", "prefixed_action_id": "al_screenshot_20250726_072437", "action_id_screenshot": "screenshots/screenshot_20250726_072437.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_074720", "screenshot_filename": "screenshot_20250726_074720.png", "report_screenshot": "screenshot_20250726_074720.png", "resolved_screenshot": "screenshots/screenshot_20250726_074720.png", "clean_action_id": "screenshot_20250726_074720", "prefixed_action_id": "al_screenshot_20250726_074720", "action_id_screenshot": "screenshots/screenshot_20250726_074720.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3851ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5498ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2562ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3659ms", "action_id": "screenshot_20250726_070552", "screenshot_filename": "screenshot_20250726_070552.png", "report_screenshot": "screenshot_20250726_070552.png", "resolved_screenshot": "screenshots/screenshot_20250726_070552.png", "clean_action_id": "screenshot_20250726_070552", "prefixed_action_id": "al_screenshot_20250726_070552", "action_id_screenshot": "screenshots/screenshot_20250726_070552.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2699ms", "action_id": "screenshot_20250726_072423", "screenshot_filename": "screenshot_20250726_072423.png", "report_screenshot": "screenshot_20250726_072423.png", "resolved_screenshot": "screenshots/screenshot_20250726_072423.png", "clean_action_id": "screenshot_20250726_072423", "prefixed_action_id": "al_screenshot_20250726_072423", "action_id_screenshot": "screenshots/screenshot_20250726_072423.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2089ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2757ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibility_id: \"Add to bag\" is visible", "status": "passed", "duration": "7115ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "6431ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "screenshot_20250726_070234", "screenshot_filename": "screenshot_20250726_070234.png", "report_screenshot": "screenshot_20250726_070234.png", "resolved_screenshot": "screenshots/screenshot_20250726_070234.png", "clean_action_id": "screenshot_20250726_070234", "prefixed_action_id": "al_screenshot_20250726_070234", "action_id_screenshot": "screenshots/screenshot_20250726_070234.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3260ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11153ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2127ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2669ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "7269ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "5009ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3035ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1181ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2807ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3355ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2784ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3073ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11147ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2193ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2756ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2727ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2287ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3366ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5486ms", "action_id": "screenshot_20250726_080058", "screenshot_filename": "screenshot_20250726_080058.png", "report_screenshot": "screenshot_20250726_080058.png", "resolved_screenshot": "screenshots/screenshot_20250726_080058.png", "clean_action_id": "screenshot_20250726_080058", "prefixed_action_id": "al_screenshot_20250726_080058", "action_id_screenshot": "screenshots/screenshot_20250726_080058.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2445ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3220ms", "action_id": "screenshot_20250726_073846", "screenshot_filename": "screenshot_20250726_073846.png", "report_screenshot": "screenshot_20250726_073846.png", "resolved_screenshot": "screenshots/screenshot_20250726_073846.png", "clean_action_id": "screenshot_20250726_073846", "prefixed_action_id": "al_screenshot_20250726_073846", "action_id_screenshot": "screenshots/screenshot_20250726_073846.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2450ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_065956", "screenshot_filename": "screenshot_20250726_065956.png", "report_screenshot": "screenshot_20250726_065956.png", "resolved_screenshot": "screenshots/screenshot_20250726_065956.png", "clean_action_id": "screenshot_20250726_065956", "prefixed_action_id": "al_screenshot_20250726_065956", "action_id_screenshot": "screenshots/screenshot_20250726_065956.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "6917ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4569ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "screenshot_20250726_071923", "screenshot_filename": "screenshot_20250726_071923.png", "report_screenshot": "screenshot_20250726_071923.png", "resolved_screenshot": "screenshots/screenshot_20250726_071923.png", "clean_action_id": "screenshot_20250726_071923", "prefixed_action_id": "al_screenshot_20250726_071923", "action_id_screenshot": "screenshots/screenshot_20250726_071923.png"}, {"name": "Tap on Text: \"in\"", "status": "passed", "duration": "2880ms", "action_id": "screenshot_20250726_073852", "screenshot_filename": "screenshot_20250726_073852.png", "report_screenshot": "screenshot_20250726_073852.png", "resolved_screenshot": "screenshots/screenshot_20250726_073852.png", "clean_action_id": "screenshot_20250726_073852", "prefixed_action_id": "al_screenshot_20250726_073852", "action_id_screenshot": "screenshots/screenshot_20250726_073852.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1168ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_071710", "screenshot_filename": "screenshot_20250726_071710.png", "report_screenshot": "screenshot_20250726_071710.png", "resolved_screenshot": "screenshots/screenshot_20250726_071710.png", "clean_action_id": "screenshot_20250726_071710", "prefixed_action_id": "al_screenshot_20250726_071710", "action_id_screenshot": "screenshots/screenshot_20250726_071710.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10788ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2567ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2554ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2302ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2676ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5453ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2424ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "WishList\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2187ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5411ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1205ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2197ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2785ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3288ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2721ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3079ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3511ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2860ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2165ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2693ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2681ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "6064ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "3258ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "15338ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2744ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "6044ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "3275ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3218ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3712ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "iOS Function: text - Text: \"P_43386093\"", "status": "passed", "duration": "2655ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2434ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2839ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "6133ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "3222ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "3246ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1623ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2353ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "2563ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2369ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2515ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "passed", "duration": "2399ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2683ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3218ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11291ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3174ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "2225ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "2775ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2316ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "3232ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "ifThenSteps action", "status": "passed", "duration": "4300ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2673ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "ifThenSteps action", "status": "passed", "duration": "4297ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2459ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2438ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5462ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2399ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Kmart-Prod-Signin\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON>try\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            56 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2190ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5390ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1200ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2668ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2789ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3329ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2768ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3082ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2842ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2589ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5582ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2451ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2143ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4481ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1196ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2487ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"OnePass\"", "status": "passed", "duration": "2881ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2792ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3391ms", "action_id": "kiransah<PERSON>", "screenshot_filename": "kiransahoo.png", "report_screenshot": "kiransahoo.png", "resolved_screenshot": "screenshots/kiransahoo.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "2922ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@6\"", "status": "passed", "duration": "3195ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2819ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2571ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4331ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2439ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2138ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4454ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1176ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2242ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3101ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "2948ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10016ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "1630ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1660ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1553ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1604ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1564ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1626ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1584ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "5505ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2554ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5501ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2432ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2127ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4493ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1217ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2784ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Sign in with Google\"]\" is visible", "status": "passed", "duration": "5286ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign in with Google\"]", "status": "passed", "duration": "2807ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "2949ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2981ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2560ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5599ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2465ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1042ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            62 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2189ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5019ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5658ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1184ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2667ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1912ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2699ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5017ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2525ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible", "status": "passed", "duration": "14556ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "2510ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "4515ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "3082ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2325ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5017ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2509ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]\" exists", "status": "passed", "duration": "1561ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "status": "passed", "duration": "2512ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]\" exists", "status": "passed", "duration": "1560ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2290ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "11417ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5017ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "2922ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2530ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2473ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "2460ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "2752ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2300ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "2732ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2315ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "2808ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2291ms", "action_id": "screenshot_20250726_072437", "screenshot_filename": "screenshot_20250726_072437.png", "report_screenshot": "screenshot_20250726_072437.png", "resolved_screenshot": "screenshots/screenshot_20250726_072437.png", "clean_action_id": "screenshot_20250726_072437", "prefixed_action_id": "al_screenshot_20250726_072437", "action_id_screenshot": "screenshots/screenshot_20250726_072437.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2376ms", "action_id": "screenshot_20250726_074720", "screenshot_filename": "screenshot_20250726_074720.png", "report_screenshot": "screenshot_20250726_074720.png", "resolved_screenshot": "screenshots/screenshot_20250726_074720.png", "clean_action_id": "screenshot_20250726_074720", "prefixed_action_id": "al_screenshot_20250726_074720", "action_id_screenshot": "screenshots/screenshot_20250726_074720.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2684ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "passed", "duration": "3246ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "passed", "duration": "4219ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "passed", "duration": "4219ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "passed", "duration": "4212ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2814ms", "action_id": "screenshot_20250726_070552", "screenshot_filename": "screenshot_20250726_070552.png", "report_screenshot": "screenshot_20250726_070552.png", "resolved_screenshot": "screenshots/screenshot_20250726_070552.png", "clean_action_id": "screenshot_20250726_070552", "prefixed_action_id": "al_screenshot_20250726_070552", "action_id_screenshot": "screenshots/screenshot_20250726_070552.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "passed", "duration": "4255ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Flybuys barcode number", "status": "passed", "duration": "4258ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Input text: \"2791234567890\"", "status": "passed", "duration": "1705ms", "action_id": "2791234567", "screenshot_filename": "2791234567.png", "report_screenshot": "2791234567.png", "resolved_screenshot": "screenshots/2791234567.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4429ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "passed", "duration": "4241ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2345ms", "action_id": "screenshot_20250726_072423", "screenshot_filename": "screenshot_20250726_072423.png", "report_screenshot": "screenshot_20250726_072423.png", "resolved_screenshot": "screenshots/screenshot_20250726_072423.png", "clean_action_id": "screenshot_20250726_072423", "prefixed_action_id": "al_screenshot_20250726_072423", "action_id_screenshot": "screenshots/screenshot_20250726_072423.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "passed", "duration": "2065ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5486ms", "action_id": "screenshot_20250726_070234", "screenshot_filename": "screenshot_20250726_070234.png", "report_screenshot": "screenshot_20250726_070234.png", "resolved_screenshot": "screenshots/screenshot_20250726_070234.png", "clean_action_id": "screenshot_20250726_070234", "prefixed_action_id": "al_screenshot_20250726_070234", "action_id_screenshot": "screenshots/screenshot_20250726_070234.png"}, {"name": "Tap on Text: \"locator\"", "status": "passed", "duration": "2594ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3257ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap and Type at (env[store-locator-x], env[store-locator-y]): \"env[store-locator-postcode]\"", "status": "passed", "duration": "5334ms", "action_id": "screenshot_20250726_080058", "screenshot_filename": "screenshot_20250726_080058.png", "report_screenshot": "screenshot_20250726_080058.png", "resolved_screenshot": "screenshots/screenshot_20250726_080058.png", "clean_action_id": "screenshot_20250726_080058", "prefixed_action_id": "al_screenshot_20250726_080058", "action_id_screenshot": "screenshots/screenshot_20250726_080058.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3218ms", "action_id": "screenshot_20250726_073846", "screenshot_filename": "screenshot_20250726_073846.png", "report_screenshot": "screenshot_20250726_073846.png", "resolved_screenshot": "screenshots/screenshot_20250726_073846.png", "clean_action_id": "screenshot_20250726_073846", "prefixed_action_id": "al_screenshot_20250726_073846", "action_id_screenshot": "screenshots/screenshot_20250726_073846.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]\" exists", "status": "passed", "duration": "1743ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2319ms", "action_id": "screenshot_20250726_065956", "screenshot_filename": "screenshot_20250726_065956.png", "report_screenshot": "screenshot_20250726_065956.png", "resolved_screenshot": "screenshots/screenshot_20250726_065956.png", "clean_action_id": "screenshot_20250726_065956", "prefixed_action_id": "al_screenshot_20250726_065956", "action_id_screenshot": "screenshots/screenshot_20250726_065956.png"}, {"name": "Tap on Text: \"Invite\"", "status": "passed", "duration": "2650ms", "action_id": "screenshot_20250726_071923", "screenshot_filename": "screenshot_20250726_071923.png", "report_screenshot": "screenshot_20250726_071923.png", "resolved_screenshot": "screenshots/screenshot_20250726_071923.png", "clean_action_id": "screenshot_20250726_071923", "prefixed_action_id": "al_screenshot_20250726_071923", "action_id_screenshot": "screenshots/screenshot_20250726_071923.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]\" exists", "status": "passed", "duration": "1828ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2864ms", "action_id": "screenshot_20250726_073852", "screenshot_filename": "screenshot_20250726_073852.png", "report_screenshot": "screenshot_20250726_073852.png", "resolved_screenshot": "screenshots/screenshot_20250726_073852.png", "clean_action_id": "screenshot_20250726_073852", "prefixed_action_id": "al_screenshot_20250726_073852", "action_id_screenshot": "screenshots/screenshot_20250726_073852.png"}, {"name": "Tap on Text: \"Customer\"", "status": "passed", "duration": "2607ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2280ms", "action_id": "screenshot_20250726_071710", "screenshot_filename": "screenshot_20250726_071710.png", "report_screenshot": "screenshot_20250726_071710.png", "resolved_screenshot": "screenshots/screenshot_20250726_071710.png", "clean_action_id": "screenshot_20250726_071710", "prefixed_action_id": "al_screenshot_20250726_071710", "action_id_screenshot": "screenshots/screenshot_20250726_071710.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5485ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2610ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Others\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            43 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2187ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "status": "passed", "duration": "2471ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "482ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "1379ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"imgHelp\"]\" exists", "status": "passed", "duration": "1378ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]\" exists", "status": "passed", "duration": "1416ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2649ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2562ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "status": "passed", "duration": "2379ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Order number\"]", "status": "passed", "duration": "2353ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[searchorder]\"", "status": "passed", "duration": "1647ms", "action_id": "searchorde", "screenshot_filename": "searchorde.png", "report_screenshot": "searchorde.png", "resolved_screenshot": "screenshots/searchorde.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email Address\"] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2493ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"<EMAIL>\"", "status": "passed", "duration": "1850ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search for order\"]", "status": "passed", "duration": "2524ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"refunded\"]\" exists", "status": "passed", "duration": "1536ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2279ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2404ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1213ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "3609ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3311ms", "action_id": "kiransah<PERSON>", "screenshot_filename": "kiransahoo.png", "report_screenshot": "kiransahoo.png", "resolved_screenshot": "screenshots/kiransahoo.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2788ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3101ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]\" exists", "status": "passed", "duration": "2825ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "status": "passed", "duration": "2493ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]\" exists", "status": "passed", "duration": "1456ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2304ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2688ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "2834ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]\" exists", "status": "passed", "duration": "1579ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2534ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"<PERSON>ly<PERSON> Receipt\"]/XCUIElementTypeOther[2]\" exists", "status": "passed", "duration": "2026ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "3165ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3275ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "2889ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2590ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"btnUpdate\"]\"", "status": "passed", "duration": "10826ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2332ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Save my location\"]\"", "status": "passed", "duration": "10248ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2034ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2458ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1910ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1043ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23226ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Postcode Flow\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            52 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2182ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5508ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1213ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2580ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2178ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2572ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4309ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3371ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "2917ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3315ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2905ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Tap if locator exists: accessibility_id=\"btnUpdate\"", "status": "passed", "duration": "5442ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3477ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2694ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2091ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3298ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till accessibility_id=btnCurrentLocationButton", "status": "passed", "duration": "3345ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2787ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3339ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2828ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Check if element with text=\"Tarneit\" exists", "status": "passed", "duration": "13310ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2170ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2717ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2733ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3515ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4298ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3509ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "2985ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3328ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2899ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Check if element with text=\"Broadway\" exists", "status": "passed", "duration": "13483ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "15700ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "2147ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3279ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11158ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2756ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "passed", "duration": "2191ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3338ms", "action_id": "screenshot_20250726_072437", "screenshot_filename": "screenshot_20250726_072437.png", "report_screenshot": "screenshot_20250726_072437.png", "resolved_screenshot": "screenshots/screenshot_20250726_072437.png", "clean_action_id": "screenshot_20250726_072437", "prefixed_action_id": "al_screenshot_20250726_072437", "action_id_screenshot": "screenshots/screenshot_20250726_072437.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "passed", "duration": "5400ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"3000\"", "status": "passed", "duration": "5394ms", "action_id": "screenshot_20250726_074720", "screenshot_filename": "screenshot_20250726_074720.png", "report_screenshot": "screenshot_20250726_074720.png", "resolved_screenshot": "screenshots/screenshot_20250726_074720.png", "clean_action_id": "screenshot_20250726_074720", "prefixed_action_id": "al_screenshot_20250726_074720", "action_id_screenshot": "screenshots/screenshot_20250726_074720.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3325ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "5341ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3167ms", "action_id": "screenshot_20250726_070552", "screenshot_filename": "screenshot_20250726_070552.png", "report_screenshot": "screenshot_20250726_070552.png", "resolved_screenshot": "screenshots/screenshot_20250726_070552.png", "clean_action_id": "screenshot_20250726_070552", "prefixed_action_id": "al_screenshot_20250726_070552", "action_id_screenshot": "screenshots/screenshot_20250726_070552.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2797ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "1868ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "2404ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with text=\"Melbourne\" exists", "status": "passed", "duration": "16171ms", "action_id": "screenshot_20250726_072423", "screenshot_filename": "screenshot_20250726_072423.png", "report_screenshot": "screenshot_20250726_072423.png", "resolved_screenshot": "screenshots/screenshot_20250726_072423.png", "clean_action_id": "screenshot_20250726_072423", "prefixed_action_id": "al_screenshot_20250726_072423", "action_id_screenshot": "screenshots/screenshot_20250726_072423.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3314ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5492ms", "action_id": "screenshot_20250726_070234", "screenshot_filename": "screenshot_20250726_070234.png", "report_screenshot": "screenshot_20250726_070234.png", "resolved_screenshot": "screenshots/screenshot_20250726_070234.png", "clean_action_id": "screenshot_20250726_070234", "prefixed_action_id": "al_screenshot_20250726_070234", "action_id_screenshot": "screenshots/screenshot_20250726_070234.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2446ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "App Settings AU\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            68 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2190ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "2486ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1188ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "1224ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1252ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2765ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5024ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "975ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5015ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3286ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "287ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "243ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "576ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "254ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "697ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "199ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "130ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5015ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "810ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3278ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2609ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2807ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2605ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "3273ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1157ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"kmart au\"", "status": "passed", "duration": "1699ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "1895ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists", "status": "passed", "duration": "1589ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "2492ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "2282ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2587ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21132ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "3009ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "3947ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2421ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3783ms", "action_id": "screenshot_20250726_072437", "screenshot_filename": "screenshot_20250726_072437.png", "report_screenshot": "screenshot_20250726_072437.png", "resolved_screenshot": "screenshots/screenshot_20250726_072437.png", "clean_action_id": "screenshot_20250726_072437", "prefixed_action_id": "al_screenshot_20250726_072437", "action_id_screenshot": "screenshots/screenshot_20250726_072437.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5578ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "3034ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]\"", "status": "passed", "duration": "11266ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2852ms", "action_id": "screenshot_20250726_074720", "screenshot_filename": "screenshot_20250726_074720.png", "report_screenshot": "screenshot_20250726_074720.png", "resolved_screenshot": "screenshots/screenshot_20250726_074720.png", "clean_action_id": "screenshot_20250726_074720", "prefixed_action_id": "al_screenshot_20250726_074720", "action_id_screenshot": "screenshots/screenshot_20250726_074720.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21132ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2981ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4001ms", "action_id": "screenshot_20250726_070552", "screenshot_filename": "screenshot_20250726_070552.png", "report_screenshot": "screenshot_20250726_070552.png", "resolved_screenshot": "screenshots/screenshot_20250726_070552.png", "clean_action_id": "screenshot_20250726_070552", "prefixed_action_id": "al_screenshot_20250726_070552", "action_id_screenshot": "screenshots/screenshot_20250726_070552.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2407ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "17030ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2971ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11119ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "passed", "duration": "2684ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "passed", "duration": "2727ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2746ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2330ms", "action_id": "screenshot_20250726_072423", "screenshot_filename": "screenshot_20250726_072423.png", "report_screenshot": "screenshot_20250726_072423.png", "resolved_screenshot": "screenshots/screenshot_20250726_072423.png", "clean_action_id": "screenshot_20250726_072423", "prefixed_action_id": "al_screenshot_20250726_072423", "action_id_screenshot": "screenshots/screenshot_20250726_072423.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "passed", "duration": "2985ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]\" is visible", "status": "passed", "duration": "4985ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]", "status": "passed", "duration": "2564ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "status": "passed", "duration": "2516ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2346ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "4288ms", "action_id": "screenshot_20250726_070234", "screenshot_filename": "screenshot_20250726_070234.png", "report_screenshot": "screenshot_20250726_070234.png", "resolved_screenshot": "screenshots/screenshot_20250726_070234.png", "clean_action_id": "screenshot_20250726_070234", "prefixed_action_id": "al_screenshot_20250726_070234", "action_id_screenshot": "screenshots/screenshot_20250726_070234.png"}, {"name": "Tap if locator exists: accessibility_id=\"Add to bag\"", "status": "passed", "duration": "7072ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3398ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2538ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "3923ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[contains(@name,\"Remove\")]\"", "status": "passed", "duration": "3724ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5015ms", "action_id": "screenshot_20250726_080058", "screenshot_filename": "screenshot_20250726_080058.png", "report_screenshot": "screenshot_20250726_080058.png", "resolved_screenshot": "screenshots/screenshot_20250726_080058.png", "clean_action_id": "screenshot_20250726_080058", "prefixed_action_id": "al_screenshot_20250726_080058", "action_id_screenshot": "screenshots/screenshot_20250726_080058.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU - Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            69 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2184ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2455ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]\"", "status": "passed", "duration": "11248ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "2711ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2569ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "18338ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "3064ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "2802ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2340ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "122ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2399ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3721ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "iOS Function: text - Text: \"kids toys\"", "status": "passed", "duration": "2633ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Execute Test Case: Click_Paginations (8 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3404ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2472ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2605ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2643ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "2537ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "4007ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3823ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2370ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4463ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1193ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2781ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3372ms", "action_id": "screenshot_20250726_072437", "screenshot_filename": "screenshot_20250726_072437.png", "report_screenshot": "screenshot_20250726_072437.png", "resolved_screenshot": "screenshots/screenshot_20250726_072437.png", "clean_action_id": "screenshot_20250726_072437", "prefixed_action_id": "al_screenshot_20250726_072437", "action_id_screenshot": "screenshots/screenshot_20250726_072437.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2774ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3110ms", "action_id": "screenshot_20250726_074720", "screenshot_filename": "screenshot_20250726_074720.png", "report_screenshot": "screenshot_20250726_074720.png", "resolved_screenshot": "screenshots/screenshot_20250726_074720.png", "clean_action_id": "screenshot_20250726_074720", "prefixed_action_id": "al_screenshot_20250726_074720", "action_id_screenshot": "screenshots/screenshot_20250726_074720.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2873ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3558ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "iOS Function: text - Text: \"env[cooker-id]\"", "status": "passed", "duration": "2686ms", "action_id": "screenshot_20250726_070552", "screenshot_filename": "screenshot_20250726_070552.png", "report_screenshot": "screenshot_20250726_070552.png", "resolved_screenshot": "screenshots/screenshot_20250726_070552.png", "clean_action_id": "screenshot_20250726_070552", "prefixed_action_id": "al_screenshot_20250726_070552", "action_id_screenshot": "screenshots/screenshot_20250726_070552.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1936ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2530ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2925ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4873ms", "action_id": "screenshot_20250726_072423", "screenshot_filename": "screenshot_20250726_072423.png", "report_screenshot": "screenshot_20250726_072423.png", "resolved_screenshot": "screenshots/screenshot_20250726_072423.png", "clean_action_id": "screenshot_20250726_072423", "prefixed_action_id": "al_screenshot_20250726_072423", "action_id_screenshot": "screenshots/screenshot_20250726_072423.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\"", "status": "passed", "duration": "11567ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "3339ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "2069ms", "action_id": "screenshot_20250726_070234", "screenshot_filename": "screenshot_20250726_070234.png", "report_screenshot": "screenshot_20250726_070234.png", "resolved_screenshot": "screenshots/screenshot_20250726_070234.png", "clean_action_id": "screenshot_20250726_070234", "prefixed_action_id": "al_screenshot_20250726_070234", "action_id_screenshot": "screenshots/screenshot_20250726_070234.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "2210ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2401ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2385ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2611ms", "action_id": "screenshot_20250726_080058", "screenshot_filename": "screenshot_20250726_080058.png", "report_screenshot": "screenshot_20250726_080058.png", "resolved_screenshot": "screenshots/screenshot_20250726_080058.png", "clean_action_id": "screenshot_20250726_080058", "prefixed_action_id": "al_screenshot_20250726_080058", "action_id_screenshot": "screenshots/screenshot_20250726_080058.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2578ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtJoinTodayButton\"]", "status": "passed", "duration": "2590ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1203ms", "action_id": "screenshot_20250726_073846", "screenshot_filename": "screenshot_20250726_073846.png", "report_screenshot": "screenshot_20250726_073846.png", "resolved_screenshot": "screenshots/screenshot_20250726_073846.png", "clean_action_id": "screenshot_20250726_073846", "prefixed_action_id": "al_screenshot_20250726_073846", "action_id_screenshot": "screenshots/screenshot_20250726_073846.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1736ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Cancel\"", "status": "passed", "duration": "3358ms", "action_id": "screenshot_20250726_065956", "screenshot_filename": "screenshot_20250726_065956.png", "report_screenshot": "screenshot_20250726_065956.png", "resolved_screenshot": "screenshots/screenshot_20250726_065956.png", "clean_action_id": "screenshot_20250726_065956", "prefixed_action_id": "al_screenshot_20250726_065956", "action_id_screenshot": "screenshots/screenshot_20250726_065956.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "3452ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_071923", "screenshot_filename": "screenshot_20250726_071923.png", "report_screenshot": "screenshot_20250726_071923.png", "resolved_screenshot": "screenshots/screenshot_20250726_071923.png", "clean_action_id": "screenshot_20250726_071923", "prefixed_action_id": "al_screenshot_20250726_071923", "action_id_screenshot": "screenshots/screenshot_20250726_071923.png"}, {"name": "Tap on Text: \"Click\"", "status": "passed", "duration": "2881ms", "action_id": "screenshot_20250726_073852", "screenshot_filename": "screenshot_20250726_073852.png", "report_screenshot": "screenshot_20250726_073852.png", "resolved_screenshot": "screenshots/screenshot_20250726_073852.png", "clean_action_id": "screenshot_20250726_073852", "prefixed_action_id": "al_screenshot_20250726_073852", "action_id_screenshot": "screenshots/screenshot_20250726_073852.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,\"store details\")])[1]", "status": "passed", "duration": "2577ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2596ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]\" is visible", "status": "passed", "duration": "11572ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "status": "passed", "duration": "2546ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2305ms", "action_id": "screenshot_20250726_071710", "screenshot_filename": "screenshot_20250726_071710.png", "report_screenshot": "screenshot_20250726_071710.png", "resolved_screenshot": "screenshots/screenshot_20250726_071710.png", "clean_action_id": "screenshot_20250726_071710", "prefixed_action_id": "al_screenshot_20250726_071710", "action_id_screenshot": "screenshots/screenshot_20250726_071710.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"About KHub Stores\"]", "status": "passed", "duration": "2526ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2395ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Swipe from (50%, 30%) to (50%, 70%)", "status": "passed", "duration": "2985ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2539ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2271ms", "action_id": "screenshot_20250726_065759", "screenshot_filename": "screenshot_20250726_065759.png", "report_screenshot": "screenshot_20250726_065759.png", "resolved_screenshot": "screenshots/screenshot_20250726_065759.png", "clean_action_id": "screenshot_20250726_065759", "prefixed_action_id": "al_screenshot_20250726_065759", "action_id_screenshot": "screenshots/screenshot_20250726_065759.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2719ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\"", "status": "passed", "duration": "11143ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Remove item\"]\"", "status": "passed", "duration": "11153ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2305ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountJoinTodayButton\"]", "status": "passed", "duration": "2430ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1235ms", "action_id": "screenshot_20250726_074640", "screenshot_filename": "screenshot_20250726_074640.png", "report_screenshot": "screenshot_20250726_074640.png", "resolved_screenshot": "screenshots/screenshot_20250726_074640.png", "clean_action_id": "screenshot_20250726_074640", "prefixed_action_id": "al_screenshot_20250726_074640", "action_id_screenshot": "screenshots/screenshot_20250726_074640.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1761ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1102ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Payments Check\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "31ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Restart app: au.com.kmart", "status": "passed", "duration": "2190ms", "action_id": "screenshot_20250726_080448", "screenshot_filename": "screenshot_20250726_080448.png", "report_screenshot": "screenshot_20250726_080448.png", "resolved_screenshot": "screenshots/screenshot_20250726_080448.png", "clean_action_id": "screenshot_20250726_080448", "prefixed_action_id": "al_screenshot_20250726_080448", "action_id_screenshot": "screenshots/screenshot_20250726_080448.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5534ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1191ms", "action_id": "screenshot_20250726_075827", "screenshot_filename": "screenshot_20250726_075827.png", "report_screenshot": "screenshot_20250726_075827.png", "resolved_screenshot": "screenshots/screenshot_20250726_075827.png", "clean_action_id": "screenshot_20250726_075827", "prefixed_action_id": "al_screenshot_20250726_075827", "action_id_screenshot": "screenshots/screenshot_20250726_075827.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_080306", "screenshot_filename": "screenshot_20250726_080306.png", "report_screenshot": "screenshot_20250726_080306.png", "resolved_screenshot": "screenshots/screenshot_20250726_080306.png", "clean_action_id": "screenshot_20250726_080306", "prefixed_action_id": "al_screenshot_20250726_080306", "action_id_screenshot": "screenshots/screenshot_20250726_080306.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "12459ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3578ms", "action_id": "screenshot_20250726_073517", "screenshot_filename": "screenshot_20250726_073517.png", "report_screenshot": "screenshot_20250726_073517.png", "resolved_screenshot": "screenshots/screenshot_20250726_073517.png", "clean_action_id": "screenshot_20250726_073517", "prefixed_action_id": "al_screenshot_20250726_073517", "action_id_screenshot": "screenshots/screenshot_20250726_073517.png"}, {"name": "iOS Function: text - Text: \"P_42691341\"", "status": "passed", "duration": "2669ms", "action_id": "screenshot_20250726_071314", "screenshot_filename": "screenshot_20250726_071314.png", "report_screenshot": "screenshot_20250726_071314.png", "resolved_screenshot": "screenshots/screenshot_20250726_071314.png", "clean_action_id": "screenshot_20250726_071314", "prefixed_action_id": "al_screenshot_20250726_071314", "action_id_screenshot": "screenshots/screenshot_20250726_071314.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1916ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2541ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "3700ms", "action_id": "screenshot_20250726_075614", "screenshot_filename": "screenshot_20250726_075614.png", "report_screenshot": "screenshot_20250726_075614.png", "resolved_screenshot": "screenshots/screenshot_20250726_075614.png", "clean_action_id": "screenshot_20250726_075614", "prefixed_action_id": "al_screenshot_20250726_075614", "action_id_screenshot": "screenshots/screenshot_20250726_075614.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "6408ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3219ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10805ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2009ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2574ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Continue to details\"]\" is visible", "status": "passed", "duration": "9481ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Continue to details\"]", "status": "passed", "duration": "2561ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"First Name\"]", "status": "passed", "duration": "2524ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5284ms", "action_id": "screenshot_20250726_072153", "screenshot_filename": "screenshot_20250726_072153.png", "report_screenshot": "screenshot_20250726_072153.png", "resolved_screenshot": "screenshots/screenshot_20250726_072153.png", "clean_action_id": "screenshot_20250726_072153", "prefixed_action_id": "al_screenshot_20250726_072153", "action_id_screenshot": "screenshots/screenshot_20250726_072153.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Last Name\"]", "status": "passed", "duration": "2578ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5156ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2551ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5944ms", "action_id": "screenshot_20250726_071328", "screenshot_filename": "screenshot_20250726_071328.png", "report_screenshot": "screenshot_20250726_071328.png", "resolved_screenshot": "screenshots/screenshot_20250726_071328.png", "clean_action_id": "screenshot_20250726_071328", "prefixed_action_id": "al_screenshot_20250726_071328", "action_id_screenshot": "screenshots/screenshot_20250726_071328.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Mobile number\"]", "status": "passed", "duration": "2533ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5268ms", "action_id": "screenshot_20250726_072621", "screenshot_filename": "screenshot_20250726_072621.png", "report_screenshot": "screenshot_20250726_072621.png", "resolved_screenshot": "screenshots/screenshot_20250726_072621.png", "clean_action_id": "screenshot_20250726_072621", "prefixed_action_id": "al_screenshot_20250726_072621", "action_id_screenshot": "screenshots/screenshot_20250726_072621.png"}, {"name": "iOS Function: text - Text: \" \"", "status": "passed", "duration": "2625ms", "action_id": "screenshot_20250726_075628", "screenshot_filename": "screenshot_20250726_075628.png", "report_screenshot": "screenshot_20250726_075628.png", "resolved_screenshot": "screenshots/screenshot_20250726_075628.png", "clean_action_id": "screenshot_20250726_075628", "prefixed_action_id": "al_screenshot_20250726_075628", "action_id_screenshot": "screenshots/screenshot_20250726_075628.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "3301ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap and Type at (54, 304): \"305 238 Flinders\"", "status": "passed", "duration": "5791ms", "action_id": "screenshot_20250726_071857", "screenshot_filename": "screenshot_20250726_071857.png", "report_screenshot": "screenshot_20250726_071857.png", "resolved_screenshot": "screenshots/screenshot_20250726_071857.png", "clean_action_id": "screenshot_20250726_071857", "prefixed_action_id": "al_screenshot_20250726_071857", "action_id_screenshot": "screenshots/screenshot_20250726_071857.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "2703ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[delivery-address-img]", "status": "passed", "duration": "2251ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Continue to payment\"]\" is visible", "status": "passed", "duration": "6332ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Continue to payment\"]", "status": "passed", "duration": "2480ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "status": "passed", "duration": "6792ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeLink[@name=\"PayPal\"]\" is visible", "status": "passed", "duration": "6723ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"PayPal\"]", "status": "passed", "duration": "2500ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[contains(@name,\"PayPal\")]\" exists", "status": "passed", "duration": "1763ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"close\"]", "status": "passed", "duration": "2584ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther", "status": "passed", "duration": "2535ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"Pay in 4\"]", "status": "passed", "duration": "2533ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Pay in 4 with PayPal\"]\" exists", "status": "passed", "duration": "1555ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"close\"]", "status": "passed", "duration": "2635ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "status": "passed", "duration": "2522ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "status": "passed", "duration": "2469ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"Afterpay\"]\" exists", "status": "passed", "duration": "1421ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2371ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther", "status": "passed", "duration": "2490ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "status": "passed", "duration": "2496ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Sign in with your Zip account\"]\" exists", "status": "passed", "duration": "1583ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2341ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3359ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10820ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2049ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "5076ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2579ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "2387ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Kmart_AU_Cleanup (6 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250726_072437", "screenshot_filename": "screenshot_20250726_072437.png", "report_screenshot": "screenshot_20250726_072437.png", "resolved_screenshot": "screenshots/screenshot_20250726_072437.png", "clean_action_id": "screenshot_20250726_072437", "prefixed_action_id": "al_screenshot_20250726_072437", "action_id_screenshot": "screenshots/screenshot_20250726_072437.png"}]}], "passed": 11, "failed": 0, "skipped": 0, "status": "passed"}