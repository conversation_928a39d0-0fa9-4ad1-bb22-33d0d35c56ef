Action Log - 2025-07-26 08:07:51
================================================================================

[[08:07:50]] [INFO] Generating execution report...
[[08:07:50]] [SUCCESS] All tests passed successfully!
[[08:07:50]] [SUCCESS] Screenshot refreshed
[[08:07:50]] [INFO] Refreshing screenshot...
[[08:07:50]] [SUCCESS] Screenshot refreshed successfully
[[08:07:50]] [SUCCESS] Screenshot refreshed successfully
[[08:07:50]] [SUCCESS] Screenshot refreshed
[[08:07:50]] [INFO] Refreshing screenshot...
[[08:07:47]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:07:47]] [SUCCESS] Screenshot refreshed successfully
[[08:07:47]] [SUCCESS] Screenshot refreshed successfully
[[08:07:47]] [SUCCESS] Screenshot refreshed
[[08:07:47]] [INFO] Refreshing screenshot...
[[08:07:42]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:07:42]] [SUCCESS] Screenshot refreshed successfully
[[08:07:42]] [SUCCESS] Screenshot refreshed successfully
[[08:07:41]] [SUCCESS] Screenshot refreshed
[[08:07:41]] [INFO] Refreshing screenshot...
[[08:07:38]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:07:38]] [SUCCESS] Screenshot refreshed successfully
[[08:07:38]] [SUCCESS] Screenshot refreshed successfully
[[08:07:37]] [SUCCESS] Screenshot refreshed
[[08:07:37]] [INFO] Refreshing screenshot...
[[08:07:34]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:07:33]] [SUCCESS] Screenshot refreshed successfully
[[08:07:33]] [SUCCESS] Screenshot refreshed successfully
[[08:07:33]] [SUCCESS] Screenshot refreshed
[[08:07:33]] [INFO] Refreshing screenshot...
[[08:07:27]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:07:26]] [SUCCESS] Screenshot refreshed successfully
[[08:07:26]] [SUCCESS] Screenshot refreshed successfully
[[08:07:26]] [SUCCESS] Screenshot refreshed
[[08:07:26]] [INFO] Refreshing screenshot...
[[08:07:19]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:07:19]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:07:19]] [INFO] Loading steps for multiStep action: Kmart_AU_Cleanup
[[08:07:19]] [INFO] 4pFqgUdIwt=running
[[08:07:19]] [INFO] Executing action 641/641: Execute Test Case: Kmart_AU_Cleanup (6 steps)
[[08:07:19]] [SUCCESS] Screenshot refreshed successfully
[[08:07:19]] [SUCCESS] Screenshot refreshed successfully
[[08:07:19]] [SUCCESS] Screenshot refreshed
[[08:07:19]] [INFO] Refreshing screenshot...
[[08:07:19]] [INFO] q6kSH9e0MI=pass
[[08:07:15]] [INFO] q6kSH9e0MI=running
[[08:07:15]] [INFO] Executing action 640/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:07:14]] [SUCCESS] Screenshot refreshed successfully
[[08:07:14]] [SUCCESS] Screenshot refreshed successfully
[[08:07:14]] [SUCCESS] Screenshot refreshed
[[08:07:14]] [INFO] Refreshing screenshot...
[[08:07:14]] [INFO] a4pJa7EAyI=pass
[[08:07:10]] [INFO] a4pJa7EAyI=running
[[08:07:10]] [INFO] Executing action 639/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:07:10]] [SUCCESS] Screenshot refreshed successfully
[[08:07:10]] [SUCCESS] Screenshot refreshed successfully
[[08:07:10]] [SUCCESS] Screenshot refreshed
[[08:07:10]] [INFO] Refreshing screenshot...
[[08:07:10]] [INFO] 2bcxKJ2cPg=pass
[[08:07:03]] [INFO] 2bcxKJ2cPg=running
[[08:07:03]] [INFO] Executing action 638/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[08:07:03]] [SUCCESS] Screenshot refreshed successfully
[[08:07:03]] [SUCCESS] Screenshot refreshed successfully
[[08:07:03]] [SUCCESS] Screenshot refreshed
[[08:07:03]] [INFO] Refreshing screenshot...
[[08:07:03]] [INFO] aqBkqyVhrZ=pass
[[08:06:59]] [INFO] aqBkqyVhrZ=running
[[08:06:59]] [INFO] Executing action 637/641: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:06:59]] [SUCCESS] Screenshot refreshed successfully
[[08:06:59]] [SUCCESS] Screenshot refreshed successfully
[[08:06:59]] [SUCCESS] Screenshot refreshed
[[08:06:59]] [INFO] Refreshing screenshot...
[[08:06:59]] [INFO] wSHsGWAwPm=pass
[[08:06:47]] [INFO] wSHsGWAwPm=running
[[08:06:47]] [INFO] Executing action 636/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[08:06:46]] [SUCCESS] Screenshot refreshed successfully
[[08:06:46]] [SUCCESS] Screenshot refreshed successfully
[[08:06:46]] [SUCCESS] Screenshot refreshed
[[08:06:46]] [INFO] Refreshing screenshot...
[[08:06:46]] [INFO] gPYNwJ0HKo=pass
[[08:06:42]] [INFO] gPYNwJ0HKo=running
[[08:06:42]] [INFO] Executing action 635/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:06:41]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [SUCCESS] Screenshot refreshed
[[08:06:41]] [INFO] Refreshing screenshot...
[[08:06:41]] [INFO] vYLhraWpQm=pass
[[08:06:37]] [INFO] vYLhraWpQm=running
[[08:06:37]] [INFO] Executing action 634/641: Tap on image: banner-close-updated.png
[[08:06:37]] [SUCCESS] Screenshot refreshed successfully
[[08:06:37]] [SUCCESS] Screenshot refreshed successfully
[[08:06:37]] [SUCCESS] Screenshot refreshed
[[08:06:37]] [INFO] Refreshing screenshot...
[[08:06:37]] [INFO] TAKgcEDqvz=pass
[[08:06:34]] [INFO] TAKgcEDqvz=running
[[08:06:34]] [INFO] Executing action 633/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[08:06:34]] [SUCCESS] Screenshot refreshed successfully
[[08:06:34]] [SUCCESS] Screenshot refreshed successfully
[[08:06:33]] [SUCCESS] Screenshot refreshed
[[08:06:33]] [INFO] Refreshing screenshot...
[[08:06:33]] [INFO] UgjXUTZy7Z=pass
[[08:06:30]] [INFO] UgjXUTZy7Z=running
[[08:06:30]] [INFO] Executing action 632/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[08:06:29]] [SUCCESS] Screenshot refreshed successfully
[[08:06:29]] [SUCCESS] Screenshot refreshed successfully
[[08:06:29]] [SUCCESS] Screenshot refreshed
[[08:06:29]] [INFO] Refreshing screenshot...
[[08:06:29]] [INFO] YqmO7h7VP0=pass
[[08:06:25]] [INFO] YqmO7h7VP0=running
[[08:06:25]] [INFO] Executing action 631/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:25]] [SUCCESS] Screenshot refreshed
[[08:06:25]] [INFO] Refreshing screenshot...
[[08:06:25]] [INFO] vYLhraWpQm=pass
[[08:06:21]] [INFO] vYLhraWpQm=running
[[08:06:21]] [INFO] Executing action 630/641: Tap on image: banner-close-updated.png
[[08:06:21]] [SUCCESS] Screenshot refreshed successfully
[[08:06:21]] [SUCCESS] Screenshot refreshed successfully
[[08:06:20]] [SUCCESS] Screenshot refreshed
[[08:06:20]] [INFO] Refreshing screenshot...
[[08:06:20]] [INFO] lSG7un0qKK=pass
[[08:06:18]] [INFO] lSG7un0qKK=running
[[08:06:18]] [INFO] Executing action 629/641: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[08:06:17]] [SUCCESS] Screenshot refreshed successfully
[[08:06:17]] [SUCCESS] Screenshot refreshed successfully
[[08:06:17]] [SUCCESS] Screenshot refreshed
[[08:06:17]] [INFO] Refreshing screenshot...
[[08:06:17]] [INFO] 9Pwdq32eUk=pass
[[08:06:13]] [INFO] 9Pwdq32eUk=running
[[08:06:13]] [INFO] Executing action 628/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[08:06:13]] [SUCCESS] Screenshot refreshed successfully
[[08:06:13]] [SUCCESS] Screenshot refreshed successfully
[[08:06:13]] [SUCCESS] Screenshot refreshed
[[08:06:13]] [INFO] Refreshing screenshot...
[[08:06:13]] [INFO] YBT2MVclAv=pass
[[08:06:09]] [INFO] YBT2MVclAv=running
[[08:06:09]] [INFO] Executing action 627/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[08:06:09]] [SUCCESS] Screenshot refreshed successfully
[[08:06:09]] [SUCCESS] Screenshot refreshed successfully
[[08:06:08]] [SUCCESS] Screenshot refreshed
[[08:06:08]] [INFO] Refreshing screenshot...
[[08:06:08]] [INFO] TzPItWbvDR=pass
[[08:06:04]] [INFO] TzPItWbvDR=running
[[08:06:04]] [INFO] Executing action 626/641: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[08:06:04]] [SUCCESS] Screenshot refreshed successfully
[[08:06:04]] [SUCCESS] Screenshot refreshed successfully
[[08:06:04]] [SUCCESS] Screenshot refreshed
[[08:06:04]] [INFO] Refreshing screenshot...
[[08:06:04]] [INFO] wSdfNe4Kww=pass
[[08:06:01]] [INFO] wSdfNe4Kww=running
[[08:06:01]] [INFO] Executing action 625/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[08:06:01]] [SUCCESS] Screenshot refreshed successfully
[[08:06:01]] [SUCCESS] Screenshot refreshed successfully
[[08:06:00]] [SUCCESS] Screenshot refreshed
[[08:06:00]] [INFO] Refreshing screenshot...
[[08:06:00]] [INFO] GN587Y6VBQ=pass
[[08:05:57]] [INFO] GN587Y6VBQ=running
[[08:05:57]] [INFO] Executing action 624/641: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[08:05:56]] [SUCCESS] Screenshot refreshed successfully
[[08:05:56]] [SUCCESS] Screenshot refreshed successfully
[[08:05:56]] [SUCCESS] Screenshot refreshed
[[08:05:56]] [INFO] Refreshing screenshot...
[[08:05:56]] [INFO] dkSs61jGvX=pass
[[08:05:52]] [INFO] dkSs61jGvX=running
[[08:05:52]] [INFO] Executing action 623/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[08:05:52]] [SUCCESS] Screenshot refreshed successfully
[[08:05:52]] [SUCCESS] Screenshot refreshed successfully
[[08:05:52]] [SUCCESS] Screenshot refreshed
[[08:05:52]] [INFO] Refreshing screenshot...
[[08:05:52]] [INFO] XLpUP3Wr93=pass
[[08:05:48]] [INFO] XLpUP3Wr93=running
[[08:05:48]] [INFO] Executing action 622/641: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[08:05:47]] [SUCCESS] Screenshot refreshed successfully
[[08:05:47]] [SUCCESS] Screenshot refreshed successfully
[[08:05:47]] [SUCCESS] Screenshot refreshed
[[08:05:47]] [INFO] Refreshing screenshot...
[[08:05:47]] [INFO] mfOWujfRpL=pass
[[08:05:44]] [INFO] mfOWujfRpL=running
[[08:05:44]] [INFO] Executing action 621/641: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[08:05:44]] [SUCCESS] Screenshot refreshed successfully
[[08:05:44]] [SUCCESS] Screenshot refreshed successfully
[[08:05:44]] [SUCCESS] Screenshot refreshed
[[08:05:44]] [INFO] Refreshing screenshot...
[[08:05:44]] [INFO] ftA0OJvd0W=pass
[[08:05:40]] [INFO] ftA0OJvd0W=running
[[08:05:40]] [INFO] Executing action 620/641: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[08:05:39]] [SUCCESS] Screenshot refreshed successfully
[[08:05:39]] [SUCCESS] Screenshot refreshed successfully
[[08:05:39]] [SUCCESS] Screenshot refreshed
[[08:05:39]] [INFO] Refreshing screenshot...
[[08:05:39]] [INFO] CBBib3pFkq=pass
[[08:05:31]] [INFO] CBBib3pFkq=running
[[08:05:31]] [INFO] Executing action 619/641: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[08:05:31]] [SUCCESS] Screenshot refreshed successfully
[[08:05:31]] [SUCCESS] Screenshot refreshed successfully
[[08:05:31]] [SUCCESS] Screenshot refreshed
[[08:05:31]] [INFO] Refreshing screenshot...
[[08:05:31]] [INFO] 6LQ5cq0f6N=pass
[[08:05:22]] [INFO] 6LQ5cq0f6N=running
[[08:05:22]] [INFO] Executing action 618/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[08:05:22]] [SUCCESS] Screenshot refreshed successfully
[[08:05:22]] [SUCCESS] Screenshot refreshed successfully
[[08:05:22]] [SUCCESS] Screenshot refreshed
[[08:05:22]] [INFO] Refreshing screenshot...
[[08:05:22]] [INFO] 1Lirmyxkft=pass
[[08:05:18]] [INFO] 1Lirmyxkft=running
[[08:05:18]] [INFO] Executing action 617/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[08:05:18]] [SUCCESS] Screenshot refreshed successfully
[[08:05:18]] [SUCCESS] Screenshot refreshed successfully
[[08:05:18]] [SUCCESS] Screenshot refreshed
[[08:05:18]] [INFO] Refreshing screenshot...
[[08:05:18]] [INFO] TTpwkHEyuE=pass
[[08:05:10]] [INFO] TTpwkHEyuE=running
[[08:05:10]] [INFO] Executing action 616/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[08:05:10]] [SUCCESS] Screenshot refreshed successfully
[[08:05:10]] [SUCCESS] Screenshot refreshed successfully
[[08:05:09]] [SUCCESS] Screenshot refreshed
[[08:05:09]] [INFO] Refreshing screenshot...
[[08:05:09]] [INFO] mMnRNh3NEd=pass
[[08:05:06]] [INFO] mMnRNh3NEd=running
[[08:05:06]] [INFO] Executing action 615/641: Tap on image: env[delivery-address-img]
[[08:05:05]] [SUCCESS] Screenshot refreshed successfully
[[08:05:05]] [SUCCESS] Screenshot refreshed successfully
[[08:05:05]] [SUCCESS] Screenshot refreshed
[[08:05:05]] [INFO] Refreshing screenshot...
[[08:05:05]] [INFO] NcU6aex76k=pass
[[08:05:01]] [INFO] NcU6aex76k=running
[[08:05:01]] [INFO] Executing action 614/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:05:01]] [SUCCESS] Screenshot refreshed successfully
[[08:05:01]] [SUCCESS] Screenshot refreshed successfully
[[08:05:01]] [SUCCESS] Screenshot refreshed
[[08:05:01]] [INFO] Refreshing screenshot...
[[08:05:01]] [INFO] SQ1i1ElZCE=pass
[[08:04:53]] [INFO] SQ1i1ElZCE=running
[[08:04:53]] [INFO] Executing action 613/641: Tap and Type at (54, 304): "305 238 Flinders"
[[08:04:53]] [SUCCESS] Screenshot refreshed successfully
[[08:04:53]] [SUCCESS] Screenshot refreshed successfully
[[08:04:53]] [SUCCESS] Screenshot refreshed
[[08:04:53]] [INFO] Refreshing screenshot...
[[08:04:53]] [INFO] 5ZzW1VVSzy=pass
[[08:04:48]] [INFO] 5ZzW1VVSzy=running
[[08:04:48]] [INFO] Executing action 612/641: Tap on Text: "address"
[[08:04:48]] [SUCCESS] Screenshot refreshed successfully
[[08:04:48]] [SUCCESS] Screenshot refreshed successfully
[[08:04:48]] [SUCCESS] Screenshot refreshed
[[08:04:48]] [INFO] Refreshing screenshot...
[[08:04:48]] [INFO] kDpsm2D3xt=pass
[[08:04:44]] [INFO] kDpsm2D3xt=running
[[08:04:44]] [INFO] Executing action 611/641: iOS Function: text - Text: " "
[[08:04:43]] [SUCCESS] Screenshot refreshed successfully
[[08:04:43]] [SUCCESS] Screenshot refreshed successfully
[[08:04:43]] [SUCCESS] Screenshot refreshed
[[08:04:43]] [INFO] Refreshing screenshot...
[[08:04:43]] [INFO] SFj4Aa7RHQ=pass
[[08:04:37]] [INFO] SFj4Aa7RHQ=running
[[08:04:37]] [INFO] Executing action 610/641: textClear action
[[08:04:36]] [SUCCESS] Screenshot refreshed successfully
[[08:04:36]] [SUCCESS] Screenshot refreshed successfully
[[08:04:36]] [SUCCESS] Screenshot refreshed
[[08:04:36]] [INFO] Refreshing screenshot...
[[08:04:36]] [INFO] yi5EsHEFvc=pass
[[08:04:32]] [INFO] yi5EsHEFvc=running
[[08:04:32]] [INFO] Executing action 609/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[08:04:32]] [SUCCESS] Screenshot refreshed successfully
[[08:04:32]] [SUCCESS] Screenshot refreshed successfully
[[08:04:32]] [SUCCESS] Screenshot refreshed
[[08:04:32]] [INFO] Refreshing screenshot...
[[08:04:32]] [INFO] lWJtKSqlPS=pass
[[08:04:25]] [INFO] lWJtKSqlPS=running
[[08:04:25]] [INFO] Executing action 608/641: textClear action
[[08:04:24]] [SUCCESS] Screenshot refreshed successfully
[[08:04:24]] [SUCCESS] Screenshot refreshed successfully
[[08:04:24]] [SUCCESS] Screenshot refreshed
[[08:04:24]] [INFO] Refreshing screenshot...
[[08:04:24]] [INFO] 9B5MQGTmpP=pass
[[08:04:20]] [INFO] 9B5MQGTmpP=running
[[08:04:20]] [INFO] Executing action 607/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:04:20]] [SUCCESS] Screenshot refreshed successfully
[[08:04:20]] [SUCCESS] Screenshot refreshed successfully
[[08:04:20]] [SUCCESS] Screenshot refreshed
[[08:04:20]] [INFO] Refreshing screenshot...
[[08:04:20]] [INFO] QvuueoTR8W=pass
[[08:04:13]] [INFO] QvuueoTR8W=running
[[08:04:13]] [INFO] Executing action 606/641: textClear action
[[08:04:13]] [SUCCESS] Screenshot refreshed successfully
[[08:04:13]] [SUCCESS] Screenshot refreshed successfully
[[08:04:13]] [SUCCESS] Screenshot refreshed
[[08:04:13]] [INFO] Refreshing screenshot...
[[08:04:13]] [INFO] p8rfQL9ara=pass
[[08:04:09]] [INFO] p8rfQL9ara=running
[[08:04:09]] [INFO] Executing action 605/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[08:04:08]] [SUCCESS] Screenshot refreshed successfully
[[08:04:08]] [SUCCESS] Screenshot refreshed successfully
[[08:04:08]] [SUCCESS] Screenshot refreshed
[[08:04:08]] [INFO] Refreshing screenshot...
[[08:04:08]] [INFO] CLMmkV1OIM=pass
[[08:04:01]] [INFO] CLMmkV1OIM=running
[[08:04:01]] [INFO] Executing action 604/641: textClear action
[[08:04:01]] [SUCCESS] Screenshot refreshed successfully
[[08:04:01]] [SUCCESS] Screenshot refreshed successfully
[[08:04:01]] [SUCCESS] Screenshot refreshed
[[08:04:01]] [INFO] Refreshing screenshot...
[[08:04:01]] [INFO] h9trcMrvxt=pass
[[08:03:57]] [INFO] h9trcMrvxt=running
[[08:03:57]] [INFO] Executing action 603/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[08:03:57]] [SUCCESS] Screenshot refreshed successfully
[[08:03:57]] [SUCCESS] Screenshot refreshed successfully
[[08:03:57]] [SUCCESS] Screenshot refreshed
[[08:03:57]] [INFO] Refreshing screenshot...
[[08:03:57]] [INFO] Q5A0cNaJ24=pass
[[08:03:53]] [INFO] Q5A0cNaJ24=running
[[08:03:53]] [INFO] Executing action 602/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[08:03:52]] [SUCCESS] Screenshot refreshed successfully
[[08:03:52]] [SUCCESS] Screenshot refreshed successfully
[[08:03:52]] [SUCCESS] Screenshot refreshed
[[08:03:52]] [INFO] Refreshing screenshot...
[[08:03:52]] [INFO] xAa049Qgls=pass
[[08:03:41]] [INFO] xAa049Qgls=running
[[08:03:41]] [INFO] Executing action 601/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[08:03:41]] [SUCCESS] Screenshot refreshed successfully
[[08:03:41]] [SUCCESS] Screenshot refreshed successfully
[[08:03:41]] [SUCCESS] Screenshot refreshed
[[08:03:41]] [INFO] Refreshing screenshot...
[[08:03:41]] [INFO] hwdyCKFAUJ=pass
[[08:03:37]] [INFO] hwdyCKFAUJ=running
[[08:03:37]] [INFO] Executing action 600/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:03:37]] [SUCCESS] Screenshot refreshed successfully
[[08:03:37]] [SUCCESS] Screenshot refreshed successfully
[[08:03:36]] [SUCCESS] Screenshot refreshed
[[08:03:36]] [INFO] Refreshing screenshot...
[[08:03:36]] [INFO] aqBkqyVhrZ=pass
[[08:03:33]] [INFO] aqBkqyVhrZ=running
[[08:03:33]] [INFO] Executing action 599/641: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:03:33]] [SUCCESS] Screenshot refreshed successfully
[[08:03:33]] [SUCCESS] Screenshot refreshed successfully
[[08:03:32]] [SUCCESS] Screenshot refreshed
[[08:03:32]] [INFO] Refreshing screenshot...
[[08:03:32]] [INFO] E3RDcrIH6J=pass
[[08:03:20]] [INFO] E3RDcrIH6J=running
[[08:03:20]] [INFO] Executing action 598/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[08:03:20]] [SUCCESS] Screenshot refreshed successfully
[[08:03:20]] [SUCCESS] Screenshot refreshed successfully
[[08:03:20]] [SUCCESS] Screenshot refreshed
[[08:03:20]] [INFO] Refreshing screenshot...
[[08:03:20]] [INFO] gPYNwJ0HKo=pass
[[08:03:15]] [INFO] gPYNwJ0HKo=running
[[08:03:15]] [INFO] Executing action 597/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:03:15]] [SUCCESS] Screenshot refreshed successfully
[[08:03:15]] [SUCCESS] Screenshot refreshed successfully
[[08:03:15]] [SUCCESS] Screenshot refreshed
[[08:03:15]] [INFO] Refreshing screenshot...
[[08:03:15]] [INFO] VLrfDHfkI8=pass
[[08:03:07]] [INFO] VLrfDHfkI8=running
[[08:03:07]] [INFO] Executing action 596/641: Tap on element with accessibility_id: Add to bag
[[08:03:07]] [SUCCESS] Screenshot refreshed successfully
[[08:03:07]] [SUCCESS] Screenshot refreshed successfully
[[08:03:06]] [SUCCESS] Screenshot refreshed
[[08:03:06]] [INFO] Refreshing screenshot...
[[08:03:06]] [INFO] PzxTDnwsZ7=pass
[[08:03:01]] [INFO] PzxTDnwsZ7=running
[[08:03:01]] [INFO] Executing action 595/641: Swipe from (50%, 70%) to (50%, 50%)
[[08:03:01]] [SUCCESS] Screenshot refreshed successfully
[[08:03:01]] [SUCCESS] Screenshot refreshed successfully
[[08:03:01]] [SUCCESS] Screenshot refreshed
[[08:03:01]] [INFO] Refreshing screenshot...
[[08:03:01]] [INFO] 6GkdPPZo8e=pass
[[08:02:57]] [INFO] 6GkdPPZo8e=running
[[08:02:57]] [INFO] Executing action 594/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:02:57]] [SUCCESS] Screenshot refreshed successfully
[[08:02:57]] [SUCCESS] Screenshot refreshed successfully
[[08:02:56]] [SUCCESS] Screenshot refreshed
[[08:02:56]] [INFO] Refreshing screenshot...
[[08:02:56]] [INFO] FSM5PqLDko=pass
[[08:02:53]] [INFO] FSM5PqLDko=running
[[08:02:53]] [INFO] Executing action 593/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:02:53]] [SUCCESS] Screenshot refreshed successfully
[[08:02:53]] [SUCCESS] Screenshot refreshed successfully
[[08:02:53]] [SUCCESS] Screenshot refreshed
[[08:02:53]] [INFO] Refreshing screenshot...
[[08:02:53]] [INFO] GPTMDcrFC2=pass
[[08:02:49]] [INFO] GPTMDcrFC2=running
[[08:02:49]] [INFO] Executing action 592/641: iOS Function: text - Text: "P_42691341"
[[08:02:48]] [SUCCESS] Screenshot refreshed successfully
[[08:02:48]] [SUCCESS] Screenshot refreshed successfully
[[08:02:48]] [SUCCESS] Screenshot refreshed
[[08:02:48]] [INFO] Refreshing screenshot...
[[08:02:48]] [INFO] 91WZz4k3NI=pass
[[08:02:43]] [INFO] 91WZz4k3NI=running
[[08:02:43]] [INFO] Executing action 591/641: Tap on Text: "Find"
[[08:02:43]] [SUCCESS] Screenshot refreshed successfully
[[08:02:43]] [SUCCESS] Screenshot refreshed successfully
[[08:02:43]] [SUCCESS] Screenshot refreshed
[[08:02:43]] [INFO] Refreshing screenshot...
[[08:02:43]] [INFO] ACaNCAo69V=pass
[[08:02:29]] [SUCCESS] Screenshot refreshed successfully
[[08:02:29]] [SUCCESS] Screenshot refreshed successfully
[[08:02:29]] [INFO] ACaNCAo69V=running
[[08:02:29]] [INFO] Executing action 590/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[08:02:29]] [SUCCESS] Screenshot refreshed
[[08:02:29]] [INFO] Refreshing screenshot...
[[08:02:28]] [SUCCESS] Screenshot refreshed successfully
[[08:02:28]] [SUCCESS] Screenshot refreshed successfully
[[08:02:28]] [SUCCESS] Screenshot refreshed
[[08:02:28]] [INFO] Refreshing screenshot...
[[08:02:24]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:02:24]] [SUCCESS] Screenshot refreshed successfully
[[08:02:24]] [SUCCESS] Screenshot refreshed successfully
[[08:02:23]] [SUCCESS] Screenshot refreshed
[[08:02:23]] [INFO] Refreshing screenshot...
[[08:02:19]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:02:19]] [SUCCESS] Screenshot refreshed successfully
[[08:02:19]] [SUCCESS] Screenshot refreshed successfully
[[08:02:19]] [SUCCESS] Screenshot refreshed
[[08:02:19]] [INFO] Refreshing screenshot...
[[08:02:14]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[08:02:14]] [SUCCESS] Screenshot refreshed successfully
[[08:02:14]] [SUCCESS] Screenshot refreshed successfully
[[08:02:14]] [SUCCESS] Screenshot refreshed
[[08:02:14]] [INFO] Refreshing screenshot...
[[08:02:10]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:02:09]] [SUCCESS] Screenshot refreshed successfully
[[08:02:09]] [SUCCESS] Screenshot refreshed successfully
[[08:02:09]] [SUCCESS] Screenshot refreshed
[[08:02:09]] [INFO] Refreshing screenshot...
[[08:02:04]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:02:04]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:02:04]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:02:03]] [INFO] JEpLBji8jZ=running
[[08:02:03]] [INFO] Executing action 589/641: Execute Test Case: Kmart-Signin (5 steps)
[[08:02:03]] [SUCCESS] Screenshot refreshed successfully
[[08:02:03]] [SUCCESS] Screenshot refreshed successfully
[[08:02:03]] [SUCCESS] Screenshot refreshed
[[08:02:03]] [INFO] Refreshing screenshot...
[[08:02:03]] [INFO] TrbMRAIV8i=pass
[[08:02:00]] [INFO] TrbMRAIV8i=running
[[08:02:00]] [INFO] Executing action 588/641: iOS Function: alert_accept
[[08:02:00]] [SUCCESS] Screenshot refreshed successfully
[[08:02:00]] [SUCCESS] Screenshot refreshed successfully
[[08:02:00]] [SUCCESS] Screenshot refreshed
[[08:02:00]] [INFO] Refreshing screenshot...
[[08:02:00]] [INFO] MxtVneSHFi=pass
[[08:01:53]] [INFO] MxtVneSHFi=running
[[08:01:53]] [INFO] Executing action 587/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:01:53]] [SUCCESS] Screenshot refreshed successfully
[[08:01:53]] [SUCCESS] Screenshot refreshed successfully
[[08:01:52]] [SUCCESS] Screenshot refreshed
[[08:01:52]] [INFO] Refreshing screenshot...
[[08:01:52]] [INFO] 3uORTsBIAg=pass
[[08:01:49]] [INFO] 3uORTsBIAg=running
[[08:01:49]] [INFO] Executing action 586/641: Restart app: au.com.kmart
[[08:01:49]] [SUCCESS] Screenshot refreshed successfully
[[08:01:49]] [SUCCESS] Screenshot refreshed successfully
[[08:01:48]] [SUCCESS] Screenshot refreshed
[[08:01:48]] [INFO] Refreshing screenshot...
[[08:01:48]] [INFO] K8uGC1LDOS=pass
[[08:01:37]] [SUCCESS] Screenshot refreshed successfully
[[08:01:37]] [SUCCESS] Screenshot refreshed successfully
[[08:01:37]] [INFO] K8uGC1LDOS=running
[[08:01:37]] [INFO] Executing action 585/641: Terminate app: au.com.kmart
[[08:01:37]] [SUCCESS] Screenshot refreshed
[[08:01:37]] [INFO] Refreshing screenshot...
[[08:01:37]] [SUCCESS] Screenshot refreshed successfully
[[08:01:37]] [SUCCESS] Screenshot refreshed successfully
[[08:01:36]] [SUCCESS] Screenshot refreshed
[[08:01:36]] [INFO] Refreshing screenshot...
[[08:01:34]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:01:33]] [SUCCESS] Screenshot refreshed successfully
[[08:01:33]] [SUCCESS] Screenshot refreshed successfully
[[08:01:33]] [SUCCESS] Screenshot refreshed
[[08:01:33]] [INFO] Refreshing screenshot...
[[08:01:21]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:01:21]] [SUCCESS] Screenshot refreshed successfully
[[08:01:21]] [SUCCESS] Screenshot refreshed successfully
[[08:01:20]] [SUCCESS] Screenshot refreshed
[[08:01:20]] [INFO] Refreshing screenshot...
[[08:01:17]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:01:16]] [SUCCESS] Screenshot refreshed successfully
[[08:01:16]] [SUCCESS] Screenshot refreshed successfully
[[08:01:16]] [SUCCESS] Screenshot refreshed
[[08:01:16]] [INFO] Refreshing screenshot...
[[08:01:13]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:01:12]] [SUCCESS] Screenshot refreshed successfully
[[08:01:12]] [SUCCESS] Screenshot refreshed successfully
[[08:01:12]] [SUCCESS] Screenshot refreshed
[[08:01:12]] [INFO] Refreshing screenshot...
[[08:01:06]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:01:06]] [SUCCESS] Screenshot refreshed successfully
[[08:01:06]] [SUCCESS] Screenshot refreshed successfully
[[08:01:05]] [SUCCESS] Screenshot refreshed
[[08:01:05]] [INFO] Refreshing screenshot...
[[08:00:59]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:00:59]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:00:59]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:00:59]] [INFO] Ll4UlkE3L9=running
[[08:00:59]] [INFO] Executing action 584/641: cleanupSteps action
[[08:00:59]] [SUCCESS] Screenshot refreshed successfully
[[08:00:59]] [SUCCESS] Screenshot refreshed successfully
[[08:00:59]] [SUCCESS] Screenshot refreshed
[[08:00:59]] [INFO] Refreshing screenshot...
[[08:00:59]] [INFO] 25UEKPIknm=pass
[[08:00:56]] [INFO] 25UEKPIknm=running
[[08:00:56]] [INFO] Executing action 583/641: Terminate app: env[appid]
[[08:00:56]] [SUCCESS] Screenshot refreshed successfully
[[08:00:56]] [SUCCESS] Screenshot refreshed successfully
[[08:00:56]] [SUCCESS] Screenshot refreshed
[[08:00:56]] [INFO] Refreshing screenshot...
[[08:00:56]] [INFO] UqgDn5CuPY=pass
[[08:00:52]] [INFO] UqgDn5CuPY=running
[[08:00:52]] [INFO] Executing action 582/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[08:00:52]] [SUCCESS] Screenshot refreshed successfully
[[08:00:52]] [SUCCESS] Screenshot refreshed successfully
[[08:00:52]] [SUCCESS] Screenshot refreshed
[[08:00:52]] [INFO] Refreshing screenshot...
[[08:00:52]] [INFO] VfTTTtrliQ=pass
[[08:00:49]] [INFO] VfTTTtrliQ=running
[[08:00:49]] [INFO] Executing action 581/641: iOS Function: alert_accept
[[08:00:49]] [SUCCESS] Screenshot refreshed successfully
[[08:00:49]] [SUCCESS] Screenshot refreshed successfully
[[08:00:49]] [SUCCESS] Screenshot refreshed
[[08:00:49]] [INFO] Refreshing screenshot...
[[08:00:49]] [INFO] ipT2XD9io6=pass
[[08:00:45]] [INFO] ipT2XD9io6=running
[[08:00:45]] [INFO] Executing action 580/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[08:00:45]] [SUCCESS] Screenshot refreshed successfully
[[08:00:45]] [SUCCESS] Screenshot refreshed successfully
[[08:00:45]] [SUCCESS] Screenshot refreshed
[[08:00:45]] [INFO] Refreshing screenshot...
[[08:00:45]] [INFO] OKCHAK6HCJ=pass
[[08:00:41]] [INFO] OKCHAK6HCJ=running
[[08:00:41]] [INFO] Executing action 579/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:00:41]] [SUCCESS] Screenshot refreshed successfully
[[08:00:41]] [SUCCESS] Screenshot refreshed successfully
[[08:00:40]] [SUCCESS] Screenshot refreshed
[[08:00:40]] [INFO] Refreshing screenshot...
[[08:00:40]] [INFO] VLlqyGmmr8=pass
[[08:00:28]] [INFO] VLlqyGmmr8=running
[[08:00:28]] [INFO] Executing action 578/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Remove item"]"
[[08:00:28]] [SUCCESS] Screenshot refreshed successfully
[[08:00:28]] [SUCCESS] Screenshot refreshed successfully
[[08:00:27]] [SUCCESS] Screenshot refreshed
[[08:00:27]] [INFO] Refreshing screenshot...
[[08:00:27]] [INFO] rWuyGodCon=pass
[[08:00:15]] [INFO] rWuyGodCon=running
[[08:00:15]] [INFO] Executing action 577/641: Tap if locator exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]"
[[08:00:15]] [SUCCESS] Screenshot refreshed successfully
[[08:00:15]] [SUCCESS] Screenshot refreshed successfully
[[08:00:14]] [SUCCESS] Screenshot refreshed
[[08:00:14]] [INFO] Refreshing screenshot...
[[08:00:14]] [INFO] HlpBHrQZnk=pass
[[08:00:10]] [INFO] HlpBHrQZnk=running
[[08:00:10]] [INFO] Executing action 576/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:00:10]] [SUCCESS] Screenshot refreshed successfully
[[08:00:10]] [SUCCESS] Screenshot refreshed successfully
[[08:00:10]] [SUCCESS] Screenshot refreshed
[[08:00:10]] [INFO] Refreshing screenshot...
[[08:00:10]] [INFO] AEnFqnkOa1=pass
[[08:00:06]] [INFO] AEnFqnkOa1=running
[[08:00:06]] [INFO] Executing action 575/641: Tap on image: banner-close-updated.png
[[08:00:06]] [SUCCESS] Screenshot refreshed successfully
[[08:00:06]] [SUCCESS] Screenshot refreshed successfully
[[08:00:06]] [SUCCESS] Screenshot refreshed
[[08:00:06]] [INFO] Refreshing screenshot...
[[08:00:06]] [INFO] z1CfcW4xYT=pass
[[08:00:02]] [INFO] z1CfcW4xYT=running
[[08:00:02]] [INFO] Executing action 574/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:00:01]] [SUCCESS] Screenshot refreshed successfully
[[08:00:01]] [SUCCESS] Screenshot refreshed successfully
[[08:00:01]] [SUCCESS] Screenshot refreshed
[[08:00:01]] [INFO] Refreshing screenshot...
[[08:00:01]] [INFO] dJNRgTXoqs=pass
[[07:59:57]] [INFO] dJNRgTXoqs=running
[[07:59:57]] [INFO] Executing action 573/641: Swipe from (50%, 30%) to (50%, 70%)
[[07:59:57]] [SUCCESS] Screenshot refreshed successfully
[[07:59:57]] [SUCCESS] Screenshot refreshed successfully
[[07:59:56]] [SUCCESS] Screenshot refreshed
[[07:59:56]] [INFO] Refreshing screenshot...
[[07:59:56]] [INFO] ceF4VRTJlO=pass
[[07:59:53]] [INFO] ceF4VRTJlO=running
[[07:59:53]] [INFO] Executing action 572/641: Tap on image: banner-close-updated.png
[[07:59:52]] [SUCCESS] Screenshot refreshed successfully
[[07:59:52]] [SUCCESS] Screenshot refreshed successfully
[[07:59:52]] [SUCCESS] Screenshot refreshed
[[07:59:52]] [INFO] Refreshing screenshot...
[[07:59:52]] [INFO] 8hCPyY2zPt=pass
[[07:59:48]] [INFO] 8hCPyY2zPt=running
[[07:59:48]] [INFO] Executing action 571/641: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[07:59:48]] [SUCCESS] Screenshot refreshed successfully
[[07:59:48]] [SUCCESS] Screenshot refreshed successfully
[[07:59:48]] [SUCCESS] Screenshot refreshed
[[07:59:48]] [INFO] Refreshing screenshot...
[[07:59:48]] [INFO] r0FfJ85LFM=pass
[[07:59:44]] [INFO] r0FfJ85LFM=running
[[07:59:44]] [INFO] Executing action 570/641: Tap on image: banner-close-updated.png
[[07:59:44]] [SUCCESS] Screenshot refreshed successfully
[[07:59:44]] [SUCCESS] Screenshot refreshed successfully
[[07:59:44]] [SUCCESS] Screenshot refreshed
[[07:59:44]] [INFO] Refreshing screenshot...
[[07:59:44]] [INFO] 2QEdm5WM18=pass
[[07:59:40]] [INFO] 2QEdm5WM18=running
[[07:59:40]] [INFO] Executing action 569/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[07:59:39]] [SUCCESS] Screenshot refreshed successfully
[[07:59:39]] [SUCCESS] Screenshot refreshed successfully
[[07:59:39]] [SUCCESS] Screenshot refreshed
[[07:59:39]] [INFO] Refreshing screenshot...
[[07:59:39]] [INFO] NW6M15JbAy=pass
[[07:59:26]] [INFO] NW6M15JbAy=running
[[07:59:26]] [INFO] Executing action 568/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[07:59:26]] [SUCCESS] Screenshot refreshed successfully
[[07:59:26]] [SUCCESS] Screenshot refreshed successfully
[[07:59:26]] [SUCCESS] Screenshot refreshed
[[07:59:26]] [INFO] Refreshing screenshot...
[[07:59:26]] [INFO] njiHWyVooT=pass
[[07:59:22]] [INFO] njiHWyVooT=running
[[07:59:22]] [INFO] Executing action 567/641: Tap on image: banner-close-updated.png
[[07:59:21]] [SUCCESS] Screenshot refreshed successfully
[[07:59:21]] [SUCCESS] Screenshot refreshed successfully
[[07:59:21]] [SUCCESS] Screenshot refreshed
[[07:59:21]] [INFO] Refreshing screenshot...
[[07:59:21]] [INFO] 93bAew9Y4Y=pass
[[07:59:17]] [INFO] 93bAew9Y4Y=running
[[07:59:17]] [INFO] Executing action 566/641: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[07:59:17]] [SUCCESS] Screenshot refreshed successfully
[[07:59:17]] [SUCCESS] Screenshot refreshed successfully
[[07:59:17]] [SUCCESS] Screenshot refreshed
[[07:59:17]] [INFO] Refreshing screenshot...
[[07:59:17]] [INFO] rPQ5EkTza1=pass
[[07:59:13]] [SUCCESS] Screenshot refreshed successfully
[[07:59:13]] [SUCCESS] Screenshot refreshed successfully
[[07:59:12]] [INFO] rPQ5EkTza1=running
[[07:59:12]] [INFO] Executing action 565/641: Tap on Text: "Click"
[[07:59:12]] [SUCCESS] Screenshot refreshed
[[07:59:12]] [INFO] Refreshing screenshot...
[[07:59:12]] [SUCCESS] Screenshot refreshed successfully
[[07:59:12]] [SUCCESS] Screenshot refreshed successfully
[[07:59:12]] [SUCCESS] Screenshot refreshed
[[07:59:12]] [INFO] Refreshing screenshot...
[[07:59:09]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:59:08]] [SUCCESS] Screenshot refreshed successfully
[[07:59:08]] [SUCCESS] Screenshot refreshed successfully
[[07:59:08]] [SUCCESS] Screenshot refreshed
[[07:59:08]] [INFO] Refreshing screenshot...
[[07:58:56]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:58:56]] [SUCCESS] Screenshot refreshed successfully
[[07:58:56]] [SUCCESS] Screenshot refreshed successfully
[[07:58:56]] [SUCCESS] Screenshot refreshed
[[07:58:56]] [INFO] Refreshing screenshot...
[[07:58:50]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[07:58:49]] [SUCCESS] Screenshot refreshed successfully
[[07:58:49]] [SUCCESS] Screenshot refreshed successfully
[[07:58:49]] [SUCCESS] Screenshot refreshed
[[07:58:49]] [INFO] Refreshing screenshot...
[[07:58:45]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:58:45]] [SUCCESS] Screenshot refreshed successfully
[[07:58:45]] [SUCCESS] Screenshot refreshed successfully
[[07:58:45]] [SUCCESS] Screenshot refreshed
[[07:58:45]] [INFO] Refreshing screenshot...
[[07:58:41]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[07:58:40]] [SUCCESS] Screenshot refreshed successfully
[[07:58:40]] [SUCCESS] Screenshot refreshed successfully
[[07:58:40]] [SUCCESS] Screenshot refreshed
[[07:58:40]] [INFO] Refreshing screenshot...
[[07:58:37]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:58:37]] [SUCCESS] Screenshot refreshed successfully
[[07:58:37]] [SUCCESS] Screenshot refreshed successfully
[[07:58:36]] [SUCCESS] Screenshot refreshed
[[07:58:36]] [INFO] Refreshing screenshot...
[[07:58:32]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[07:58:32]] [SUCCESS] Screenshot refreshed successfully
[[07:58:32]] [SUCCESS] Screenshot refreshed successfully
[[07:58:32]] [SUCCESS] Screenshot refreshed
[[07:58:32]] [INFO] Refreshing screenshot...
[[07:58:25]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[07:58:25]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[07:58:25]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[07:58:25]] [INFO] 0YgZZfWdYY=running
[[07:58:25]] [INFO] Executing action 564/641: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[07:58:24]] [SUCCESS] Screenshot refreshed successfully
[[07:58:24]] [SUCCESS] Screenshot refreshed successfully
[[07:58:24]] [SUCCESS] Screenshot refreshed
[[07:58:24]] [INFO] Refreshing screenshot...
[[07:58:24]] [INFO] arH1CZCPXh=pass
[[07:58:19]] [INFO] arH1CZCPXh=running
[[07:58:19]] [INFO] Executing action 563/641: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[07:58:19]] [SUCCESS] Screenshot refreshed successfully
[[07:58:19]] [SUCCESS] Screenshot refreshed successfully
[[07:58:19]] [SUCCESS] Screenshot refreshed
[[07:58:19]] [INFO] Refreshing screenshot...
[[07:58:19]] [INFO] JLAJhxPdsl=pass
[[07:58:14]] [INFO] JLAJhxPdsl=running
[[07:58:14]] [INFO] Executing action 562/641: Tap on Text: "Cancel"
[[07:58:13]] [SUCCESS] Screenshot refreshed successfully
[[07:58:13]] [SUCCESS] Screenshot refreshed successfully
[[07:58:13]] [SUCCESS] Screenshot refreshed
[[07:58:13]] [INFO] Refreshing screenshot...
[[07:58:13]] [INFO] UqgDn5CuPY=pass
[[07:58:10]] [INFO] UqgDn5CuPY=running
[[07:58:10]] [INFO] Executing action 561/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[07:58:10]] [SUCCESS] Screenshot refreshed successfully
[[07:58:10]] [SUCCESS] Screenshot refreshed successfully
[[07:58:10]] [SUCCESS] Screenshot refreshed
[[07:58:10]] [INFO] Refreshing screenshot...
[[07:58:10]] [INFO] VfTTTtrliQ=pass
[[07:58:07]] [INFO] VfTTTtrliQ=running
[[07:58:07]] [INFO] Executing action 560/641: iOS Function: alert_accept
[[07:58:07]] [SUCCESS] Screenshot refreshed successfully
[[07:58:07]] [SUCCESS] Screenshot refreshed successfully
[[07:58:07]] [SUCCESS] Screenshot refreshed
[[07:58:07]] [INFO] Refreshing screenshot...
[[07:58:07]] [INFO] ipT2XD9io6=pass
[[07:58:03]] [INFO] ipT2XD9io6=running
[[07:58:03]] [INFO] Executing action 559/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[07:58:02]] [SUCCESS] Screenshot refreshed successfully
[[07:58:02]] [SUCCESS] Screenshot refreshed successfully
[[07:58:02]] [SUCCESS] Screenshot refreshed
[[07:58:02]] [INFO] Refreshing screenshot...
[[07:58:02]] [INFO] OKCHAK6HCJ=pass
[[07:57:58]] [INFO] OKCHAK6HCJ=running
[[07:57:58]] [INFO] Executing action 558/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:57:58]] [SUCCESS] Screenshot refreshed successfully
[[07:57:58]] [SUCCESS] Screenshot refreshed successfully
[[07:57:58]] [SUCCESS] Screenshot refreshed
[[07:57:58]] [INFO] Refreshing screenshot...
[[07:57:58]] [INFO] RbD937Xbte=pass
[[07:57:53]] [INFO] RbD937Xbte=running
[[07:57:53]] [INFO] Executing action 557/641: Tap on Text: "out"
[[07:57:53]] [SUCCESS] Screenshot refreshed successfully
[[07:57:53]] [SUCCESS] Screenshot refreshed successfully
[[07:57:53]] [SUCCESS] Screenshot refreshed
[[07:57:53]] [INFO] Refreshing screenshot...
[[07:57:53]] [INFO] ylslyLAYKb=pass
[[07:57:49]] [INFO] ylslyLAYKb=running
[[07:57:49]] [INFO] Executing action 556/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:57:49]] [SUCCESS] Screenshot refreshed successfully
[[07:57:49]] [SUCCESS] Screenshot refreshed successfully
[[07:57:49]] [SUCCESS] Screenshot refreshed
[[07:57:49]] [INFO] Refreshing screenshot...
[[07:57:49]] [INFO] wguGCt7OoB=pass
[[07:57:45]] [INFO] wguGCt7OoB=running
[[07:57:45]] [INFO] Executing action 555/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:57:45]] [SUCCESS] Screenshot refreshed successfully
[[07:57:45]] [SUCCESS] Screenshot refreshed successfully
[[07:57:44]] [SUCCESS] Screenshot refreshed
[[07:57:44]] [INFO] Refreshing screenshot...
[[07:57:44]] [INFO] RDQCFIxjA0=pass
[[07:57:41]] [INFO] RDQCFIxjA0=running
[[07:57:41]] [INFO] Executing action 554/641: Swipe from (90%, 20%) to (30%, 20%)
[[07:57:41]] [SUCCESS] Screenshot refreshed successfully
[[07:57:41]] [SUCCESS] Screenshot refreshed successfully
[[07:57:40]] [SUCCESS] Screenshot refreshed
[[07:57:40]] [INFO] Refreshing screenshot...
[[07:57:40]] [INFO] x4Mid4HQ0Z=pass
[[07:57:37]] [INFO] x4Mid4HQ0Z=running
[[07:57:37]] [INFO] Executing action 553/641: Swipe from (90%, 20%) to (30%, 20%)
[[07:57:37]] [SUCCESS] Screenshot refreshed successfully
[[07:57:37]] [SUCCESS] Screenshot refreshed successfully
[[07:57:36]] [SUCCESS] Screenshot refreshed
[[07:57:36]] [INFO] Refreshing screenshot...
[[07:57:36]] [INFO] OKCHAK6HCJ=pass
[[07:57:32]] [INFO] OKCHAK6HCJ=running
[[07:57:32]] [INFO] Executing action 552/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:57:31]] [SUCCESS] Screenshot refreshed successfully
[[07:57:31]] [SUCCESS] Screenshot refreshed successfully
[[07:57:31]] [SUCCESS] Screenshot refreshed
[[07:57:31]] [INFO] Refreshing screenshot...
[[07:57:31]] [INFO] wJPWHp18GJ=pass
[[07:57:18]] [INFO] wJPWHp18GJ=running
[[07:57:18]] [INFO] Executing action 551/641: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"to wishlist")]"
[[07:57:18]] [SUCCESS] Screenshot refreshed successfully
[[07:57:18]] [SUCCESS] Screenshot refreshed successfully
[[07:57:18]] [SUCCESS] Screenshot refreshed
[[07:57:18]] [INFO] Refreshing screenshot...
[[07:57:18]] [INFO] ylslyLAYKb=pass
[[07:57:11]] [INFO] ylslyLAYKb=running
[[07:57:11]] [INFO] Executing action 550/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:57:11]] [SUCCESS] Screenshot refreshed successfully
[[07:57:11]] [SUCCESS] Screenshot refreshed successfully
[[07:57:11]] [SUCCESS] Screenshot refreshed
[[07:57:11]] [INFO] Refreshing screenshot...
[[07:57:11]] [INFO] 0bnBNoqPt8=pass
[[07:57:07]] [INFO] 0bnBNoqPt8=running
[[07:57:07]] [INFO] Executing action 549/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:57:06]] [SUCCESS] Screenshot refreshed successfully
[[07:57:06]] [SUCCESS] Screenshot refreshed successfully
[[07:57:06]] [SUCCESS] Screenshot refreshed
[[07:57:06]] [INFO] Refreshing screenshot...
[[07:57:06]] [INFO] xmelRkcdVx=pass
[[07:57:02]] [INFO] xmelRkcdVx=running
[[07:57:02]] [INFO] Executing action 548/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:57:02]] [SUCCESS] Screenshot refreshed successfully
[[07:57:02]] [SUCCESS] Screenshot refreshed successfully
[[07:57:02]] [SUCCESS] Screenshot refreshed
[[07:57:02]] [INFO] Refreshing screenshot...
[[07:57:02]] [INFO] ksCBjJiwHZ=pass
[[07:56:58]] [INFO] ksCBjJiwHZ=running
[[07:56:58]] [INFO] Executing action 547/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:56:58]] [SUCCESS] Screenshot refreshed successfully
[[07:56:58]] [SUCCESS] Screenshot refreshed successfully
[[07:56:58]] [SUCCESS] Screenshot refreshed
[[07:56:58]] [INFO] Refreshing screenshot...
[[07:56:58]] [INFO] d40Oo7famr=pass
[[07:56:54]] [INFO] d40Oo7famr=running
[[07:56:54]] [INFO] Executing action 546/641: iOS Function: text - Text: "env[cooker-id]"
[[07:56:54]] [SUCCESS] Screenshot refreshed successfully
[[07:56:54]] [SUCCESS] Screenshot refreshed successfully
[[07:56:53]] [SUCCESS] Screenshot refreshed
[[07:56:53]] [INFO] Refreshing screenshot...
[[07:56:53]] [INFO] ewuLtuqVuo=pass
[[07:56:48]] [INFO] ewuLtuqVuo=running
[[07:56:48]] [INFO] Executing action 545/641: Tap on Text: "Find"
[[07:56:48]] [SUCCESS] Screenshot refreshed successfully
[[07:56:48]] [SUCCESS] Screenshot refreshed successfully
[[07:56:48]] [SUCCESS] Screenshot refreshed
[[07:56:48]] [INFO] Refreshing screenshot...
[[07:56:48]] [INFO] GTXmST3hEA=pass
[[07:56:44]] [INFO] GTXmST3hEA=running
[[07:56:44]] [INFO] Executing action 544/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:56:43]] [SUCCESS] Screenshot refreshed successfully
[[07:56:43]] [SUCCESS] Screenshot refreshed successfully
[[07:56:43]] [SUCCESS] Screenshot refreshed
[[07:56:43]] [INFO] Refreshing screenshot...
[[07:56:43]] [INFO] qkZ5KShdEU=pass
[[07:56:39]] [INFO] qkZ5KShdEU=running
[[07:56:39]] [INFO] Executing action 543/641: iOS Function: text - Text: "env[pwd]"
[[07:56:38]] [SUCCESS] Screenshot refreshed successfully
[[07:56:38]] [SUCCESS] Screenshot refreshed successfully
[[07:56:38]] [SUCCESS] Screenshot refreshed
[[07:56:38]] [INFO] Refreshing screenshot...
[[07:56:38]] [INFO] 7g2LmvjtEZ=pass
[[07:56:34]] [INFO] 7g2LmvjtEZ=running
[[07:56:34]] [INFO] Executing action 542/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:56:34]] [SUCCESS] Screenshot refreshed successfully
[[07:56:34]] [SUCCESS] Screenshot refreshed successfully
[[07:56:33]] [SUCCESS] Screenshot refreshed
[[07:56:33]] [INFO] Refreshing screenshot...
[[07:56:33]] [INFO] OUT2ASweb6=pass
[[07:56:29]] [INFO] OUT2ASweb6=running
[[07:56:29]] [INFO] Executing action 541/641: iOS Function: text - Text: "env[uname]"
[[07:56:28]] [SUCCESS] Screenshot refreshed successfully
[[07:56:28]] [SUCCESS] Screenshot refreshed successfully
[[07:56:28]] [SUCCESS] Screenshot refreshed
[[07:56:28]] [INFO] Refreshing screenshot...
[[07:56:28]] [INFO] TV4kJIIV9v=pass
[[07:56:24]] [INFO] TV4kJIIV9v=running
[[07:56:24]] [INFO] Executing action 540/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:56:24]] [SUCCESS] Screenshot refreshed successfully
[[07:56:24]] [SUCCESS] Screenshot refreshed successfully
[[07:56:24]] [SUCCESS] Screenshot refreshed
[[07:56:24]] [INFO] Refreshing screenshot...
[[07:56:24]] [INFO] kQJbqm7uCi=pass
[[07:56:21]] [INFO] kQJbqm7uCi=running
[[07:56:21]] [INFO] Executing action 539/641: iOS Function: alert_accept
[[07:56:21]] [SUCCESS] Screenshot refreshed successfully
[[07:56:21]] [SUCCESS] Screenshot refreshed successfully
[[07:56:21]] [SUCCESS] Screenshot refreshed
[[07:56:21]] [INFO] Refreshing screenshot...
[[07:56:21]] [INFO] SPE01N6pyp=pass
[[07:56:15]] [INFO] SPE01N6pyp=running
[[07:56:15]] [INFO] Executing action 538/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:56:14]] [SUCCESS] Screenshot refreshed successfully
[[07:56:14]] [SUCCESS] Screenshot refreshed successfully
[[07:56:14]] [SUCCESS] Screenshot refreshed
[[07:56:14]] [INFO] Refreshing screenshot...
[[07:56:14]] [INFO] WEB5St2Mb7=pass
[[07:56:10]] [INFO] WEB5St2Mb7=running
[[07:56:10]] [INFO] Executing action 537/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:56:10]] [SUCCESS] Screenshot refreshed successfully
[[07:56:10]] [SUCCESS] Screenshot refreshed successfully
[[07:56:10]] [SUCCESS] Screenshot refreshed
[[07:56:10]] [INFO] Refreshing screenshot...
[[07:56:10]] [INFO] To7bij5MnF=pass
[[07:56:05]] [INFO] To7bij5MnF=running
[[07:56:05]] [INFO] Executing action 536/641: Swipe from (5%, 50%) to (90%, 50%)
[[07:56:04]] [SUCCESS] Screenshot refreshed successfully
[[07:56:04]] [SUCCESS] Screenshot refreshed successfully
[[07:56:04]] [SUCCESS] Screenshot refreshed
[[07:56:04]] [INFO] Refreshing screenshot...
[[07:56:04]] [INFO] NkybTKfs2U=pass
[[07:55:59]] [INFO] NkybTKfs2U=running
[[07:55:59]] [INFO] Executing action 535/641: Swipe from (5%, 50%) to (90%, 50%)
[[07:55:59]] [SUCCESS] Screenshot refreshed successfully
[[07:55:59]] [SUCCESS] Screenshot refreshed successfully
[[07:55:58]] [SUCCESS] Screenshot refreshed
[[07:55:58]] [INFO] Refreshing screenshot...
[[07:55:58]] [INFO] dYEtjrv6lz=pass
[[07:55:54]] [INFO] dYEtjrv6lz=running
[[07:55:54]] [INFO] Executing action 534/641: Tap on Text: "Months"
[[07:55:54]] [SUCCESS] Screenshot refreshed successfully
[[07:55:54]] [SUCCESS] Screenshot refreshed successfully
[[07:55:54]] [SUCCESS] Screenshot refreshed
[[07:55:54]] [INFO] Refreshing screenshot...
[[07:55:54]] [INFO] eGQ7VrKUSo=pass
[[07:55:50]] [INFO] eGQ7VrKUSo=running
[[07:55:50]] [INFO] Executing action 533/641: Tap on Text: "Age"
[[07:55:50]] [SUCCESS] Screenshot refreshed successfully
[[07:55:50]] [SUCCESS] Screenshot refreshed successfully
[[07:55:49]] [SUCCESS] Screenshot refreshed
[[07:55:49]] [INFO] Refreshing screenshot...
[[07:55:49]] [INFO] zNRPvs2cC4=pass
[[07:55:45]] [INFO] zNRPvs2cC4=running
[[07:55:45]] [INFO] Executing action 532/641: Tap on Text: "Toys"
[[07:55:45]] [SUCCESS] Screenshot refreshed successfully
[[07:55:45]] [SUCCESS] Screenshot refreshed successfully
[[07:55:45]] [SUCCESS] Screenshot refreshed
[[07:55:45]] [INFO] Refreshing screenshot...
[[07:55:45]] [INFO] KyyS139agr=pass
[[07:55:41]] [INFO] KyyS139agr=running
[[07:55:41]] [INFO] Executing action 531/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[07:55:41]] [SUCCESS] Screenshot refreshed successfully
[[07:55:41]] [SUCCESS] Screenshot refreshed successfully
[[07:55:41]] [SUCCESS] Screenshot refreshed
[[07:55:41]] [INFO] Refreshing screenshot...
[[07:55:41]] [INFO] 5e4LeoW1YU=pass
[[07:55:36]] [SUCCESS] Screenshot refreshed successfully
[[07:55:36]] [SUCCESS] Screenshot refreshed successfully
[[07:55:36]] [INFO] 5e4LeoW1YU=running
[[07:55:36]] [INFO] Executing action 530/641: Restart app: env[appid]
[[07:55:36]] [SUCCESS] Screenshot refreshed
[[07:55:36]] [INFO] Refreshing screenshot...
[[07:55:35]] [SUCCESS] Screenshot refreshed successfully
[[07:55:35]] [SUCCESS] Screenshot refreshed successfully
[[07:55:35]] [SUCCESS] Screenshot refreshed
[[07:55:35]] [INFO] Refreshing screenshot...
[[07:55:17]] [INFO] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[07:55:17]] [SUCCESS] Screenshot refreshed successfully
[[07:55:17]] [SUCCESS] Screenshot refreshed successfully
[[07:55:17]] [SUCCESS] Screenshot refreshed
[[07:55:17]] [INFO] Refreshing screenshot...
[[07:54:33]] [INFO] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
[[07:54:32]] [SUCCESS] Screenshot refreshed successfully
[[07:54:32]] [SUCCESS] Screenshot refreshed successfully
[[07:54:32]] [SUCCESS] Screenshot refreshed
[[07:54:32]] [INFO] Refreshing screenshot...
[[07:54:15]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[07:54:15]] [SUCCESS] Screenshot refreshed successfully
[[07:54:15]] [SUCCESS] Screenshot refreshed successfully
[[07:54:14]] [SUCCESS] Screenshot refreshed
[[07:54:14]] [INFO] Refreshing screenshot...
[[07:53:30]] [INFO] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
[[07:53:30]] [SUCCESS] Screenshot refreshed successfully
[[07:53:30]] [SUCCESS] Screenshot refreshed successfully
[[07:53:30]] [SUCCESS] Screenshot refreshed
[[07:53:30]] [INFO] Refreshing screenshot...
[[07:53:12]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[07:53:12]] [SUCCESS] Screenshot refreshed successfully
[[07:53:12]] [SUCCESS] Screenshot refreshed successfully
[[07:53:12]] [SUCCESS] Screenshot refreshed
[[07:53:12]] [INFO] Refreshing screenshot...
[[07:52:27]] [INFO] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
[[07:52:27]] [SUCCESS] Screenshot refreshed successfully
[[07:52:27]] [SUCCESS] Screenshot refreshed successfully
[[07:52:26]] [SUCCESS] Screenshot refreshed
[[07:52:26]] [INFO] Refreshing screenshot...
[[07:52:09]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[07:52:08]] [SUCCESS] Screenshot refreshed successfully
[[07:52:08]] [SUCCESS] Screenshot refreshed successfully
[[07:52:08]] [SUCCESS] Screenshot refreshed
[[07:52:08]] [INFO] Refreshing screenshot...
[[07:51:21]] [INFO] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
[[07:51:21]] [INFO] Loaded 8 steps from test case: Click_Paginations
[[07:51:21]] [INFO] Loading steps for multiStep action: Click_Paginations
[[07:51:21]] [INFO] Z86xBjGUKY=running
[[07:51:21]] [INFO] Executing action 529/641: Execute Test Case: Click_Paginations (8 steps)
[[07:51:21]] [SUCCESS] Screenshot refreshed successfully
[[07:51:21]] [SUCCESS] Screenshot refreshed successfully
[[07:51:21]] [SUCCESS] Screenshot refreshed
[[07:51:21]] [INFO] Refreshing screenshot...
[[07:51:21]] [INFO] IL6kON0uQ9=pass
[[07:51:17]] [INFO] IL6kON0uQ9=running
[[07:51:17]] [INFO] Executing action 528/641: iOS Function: text - Text: "kids toys"
[[07:51:16]] [SUCCESS] Screenshot refreshed successfully
[[07:51:16]] [SUCCESS] Screenshot refreshed successfully
[[07:51:16]] [SUCCESS] Screenshot refreshed
[[07:51:16]] [INFO] Refreshing screenshot...
[[07:51:16]] [INFO] 6G6P3UE7Uy=pass
[[07:51:11]] [INFO] 6G6P3UE7Uy=running
[[07:51:11]] [INFO] Executing action 527/641: Tap on Text: "Find"
[[07:51:11]] [SUCCESS] Screenshot refreshed successfully
[[07:51:11]] [SUCCESS] Screenshot refreshed successfully
[[07:51:11]] [SUCCESS] Screenshot refreshed
[[07:51:11]] [INFO] Refreshing screenshot...
[[07:51:11]] [INFO] 7xs3GiydGF=pass
[[07:51:07]] [INFO] 7xs3GiydGF=running
[[07:51:07]] [INFO] Executing action 526/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:51:06]] [SUCCESS] Screenshot refreshed successfully
[[07:51:06]] [SUCCESS] Screenshot refreshed successfully
[[07:51:06]] [SUCCESS] Screenshot refreshed
[[07:51:06]] [INFO] Refreshing screenshot...
[[07:51:06]] [INFO] VqSa9z9R2Q=pass
[[07:51:05]] [INFO] VqSa9z9R2Q=running
[[07:51:05]] [INFO] Executing action 525/641: Launch app: env[appid]
[[07:51:04]] [SUCCESS] Screenshot refreshed successfully
[[07:51:04]] [SUCCESS] Screenshot refreshed successfully
[[07:51:04]] [SUCCESS] Screenshot refreshed
[[07:51:04]] [INFO] Refreshing screenshot...
[[07:51:04]] [INFO] RHEU77LRMw=pass
[[07:51:01]] [INFO] RHEU77LRMw=running
[[07:51:01]] [INFO] Executing action 524/641: Tap on Text: "+61"
[[07:51:00]] [SUCCESS] Screenshot refreshed successfully
[[07:51:00]] [SUCCESS] Screenshot refreshed successfully
[[07:51:00]] [SUCCESS] Screenshot refreshed
[[07:51:00]] [INFO] Refreshing screenshot...
[[07:51:00]] [INFO] MTRbUlaRvI=pass
[[07:50:56]] [INFO] MTRbUlaRvI=running
[[07:50:56]] [INFO] Executing action 523/641: Tap on Text: "1800"
[[07:50:56]] [SUCCESS] Screenshot refreshed successfully
[[07:50:56]] [SUCCESS] Screenshot refreshed successfully
[[07:50:55]] [SUCCESS] Screenshot refreshed
[[07:50:55]] [INFO] Refreshing screenshot...
[[07:50:55]] [INFO] I0tM87Yjhc=pass
[[07:50:51]] [INFO] I0tM87Yjhc=running
[[07:50:51]] [INFO] Executing action 522/641: Tap on Text: "click"
[[07:50:51]] [SUCCESS] Screenshot refreshed successfully
[[07:50:51]] [SUCCESS] Screenshot refreshed successfully
[[07:50:51]] [SUCCESS] Screenshot refreshed
[[07:50:51]] [INFO] Refreshing screenshot...
[[07:50:51]] [INFO] t6L5vWfBYM=pass
[[07:50:31]] [INFO] t6L5vWfBYM=running
[[07:50:31]] [INFO] Executing action 521/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:50:30]] [SUCCESS] Screenshot refreshed successfully
[[07:50:30]] [SUCCESS] Screenshot refreshed successfully
[[07:50:30]] [SUCCESS] Screenshot refreshed
[[07:50:30]] [INFO] Refreshing screenshot...
[[07:50:30]] [INFO] DhFJzlme9K=pass
[[07:50:26]] [INFO] DhFJzlme9K=running
[[07:50:26]] [INFO] Executing action 520/641: Tap on Text: "FAQ"
[[07:50:26]] [SUCCESS] Screenshot refreshed successfully
[[07:50:26]] [SUCCESS] Screenshot refreshed successfully
[[07:50:26]] [SUCCESS] Screenshot refreshed
[[07:50:26]] [INFO] Refreshing screenshot...
[[07:50:26]] [INFO] g17Boaefhg=pass
[[07:50:22]] [INFO] g17Boaefhg=running
[[07:50:22]] [INFO] Executing action 519/641: Tap on Text: "Help"
[[07:50:22]] [SUCCESS] Screenshot refreshed successfully
[[07:50:22]] [SUCCESS] Screenshot refreshed successfully
[[07:50:21]] [SUCCESS] Screenshot refreshed
[[07:50:21]] [INFO] Refreshing screenshot...
[[07:50:21]] [INFO] nPp27xJcCn=pass
[[07:50:09]] [INFO] nPp27xJcCn=running
[[07:50:09]] [INFO] Executing action 518/641: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[07:50:08]] [SUCCESS] Screenshot refreshed successfully
[[07:50:08]] [SUCCESS] Screenshot refreshed successfully
[[07:50:08]] [SUCCESS] Screenshot refreshed
[[07:50:08]] [INFO] Refreshing screenshot...
[[07:50:08]] [INFO] SqDiBhmyOG=pass
[[07:50:05]] [INFO] SqDiBhmyOG=running
[[07:50:05]] [INFO] Executing action 517/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:50:04]] [SUCCESS] Screenshot refreshed successfully
[[07:50:04]] [SUCCESS] Screenshot refreshed successfully
[[07:50:04]] [SUCCESS] Screenshot refreshed
[[07:50:04]] [INFO] Refreshing screenshot...
[[07:50:04]] [INFO] OR0SKKnFxy=pass
[[07:49:51]] [SUCCESS] Screenshot refreshed successfully
[[07:49:51]] [SUCCESS] Screenshot refreshed successfully
[[07:49:50]] [INFO] OR0SKKnFxy=running
[[07:49:50]] [INFO] Executing action 516/641: Restart app: env[appid]
[[07:49:50]] [SUCCESS] Screenshot refreshed
[[07:49:50]] [INFO] Refreshing screenshot...
[[07:49:50]] [SUCCESS] Screenshot refreshed successfully
[[07:49:50]] [SUCCESS] Screenshot refreshed successfully
[[07:49:50]] [SUCCESS] Screenshot refreshed
[[07:49:50]] [INFO] Refreshing screenshot...
[[07:49:47]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:49:47]] [SUCCESS] Screenshot refreshed successfully
[[07:49:47]] [SUCCESS] Screenshot refreshed successfully
[[07:49:47]] [SUCCESS] Screenshot refreshed
[[07:49:47]] [INFO] Refreshing screenshot...
[[07:49:34]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:49:34]] [SUCCESS] Screenshot refreshed successfully
[[07:49:34]] [SUCCESS] Screenshot refreshed successfully
[[07:49:34]] [SUCCESS] Screenshot refreshed
[[07:49:34]] [INFO] Refreshing screenshot...
[[07:49:30]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:49:30]] [SUCCESS] Screenshot refreshed successfully
[[07:49:30]] [SUCCESS] Screenshot refreshed successfully
[[07:49:30]] [SUCCESS] Screenshot refreshed
[[07:49:30]] [INFO] Refreshing screenshot...
[[07:49:26]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:49:26]] [SUCCESS] Screenshot refreshed successfully
[[07:49:26]] [SUCCESS] Screenshot refreshed successfully
[[07:49:26]] [SUCCESS] Screenshot refreshed
[[07:49:26]] [INFO] Refreshing screenshot...
[[07:49:19]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:49:19]] [SUCCESS] Screenshot refreshed successfully
[[07:49:19]] [SUCCESS] Screenshot refreshed successfully
[[07:49:19]] [SUCCESS] Screenshot refreshed
[[07:49:19]] [INFO] Refreshing screenshot...
[[07:49:12]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:49:12]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:49:12]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:49:12]] [INFO] kPdSiomhwu=running
[[07:49:12]] [INFO] Executing action 515/641: cleanupSteps action
[[07:49:11]] [SUCCESS] Screenshot refreshed successfully
[[07:49:11]] [SUCCESS] Screenshot refreshed successfully
[[07:49:11]] [SUCCESS] Screenshot refreshed
[[07:49:11]] [INFO] Refreshing screenshot...
[[07:49:11]] [INFO] Qb1AArnpCH=pass
[[07:49:05]] [INFO] Qb1AArnpCH=running
[[07:49:05]] [INFO] Executing action 514/641: Wait for 5 ms
[[07:49:05]] [SUCCESS] Screenshot refreshed successfully
[[07:49:05]] [SUCCESS] Screenshot refreshed successfully
[[07:49:04]] [SUCCESS] Screenshot refreshed
[[07:49:04]] [INFO] Refreshing screenshot...
[[07:49:04]] [INFO] yxlzTytgFT=pass
[[07:48:59]] [INFO] yxlzTytgFT=running
[[07:48:59]] [INFO] Executing action 513/641: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Remove")]"
[[07:48:59]] [SUCCESS] Screenshot refreshed successfully
[[07:48:59]] [SUCCESS] Screenshot refreshed successfully
[[07:48:59]] [SUCCESS] Screenshot refreshed
[[07:48:59]] [INFO] Refreshing screenshot...
[[07:48:59]] [INFO] K2w7X1cPdH=pass
[[07:48:54]] [INFO] K2w7X1cPdH=running
[[07:48:54]] [INFO] Executing action 512/641: Swipe from (50%, 50%) to (50%, 30%)
[[07:48:53]] [SUCCESS] Screenshot refreshed successfully
[[07:48:53]] [SUCCESS] Screenshot refreshed successfully
[[07:48:53]] [SUCCESS] Screenshot refreshed
[[07:48:53]] [INFO] Refreshing screenshot...
[[07:48:53]] [INFO] P26OyuqWlb=pass
[[07:48:41]] [INFO] P26OyuqWlb=running
[[07:48:41]] [INFO] Executing action 511/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:48:41]] [SUCCESS] Screenshot refreshed successfully
[[07:48:41]] [SUCCESS] Screenshot refreshed successfully
[[07:48:41]] [SUCCESS] Screenshot refreshed
[[07:48:41]] [INFO] Refreshing screenshot...
[[07:48:41]] [INFO] UpUSVInizv=pass
[[07:48:37]] [INFO] UpUSVInizv=running
[[07:48:37]] [INFO] Executing action 510/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[07:48:37]] [SUCCESS] Screenshot refreshed successfully
[[07:48:37]] [SUCCESS] Screenshot refreshed successfully
[[07:48:36]] [SUCCESS] Screenshot refreshed
[[07:48:36]] [INFO] Refreshing screenshot...
[[07:48:36]] [INFO] c4T3INQkzn=pass
[[07:48:32]] [INFO] c4T3INQkzn=running
[[07:48:32]] [INFO] Executing action 509/641: Restart app: env[appid]
[[07:48:31]] [SUCCESS] Screenshot refreshed successfully
[[07:48:31]] [SUCCESS] Screenshot refreshed successfully
[[07:48:31]] [SUCCESS] Screenshot refreshed
[[07:48:31]] [INFO] Refreshing screenshot...
[[07:48:31]] [INFO] Teyz3d55XS=pass
[[07:48:23]] [INFO] Teyz3d55XS=running
[[07:48:23]] [INFO] Executing action 508/641: Tap if locator exists: accessibility_id="Add to bag"
[[07:48:22]] [SUCCESS] Screenshot refreshed successfully
[[07:48:22]] [SUCCESS] Screenshot refreshed successfully
[[07:48:22]] [SUCCESS] Screenshot refreshed
[[07:48:22]] [INFO] Refreshing screenshot...
[[07:48:22]] [INFO] MA2re5cDWr=pass
[[07:48:16]] [INFO] MA2re5cDWr=running
[[07:48:16]] [INFO] Executing action 507/641: Swipe from (50%, 50%) to (50%, 30%)
[[07:48:16]] [SUCCESS] Screenshot refreshed successfully
[[07:48:16]] [SUCCESS] Screenshot refreshed successfully
[[07:48:16]] [SUCCESS] Screenshot refreshed
[[07:48:16]] [INFO] Refreshing screenshot...
[[07:48:16]] [INFO] 2hGhWulI52=pass
[[07:48:12]] [INFO] 2hGhWulI52=running
[[07:48:12]] [INFO] Executing action 506/641: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[07:48:12]] [SUCCESS] Screenshot refreshed successfully
[[07:48:12]] [SUCCESS] Screenshot refreshed successfully
[[07:48:12]] [SUCCESS] Screenshot refreshed
[[07:48:12]] [INFO] Refreshing screenshot...
[[07:48:12]] [INFO] n57KEWjTea=pass
[[07:48:08]] [INFO] n57KEWjTea=running
[[07:48:08]] [INFO] Executing action 505/641: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[07:48:07]] [SUCCESS] Screenshot refreshed successfully
[[07:48:07]] [SUCCESS] Screenshot refreshed successfully
[[07:48:07]] [SUCCESS] Screenshot refreshed
[[07:48:07]] [INFO] Refreshing screenshot...
[[07:48:07]] [INFO] L59V5hqMX9=pass
[[07:48:03]] [INFO] L59V5hqMX9=running
[[07:48:03]] [INFO] Executing action 504/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[07:48:03]] [SUCCESS] Screenshot refreshed successfully
[[07:48:03]] [SUCCESS] Screenshot refreshed successfully
[[07:48:03]] [SUCCESS] Screenshot refreshed
[[07:48:03]] [INFO] Refreshing screenshot...
[[07:48:03]] [INFO] OKiI82VdnE=pass
[[07:47:56]] [INFO] OKiI82VdnE=running
[[07:47:56]] [INFO] Executing action 503/641: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[07:47:56]] [SUCCESS] Screenshot refreshed successfully
[[07:47:56]] [SUCCESS] Screenshot refreshed successfully
[[07:47:56]] [SUCCESS] Screenshot refreshed
[[07:47:56]] [INFO] Refreshing screenshot...
[[07:47:56]] [INFO] 3KNqlNy6Bj=pass
[[07:47:51]] [INFO] 3KNqlNy6Bj=running
[[07:47:51]] [INFO] Executing action 502/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[07:47:51]] [SUCCESS] Screenshot refreshed successfully
[[07:47:51]] [SUCCESS] Screenshot refreshed successfully
[[07:47:51]] [SUCCESS] Screenshot refreshed
[[07:47:51]] [INFO] Refreshing screenshot...
[[07:47:51]] [INFO] 3NOS1fbxZs=pass
[[07:47:47]] [INFO] 3NOS1fbxZs=running
[[07:47:47]] [INFO] Executing action 501/641: Tap on image: banner-close-updated.png
[[07:47:47]] [SUCCESS] Screenshot refreshed successfully
[[07:47:47]] [SUCCESS] Screenshot refreshed successfully
[[07:47:47]] [SUCCESS] Screenshot refreshed
[[07:47:47]] [INFO] Refreshing screenshot...
[[07:47:47]] [INFO] K0c1gL9UK1=pass
[[07:47:43]] [INFO] K0c1gL9UK1=running
[[07:47:43]] [INFO] Executing action 500/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:47:42]] [SUCCESS] Screenshot refreshed successfully
[[07:47:42]] [SUCCESS] Screenshot refreshed successfully
[[07:47:42]] [SUCCESS] Screenshot refreshed
[[07:47:42]] [INFO] Refreshing screenshot...
[[07:47:42]] [INFO] IW6uAwdtiW=pass
[[07:47:38]] [INFO] IW6uAwdtiW=running
[[07:47:38]] [INFO] Executing action 499/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[07:47:38]] [SUCCESS] Screenshot refreshed successfully
[[07:47:38]] [SUCCESS] Screenshot refreshed successfully
[[07:47:37]] [SUCCESS] Screenshot refreshed
[[07:47:37]] [INFO] Refreshing screenshot...
[[07:47:37]] [INFO] DbM0d0m6rU=pass
[[07:47:33]] [INFO] DbM0d0m6rU=running
[[07:47:33]] [INFO] Executing action 498/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[07:47:33]] [SUCCESS] Screenshot refreshed successfully
[[07:47:33]] [SUCCESS] Screenshot refreshed successfully
[[07:47:33]] [SUCCESS] Screenshot refreshed
[[07:47:33]] [INFO] Refreshing screenshot...
[[07:47:33]] [INFO] saiPPHQSPa=pass
[[07:47:20]] [INFO] saiPPHQSPa=running
[[07:47:20]] [INFO] Executing action 497/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:47:20]] [SUCCESS] Screenshot refreshed successfully
[[07:47:20]] [SUCCESS] Screenshot refreshed successfully
[[07:47:20]] [SUCCESS] Screenshot refreshed
[[07:47:20]] [INFO] Refreshing screenshot...
[[07:47:20]] [INFO] UpUSVInizv=pass
[[07:47:16]] [INFO] UpUSVInizv=running
[[07:47:16]] [INFO] Executing action 496/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[07:47:15]] [SUCCESS] Screenshot refreshed successfully
[[07:47:15]] [SUCCESS] Screenshot refreshed successfully
[[07:47:15]] [SUCCESS] Screenshot refreshed
[[07:47:15]] [INFO] Refreshing screenshot...
[[07:47:15]] [INFO] Iab9zCfpqO=pass
[[07:46:57]] [INFO] Iab9zCfpqO=running
[[07:46:57]] [INFO] Executing action 495/641: Tap on element with accessibility_id: Add to bag
[[07:46:56]] [SUCCESS] Screenshot refreshed successfully
[[07:46:56]] [SUCCESS] Screenshot refreshed successfully
[[07:46:56]] [SUCCESS] Screenshot refreshed
[[07:46:56]] [INFO] Refreshing screenshot...
[[07:46:56]] [INFO] Qy0Y0uJchm=pass
[[07:46:53]] [INFO] Qy0Y0uJchm=running
[[07:46:53]] [INFO] Executing action 494/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[07:46:52]] [SUCCESS] Screenshot refreshed successfully
[[07:46:52]] [SUCCESS] Screenshot refreshed successfully
[[07:46:52]] [SUCCESS] Screenshot refreshed
[[07:46:52]] [INFO] Refreshing screenshot...
[[07:46:52]] [INFO] YHaMIjULRf=pass
[[07:46:47]] [INFO] YHaMIjULRf=running
[[07:46:47]] [INFO] Executing action 493/641: Tap on Text: "List"
[[07:46:47]] [SUCCESS] Screenshot refreshed successfully
[[07:46:47]] [SUCCESS] Screenshot refreshed successfully
[[07:46:46]] [SUCCESS] Screenshot refreshed
[[07:46:46]] [INFO] Refreshing screenshot...
[[07:46:46]] [INFO] igReeDqips=pass
[[07:46:42]] [INFO] igReeDqips=running
[[07:46:42]] [INFO] Executing action 492/641: Tap on image: env[catalogue-menu-img]
[[07:46:41]] [SUCCESS] Screenshot refreshed successfully
[[07:46:41]] [SUCCESS] Screenshot refreshed successfully
[[07:46:41]] [SUCCESS] Screenshot refreshed
[[07:46:41]] [INFO] Refreshing screenshot...
[[07:46:41]] [INFO] gcSsGpqKwk=pass
[[07:46:18]] [INFO] gcSsGpqKwk=running
[[07:46:18]] [INFO] Executing action 491/641: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[07:46:18]] [SUCCESS] Screenshot refreshed successfully
[[07:46:18]] [SUCCESS] Screenshot refreshed successfully
[[07:46:18]] [SUCCESS] Screenshot refreshed
[[07:46:18]] [INFO] Refreshing screenshot...
[[07:46:18]] [INFO] gkkQzTCmma=pass
[[07:46:13]] [INFO] gkkQzTCmma=running
[[07:46:13]] [INFO] Executing action 490/641: Tap on Text: "Catalogue"
[[07:46:13]] [SUCCESS] Screenshot refreshed successfully
[[07:46:13]] [SUCCESS] Screenshot refreshed successfully
[[07:46:13]] [SUCCESS] Screenshot refreshed
[[07:46:13]] [INFO] Refreshing screenshot...
[[07:46:13]] [INFO] VpOhIxEl53=pass
[[07:46:00]] [INFO] VpOhIxEl53=running
[[07:46:00]] [INFO] Executing action 489/641: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[07:46:00]] [SUCCESS] Screenshot refreshed successfully
[[07:46:00]] [SUCCESS] Screenshot refreshed successfully
[[07:46:00]] [SUCCESS] Screenshot refreshed
[[07:46:00]] [INFO] Refreshing screenshot...
[[07:46:00]] [INFO] UpUSVInizv=pass
[[07:45:55]] [INFO] UpUSVInizv=running
[[07:45:55]] [INFO] Executing action 488/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[07:45:55]] [SUCCESS] Screenshot refreshed successfully
[[07:45:55]] [SUCCESS] Screenshot refreshed successfully
[[07:45:55]] [SUCCESS] Screenshot refreshed
[[07:45:55]] [INFO] Refreshing screenshot...
[[07:45:55]] [INFO] Cmvm82hiAa=pass
[[07:45:48]] [INFO] Cmvm82hiAa=running
[[07:45:48]] [INFO] Executing action 487/641: Tap on element with accessibility_id: Add to bag
[[07:45:47]] [SUCCESS] Screenshot refreshed successfully
[[07:45:47]] [SUCCESS] Screenshot refreshed successfully
[[07:45:47]] [SUCCESS] Screenshot refreshed
[[07:45:47]] [INFO] Refreshing screenshot...
[[07:45:47]] [INFO] ZZPNqTJ65s=pass
[[07:45:42]] [INFO] ZZPNqTJ65s=running
[[07:45:42]] [INFO] Executing action 486/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:45:42]] [SUCCESS] Screenshot refreshed successfully
[[07:45:42]] [SUCCESS] Screenshot refreshed successfully
[[07:45:42]] [SUCCESS] Screenshot refreshed
[[07:45:42]] [INFO] Refreshing screenshot...
[[07:45:42]] [INFO] JcAR0JctQ6=pass
[[07:45:38]] [INFO] JcAR0JctQ6=running
[[07:45:38]] [INFO] Executing action 485/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[07:45:37]] [SUCCESS] Screenshot refreshed successfully
[[07:45:37]] [SUCCESS] Screenshot refreshed successfully
[[07:45:37]] [SUCCESS] Screenshot refreshed
[[07:45:37]] [INFO] Refreshing screenshot...
[[07:45:37]] [INFO] Pd7cReoJM6=pass
[[07:45:32]] [INFO] Pd7cReoJM6=running
[[07:45:32]] [INFO] Executing action 484/641: Tap on Text: "List"
[[07:45:32]] [SUCCESS] Screenshot refreshed successfully
[[07:45:32]] [SUCCESS] Screenshot refreshed successfully
[[07:45:31]] [SUCCESS] Screenshot refreshed
[[07:45:31]] [INFO] Refreshing screenshot...
[[07:45:31]] [INFO] igReeDqips=pass
[[07:45:27]] [INFO] igReeDqips=running
[[07:45:27]] [INFO] Executing action 483/641: Tap on image: env[catalogue-menu-img]
[[07:45:27]] [SUCCESS] Screenshot refreshed successfully
[[07:45:27]] [SUCCESS] Screenshot refreshed successfully
[[07:45:26]] [SUCCESS] Screenshot refreshed
[[07:45:26]] [INFO] Refreshing screenshot...
[[07:45:26]] [INFO] Jh6RTFWeOU=pass
[[07:45:04]] [INFO] Jh6RTFWeOU=running
[[07:45:04]] [INFO] Executing action 482/641: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[07:45:03]] [SUCCESS] Screenshot refreshed successfully
[[07:45:03]] [SUCCESS] Screenshot refreshed successfully
[[07:45:03]] [SUCCESS] Screenshot refreshed
[[07:45:03]] [INFO] Refreshing screenshot...
[[07:45:03]] [INFO] gkkQzTCmma=pass
[[07:44:59]] [INFO] gkkQzTCmma=running
[[07:44:59]] [INFO] Executing action 481/641: Tap on Text: "Catalogue"
[[07:44:58]] [SUCCESS] Screenshot refreshed successfully
[[07:44:58]] [SUCCESS] Screenshot refreshed successfully
[[07:44:58]] [SUCCESS] Screenshot refreshed
[[07:44:58]] [INFO] Refreshing screenshot...
[[07:44:58]] [INFO] QUeGIASAxV=pass
[[07:44:55]] [INFO] QUeGIASAxV=running
[[07:44:55]] [INFO] Executing action 480/641: Swipe from (50%, 50%) to (50%, 30%)
[[07:44:54]] [SUCCESS] Screenshot refreshed successfully
[[07:44:54]] [SUCCESS] Screenshot refreshed successfully
[[07:44:54]] [SUCCESS] Screenshot refreshed
[[07:44:54]] [INFO] Refreshing screenshot...
[[07:44:54]] [INFO] UpUSVInizv=pass
[[07:44:50]] [INFO] UpUSVInizv=running
[[07:44:50]] [INFO] Executing action 479/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[07:44:50]] [SUCCESS] Screenshot refreshed successfully
[[07:44:50]] [SUCCESS] Screenshot refreshed successfully
[[07:44:50]] [SUCCESS] Screenshot refreshed
[[07:44:50]] [INFO] Refreshing screenshot...
[[07:44:50]] [INFO] 0QtNHB5WEK=pass
[[07:44:47]] [INFO] 0QtNHB5WEK=running
[[07:44:47]] [INFO] Executing action 478/641: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[07:44:47]] [SUCCESS] Screenshot refreshed successfully
[[07:44:47]] [SUCCESS] Screenshot refreshed successfully
[[07:44:46]] [SUCCESS] Screenshot refreshed
[[07:44:46]] [INFO] Refreshing screenshot...
[[07:44:46]] [INFO] fTdGMJ3NH3=pass
[[07:44:43]] [INFO] fTdGMJ3NH3=running
[[07:44:43]] [INFO] Executing action 477/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[07:44:43]] [SUCCESS] Screenshot refreshed successfully
[[07:44:43]] [SUCCESS] Screenshot refreshed successfully
[[07:44:43]] [SUCCESS] Screenshot refreshed
[[07:44:43]] [INFO] Refreshing screenshot...
[[07:44:43]] [INFO] rYJcLPh8Aq=pass
[[07:44:39]] [INFO] rYJcLPh8Aq=running
[[07:44:39]] [INFO] Executing action 476/641: iOS Function: text - Text: "kmart au"
[[07:44:39]] [SUCCESS] Screenshot refreshed successfully
[[07:44:39]] [SUCCESS] Screenshot refreshed successfully
[[07:44:39]] [SUCCESS] Screenshot refreshed
[[07:44:39]] [INFO] Refreshing screenshot...
[[07:44:39]] [INFO] 0Q0fm6OTij=pass
[[07:44:36]] [INFO] 0Q0fm6OTij=running
[[07:44:36]] [INFO] Executing action 475/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[07:44:36]] [SUCCESS] Screenshot refreshed successfully
[[07:44:36]] [SUCCESS] Screenshot refreshed successfully
[[07:44:36]] [SUCCESS] Screenshot refreshed
[[07:44:36]] [INFO] Refreshing screenshot...
[[07:44:36]] [INFO] xVuuejtCFA=pass
[[07:44:31]] [INFO] xVuuejtCFA=running
[[07:44:31]] [INFO] Executing action 474/641: Restart app: com.apple.mobilesafari
[[07:44:31]] [SUCCESS] Screenshot refreshed successfully
[[07:44:31]] [SUCCESS] Screenshot refreshed successfully
[[07:44:31]] [SUCCESS] Screenshot refreshed
[[07:44:31]] [INFO] Refreshing screenshot...
[[07:44:31]] [INFO] LcYLwUffqj=pass
[[07:44:27]] [INFO] LcYLwUffqj=running
[[07:44:27]] [INFO] Executing action 473/641: Tap on Text: "out"
[[07:44:26]] [SUCCESS] Screenshot refreshed successfully
[[07:44:26]] [SUCCESS] Screenshot refreshed successfully
[[07:44:26]] [SUCCESS] Screenshot refreshed
[[07:44:26]] [INFO] Refreshing screenshot...
[[07:44:26]] [INFO] ZZPNqTJ65s=pass
[[07:44:22]] [INFO] ZZPNqTJ65s=running
[[07:44:22]] [INFO] Executing action 472/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:44:22]] [SUCCESS] Screenshot refreshed successfully
[[07:44:22]] [SUCCESS] Screenshot refreshed successfully
[[07:44:22]] [SUCCESS] Screenshot refreshed
[[07:44:22]] [INFO] Refreshing screenshot...
[[07:44:22]] [INFO] UpUSVInizv=pass
[[07:44:18]] [INFO] UpUSVInizv=running
[[07:44:18]] [INFO] Executing action 471/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[07:44:18]] [SUCCESS] Screenshot refreshed successfully
[[07:44:18]] [SUCCESS] Screenshot refreshed successfully
[[07:44:17]] [SUCCESS] Screenshot refreshed
[[07:44:17]] [INFO] Refreshing screenshot...
[[07:44:17]] [INFO] hCCEvRtj1A=pass
[[07:44:12]] [INFO] hCCEvRtj1A=running
[[07:44:12]] [INFO] Executing action 470/641: Restart app: env[appid]
[[07:44:12]] [SUCCESS] Screenshot refreshed successfully
[[07:44:12]] [SUCCESS] Screenshot refreshed successfully
[[07:44:12]] [SUCCESS] Screenshot refreshed
[[07:44:12]] [INFO] Refreshing screenshot...
[[07:44:12]] [INFO] V42eHtTRYW=pass
[[07:44:06]] [INFO] V42eHtTRYW=running
[[07:44:06]] [INFO] Executing action 469/641: Wait for 5 ms
[[07:44:05]] [SUCCESS] Screenshot refreshed successfully
[[07:44:05]] [SUCCESS] Screenshot refreshed successfully
[[07:44:05]] [SUCCESS] Screenshot refreshed
[[07:44:05]] [INFO] Refreshing screenshot...
[[07:44:05]] [INFO] GRwHMVK4sA=pass
[[07:44:03]] [INFO] GRwHMVK4sA=running
[[07:44:03]] [INFO] Executing action 468/641: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[07:44:03]] [SUCCESS] Screenshot refreshed successfully
[[07:44:03]] [SUCCESS] Screenshot refreshed successfully
[[07:44:03]] [SUCCESS] Screenshot refreshed
[[07:44:03]] [INFO] Refreshing screenshot...
[[07:44:03]] [INFO] V42eHtTRYW=pass
[[07:43:56]] [INFO] V42eHtTRYW=running
[[07:43:56]] [INFO] Executing action 467/641: Wait for 5 ms
[[07:43:56]] [SUCCESS] Screenshot refreshed successfully
[[07:43:56]] [SUCCESS] Screenshot refreshed successfully
[[07:43:56]] [SUCCESS] Screenshot refreshed
[[07:43:56]] [INFO] Refreshing screenshot...
[[07:43:56]] [INFO] LfyQctrEJn=pass
[[07:43:54]] [INFO] LfyQctrEJn=running
[[07:43:54]] [INFO] Executing action 466/641: Launch app: com.apple.Preferences
[[07:43:54]] [SUCCESS] Screenshot refreshed successfully
[[07:43:54]] [SUCCESS] Screenshot refreshed successfully
[[07:43:54]] [SUCCESS] Screenshot refreshed
[[07:43:54]] [INFO] Refreshing screenshot...
[[07:43:54]] [INFO] seQcUKjkSU=pass
[[07:43:52]] [INFO] seQcUKjkSU=running
[[07:43:52]] [INFO] Executing action 465/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:43:52]] [SUCCESS] Screenshot refreshed successfully
[[07:43:52]] [SUCCESS] Screenshot refreshed successfully
[[07:43:52]] [SUCCESS] Screenshot refreshed
[[07:43:52]] [INFO] Refreshing screenshot...
[[07:43:52]] [INFO] UpUSVInizv=pass
[[07:43:50]] [INFO] UpUSVInizv=running
[[07:43:50]] [INFO] Executing action 464/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[07:43:49]] [SUCCESS] Screenshot refreshed successfully
[[07:43:49]] [SUCCESS] Screenshot refreshed successfully
[[07:43:49]] [SUCCESS] Screenshot refreshed
[[07:43:49]] [INFO] Refreshing screenshot...
[[07:43:49]] [INFO] WoymrHdtrO=pass
[[07:43:48]] [INFO] WoymrHdtrO=running
[[07:43:48]] [INFO] Executing action 463/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:43:47]] [SUCCESS] Screenshot refreshed successfully
[[07:43:47]] [SUCCESS] Screenshot refreshed successfully
[[07:43:47]] [SUCCESS] Screenshot refreshed
[[07:43:47]] [INFO] Refreshing screenshot...
[[07:43:47]] [INFO] 6xgrAWyfZ4=pass
[[07:43:45]] [INFO] 6xgrAWyfZ4=running
[[07:43:45]] [INFO] Executing action 462/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[07:43:45]] [SUCCESS] Screenshot refreshed successfully
[[07:43:45]] [SUCCESS] Screenshot refreshed successfully
[[07:43:45]] [SUCCESS] Screenshot refreshed
[[07:43:45]] [INFO] Refreshing screenshot...
[[07:43:45]] [INFO] eSr9EFlJek=pass
[[07:43:43]] [INFO] eSr9EFlJek=running
[[07:43:43]] [INFO] Executing action 461/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:43:43]] [SUCCESS] Screenshot refreshed successfully
[[07:43:43]] [SUCCESS] Screenshot refreshed successfully
[[07:43:43]] [SUCCESS] Screenshot refreshed
[[07:43:43]] [INFO] Refreshing screenshot...
[[07:43:43]] [INFO] 3KNqlNy6Bj=pass
[[07:43:41]] [INFO] 3KNqlNy6Bj=running
[[07:43:41]] [INFO] Executing action 460/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[07:43:40]] [SUCCESS] Screenshot refreshed successfully
[[07:43:40]] [SUCCESS] Screenshot refreshed successfully
[[07:43:40]] [SUCCESS] Screenshot refreshed
[[07:43:40]] [INFO] Refreshing screenshot...
[[07:43:40]] [INFO] cokvFXhj4c=pass
[[07:43:38]] [INFO] cokvFXhj4c=running
[[07:43:38]] [INFO] Executing action 459/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:43:38]] [SUCCESS] Screenshot refreshed successfully
[[07:43:38]] [SUCCESS] Screenshot refreshed successfully
[[07:43:38]] [SUCCESS] Screenshot refreshed
[[07:43:38]] [INFO] Refreshing screenshot...
[[07:43:38]] [INFO] oSQ8sPdVOJ=pass
[[07:43:33]] [INFO] oSQ8sPdVOJ=running
[[07:43:33]] [INFO] Executing action 458/641: Restart app: env[appid]
[[07:43:33]] [SUCCESS] Screenshot refreshed successfully
[[07:43:33]] [SUCCESS] Screenshot refreshed successfully
[[07:43:33]] [SUCCESS] Screenshot refreshed
[[07:43:33]] [INFO] Refreshing screenshot...
[[07:43:33]] [INFO] V42eHtTRYW=pass
[[07:43:27]] [INFO] V42eHtTRYW=running
[[07:43:27]] [INFO] Executing action 457/641: Wait for 5 ms
[[07:43:26]] [SUCCESS] Screenshot refreshed successfully
[[07:43:26]] [SUCCESS] Screenshot refreshed successfully
[[07:43:26]] [SUCCESS] Screenshot refreshed
[[07:43:26]] [INFO] Refreshing screenshot...
[[07:43:26]] [INFO] jUCAk6GJc4=pass
[[07:43:24]] [INFO] jUCAk6GJc4=running
[[07:43:24]] [INFO] Executing action 456/641: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[07:43:23]] [SUCCESS] Screenshot refreshed successfully
[[07:43:23]] [SUCCESS] Screenshot refreshed successfully
[[07:43:23]] [SUCCESS] Screenshot refreshed
[[07:43:23]] [INFO] Refreshing screenshot...
[[07:43:23]] [INFO] V42eHtTRYW=pass
[[07:43:17]] [INFO] V42eHtTRYW=running
[[07:43:17]] [INFO] Executing action 455/641: Wait for 5 ms
[[07:43:17]] [SUCCESS] Screenshot refreshed successfully
[[07:43:17]] [SUCCESS] Screenshot refreshed successfully
[[07:43:16]] [SUCCESS] Screenshot refreshed
[[07:43:16]] [INFO] Refreshing screenshot...
[[07:43:16]] [INFO] w1RV76df9x=pass
[[07:43:12]] [INFO] w1RV76df9x=running
[[07:43:12]] [INFO] Executing action 454/641: Tap on Text: "Wi-Fi"
[[07:43:12]] [SUCCESS] Screenshot refreshed successfully
[[07:43:12]] [SUCCESS] Screenshot refreshed successfully
[[07:43:12]] [SUCCESS] Screenshot refreshed
[[07:43:12]] [INFO] Refreshing screenshot...
[[07:43:12]] [INFO] LfyQctrEJn=pass
[[07:43:09]] [INFO] LfyQctrEJn=running
[[07:43:09]] [INFO] Executing action 453/641: Launch app: com.apple.Preferences
[[07:43:09]] [SUCCESS] Screenshot refreshed successfully
[[07:43:09]] [SUCCESS] Screenshot refreshed successfully
[[07:43:09]] [SUCCESS] Screenshot refreshed
[[07:43:09]] [INFO] Refreshing screenshot...
[[07:43:09]] [INFO] mIKA85kXaW=pass
[[07:43:06]] [SUCCESS] Screenshot refreshed successfully
[[07:43:06]] [SUCCESS] Screenshot refreshed successfully
[[07:43:06]] [INFO] mIKA85kXaW=running
[[07:43:06]] [INFO] Executing action 452/641: Terminate app: com.apple.Preferences
[[07:43:06]] [SUCCESS] Screenshot refreshed
[[07:43:06]] [INFO] Refreshing screenshot...
[[07:43:06]] [SUCCESS] Screenshot refreshed successfully
[[07:43:06]] [SUCCESS] Screenshot refreshed successfully
[[07:43:06]] [SUCCESS] Screenshot refreshed
[[07:43:06]] [INFO] Refreshing screenshot...
[[07:43:01]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[07:43:01]] [SUCCESS] Screenshot refreshed successfully
[[07:43:01]] [SUCCESS] Screenshot refreshed successfully
[[07:43:01]] [SUCCESS] Screenshot refreshed
[[07:43:01]] [INFO] Refreshing screenshot...
[[07:42:57]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:42:56]] [SUCCESS] Screenshot refreshed successfully
[[07:42:56]] [SUCCESS] Screenshot refreshed successfully
[[07:42:56]] [SUCCESS] Screenshot refreshed
[[07:42:56]] [INFO] Refreshing screenshot...
[[07:42:52]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[07:42:51]] [SUCCESS] Screenshot refreshed successfully
[[07:42:51]] [SUCCESS] Screenshot refreshed successfully
[[07:42:51]] [SUCCESS] Screenshot refreshed
[[07:42:51]] [INFO] Refreshing screenshot...
[[07:42:47]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:42:47]] [SUCCESS] Screenshot refreshed successfully
[[07:42:47]] [SUCCESS] Screenshot refreshed successfully
[[07:42:47]] [SUCCESS] Screenshot refreshed
[[07:42:47]] [INFO] Refreshing screenshot...
[[07:42:41]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:42:41]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[07:42:41]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[07:42:41]] [INFO] gx05zu87DK=running
[[07:42:41]] [INFO] Executing action 451/641: Execute Test Case: Kmart-Signin (5 steps)
[[07:42:41]] [SUCCESS] Screenshot refreshed successfully
[[07:42:41]] [SUCCESS] Screenshot refreshed successfully
[[07:42:40]] [SUCCESS] Screenshot refreshed
[[07:42:40]] [INFO] Refreshing screenshot...
[[07:42:40]] [INFO] rJ86z4njuR=pass
[[07:42:38]] [INFO] rJ86z4njuR=running
[[07:42:38]] [INFO] Executing action 450/641: iOS Function: alert_accept
[[07:42:38]] [SUCCESS] Screenshot refreshed successfully
[[07:42:38]] [SUCCESS] Screenshot refreshed successfully
[[07:42:37]] [SUCCESS] Screenshot refreshed
[[07:42:37]] [INFO] Refreshing screenshot...
[[07:42:37]] [INFO] veukWo4573=pass
[[07:42:34]] [INFO] veukWo4573=running
[[07:42:34]] [INFO] Executing action 449/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[07:42:33]] [SUCCESS] Screenshot refreshed successfully
[[07:42:33]] [SUCCESS] Screenshot refreshed successfully
[[07:42:33]] [SUCCESS] Screenshot refreshed
[[07:42:33]] [INFO] Refreshing screenshot...
[[07:42:33]] [INFO] XEbZHdi0GT=pass
[[07:42:20]] [SUCCESS] Screenshot refreshed successfully
[[07:42:20]] [SUCCESS] Screenshot refreshed successfully
[[07:42:19]] [INFO] XEbZHdi0GT=running
[[07:42:19]] [INFO] Executing action 448/641: Restart app: env[appid]
[[07:42:19]] [SUCCESS] Screenshot refreshed
[[07:42:19]] [INFO] Refreshing screenshot...
[[07:42:19]] [SUCCESS] Screenshot refreshed successfully
[[07:42:19]] [SUCCESS] Screenshot refreshed successfully
[[07:42:19]] [SUCCESS] Screenshot refreshed
[[07:42:19]] [INFO] Refreshing screenshot...
[[07:42:17]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:42:16]] [SUCCESS] Screenshot refreshed successfully
[[07:42:16]] [SUCCESS] Screenshot refreshed successfully
[[07:42:16]] [SUCCESS] Screenshot refreshed
[[07:42:16]] [INFO] Refreshing screenshot...
[[07:42:03]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:42:03]] [SUCCESS] Screenshot refreshed successfully
[[07:42:03]] [SUCCESS] Screenshot refreshed successfully
[[07:42:03]] [SUCCESS] Screenshot refreshed
[[07:42:03]] [INFO] Refreshing screenshot...
[[07:41:59]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:41:59]] [SUCCESS] Screenshot refreshed successfully
[[07:41:59]] [SUCCESS] Screenshot refreshed successfully
[[07:41:59]] [SUCCESS] Screenshot refreshed
[[07:41:59]] [INFO] Refreshing screenshot...
[[07:41:55]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:41:55]] [SUCCESS] Screenshot refreshed successfully
[[07:41:55]] [SUCCESS] Screenshot refreshed successfully
[[07:41:55]] [SUCCESS] Screenshot refreshed
[[07:41:55]] [INFO] Refreshing screenshot...
[[07:41:48]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:41:48]] [SUCCESS] Screenshot refreshed successfully
[[07:41:48]] [SUCCESS] Screenshot refreshed successfully
[[07:41:48]] [SUCCESS] Screenshot refreshed
[[07:41:48]] [INFO] Refreshing screenshot...
[[07:41:41]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:41:41]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:41:41]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:41:41]] [INFO] ubySifeF65=running
[[07:41:41]] [INFO] Executing action 447/641: cleanupSteps action
[[07:41:41]] [SUCCESS] Screenshot refreshed successfully
[[07:41:41]] [SUCCESS] Screenshot refreshed successfully
[[07:41:40]] [SUCCESS] Screenshot refreshed
[[07:41:40]] [INFO] Refreshing screenshot...
[[07:41:40]] [INFO] xyHVihJMBi=pass
[[07:41:37]] [INFO] xyHVihJMBi=running
[[07:41:37]] [INFO] Executing action 446/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:41:36]] [SUCCESS] Screenshot refreshed successfully
[[07:41:36]] [SUCCESS] Screenshot refreshed successfully
[[07:41:36]] [SUCCESS] Screenshot refreshed
[[07:41:36]] [INFO] Refreshing screenshot...
[[07:41:36]] [INFO] mWeLQtXiL6=pass
[[07:41:29]] [INFO] mWeLQtXiL6=running
[[07:41:29]] [INFO] Executing action 445/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:41:29]] [SUCCESS] Screenshot refreshed successfully
[[07:41:29]] [SUCCESS] Screenshot refreshed successfully
[[07:41:29]] [SUCCESS] Screenshot refreshed
[[07:41:29]] [INFO] Refreshing screenshot...
[[07:41:29]] [INFO] F4NGh9HrLw=pass
[[07:41:24]] [INFO] F4NGh9HrLw=running
[[07:41:24]] [INFO] Executing action 444/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:41:24]] [SUCCESS] Screenshot refreshed successfully
[[07:41:24]] [SUCCESS] Screenshot refreshed successfully
[[07:41:24]] [SUCCESS] Screenshot refreshed
[[07:41:24]] [INFO] Refreshing screenshot...
[[07:41:24]] [INFO] 0f2FSZYjWq=pass
[[07:41:06]] [INFO] 0f2FSZYjWq=running
[[07:41:06]] [INFO] Executing action 443/641: Check if element with text="Melbourne" exists
[[07:41:06]] [SUCCESS] Screenshot refreshed successfully
[[07:41:06]] [SUCCESS] Screenshot refreshed successfully
[[07:41:06]] [SUCCESS] Screenshot refreshed
[[07:41:06]] [INFO] Refreshing screenshot...
[[07:41:06]] [INFO] Tebej51pT2=pass
[[07:41:02]] [INFO] Tebej51pT2=running
[[07:41:02]] [INFO] Executing action 442/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:41:01]] [SUCCESS] Screenshot refreshed successfully
[[07:41:01]] [SUCCESS] Screenshot refreshed successfully
[[07:41:01]] [SUCCESS] Screenshot refreshed
[[07:41:01]] [INFO] Refreshing screenshot...
[[07:41:01]] [INFO] I4gwigwXSj=pass
[[07:40:58]] [INFO] I4gwigwXSj=running
[[07:40:58]] [INFO] Executing action 441/641: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:40:58]] [SUCCESS] Screenshot refreshed successfully
[[07:40:58]] [SUCCESS] Screenshot refreshed successfully
[[07:40:58]] [SUCCESS] Screenshot refreshed
[[07:40:58]] [INFO] Refreshing screenshot...
[[07:40:58]] [INFO] eVytJrry9x=pass
[[07:40:53]] [INFO] eVytJrry9x=running
[[07:40:53]] [INFO] Executing action 440/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:40:53]] [SUCCESS] Screenshot refreshed successfully
[[07:40:53]] [SUCCESS] Screenshot refreshed successfully
[[07:40:53]] [SUCCESS] Screenshot refreshed
[[07:40:53]] [INFO] Refreshing screenshot...
[[07:40:53]] [INFO] s8h8VDUIOC=pass
[[07:40:48]] [INFO] s8h8VDUIOC=running
[[07:40:48]] [INFO] Executing action 439/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:40:48]] [SUCCESS] Screenshot refreshed successfully
[[07:40:48]] [SUCCESS] Screenshot refreshed successfully
[[07:40:48]] [SUCCESS] Screenshot refreshed
[[07:40:48]] [INFO] Refreshing screenshot...
[[07:40:48]] [INFO] bkU728TrRF=pass
[[07:40:41]] [INFO] bkU728TrRF=running
[[07:40:41]] [INFO] Executing action 438/641: Tap on element with accessibility_id: Done
[[07:40:41]] [SUCCESS] Screenshot refreshed successfully
[[07:40:41]] [SUCCESS] Screenshot refreshed successfully
[[07:40:41]] [SUCCESS] Screenshot refreshed
[[07:40:41]] [INFO] Refreshing screenshot...
[[07:40:41]] [INFO] ZWpYNcpbFA=pass
[[07:40:36]] [INFO] ZWpYNcpbFA=running
[[07:40:36]] [INFO] Executing action 437/641: Tap on Text: "VIC"
[[07:40:36]] [SUCCESS] Screenshot refreshed successfully
[[07:40:36]] [SUCCESS] Screenshot refreshed successfully
[[07:40:36]] [SUCCESS] Screenshot refreshed
[[07:40:36]] [INFO] Refreshing screenshot...
[[07:40:36]] [INFO] Wld5Urg70o=pass
[[07:40:29]] [INFO] Wld5Urg70o=running
[[07:40:29]] [INFO] Executing action 436/641: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[07:40:29]] [SUCCESS] Screenshot refreshed successfully
[[07:40:29]] [SUCCESS] Screenshot refreshed successfully
[[07:40:28]] [SUCCESS] Screenshot refreshed
[[07:40:28]] [INFO] Refreshing screenshot...
[[07:40:28]] [INFO] QpBLC6BStn=pass
[[07:40:22]] [INFO] QpBLC6BStn=running
[[07:40:22]] [INFO] Executing action 435/641: Tap on element with accessibility_id: delete
[[07:40:21]] [SUCCESS] Screenshot refreshed successfully
[[07:40:21]] [SUCCESS] Screenshot refreshed successfully
[[07:40:21]] [SUCCESS] Screenshot refreshed
[[07:40:21]] [INFO] Refreshing screenshot...
[[07:40:21]] [INFO] G4A3KBlXHq=pass
[[07:40:16]] [INFO] G4A3KBlXHq=running
[[07:40:16]] [INFO] Executing action 434/641: Tap on Text: "Nearby"
[[07:40:16]] [SUCCESS] Screenshot refreshed successfully
[[07:40:16]] [SUCCESS] Screenshot refreshed successfully
[[07:40:16]] [SUCCESS] Screenshot refreshed
[[07:40:16]] [INFO] Refreshing screenshot...
[[07:40:16]] [INFO] uArzgeZYf7=pass
[[07:40:12]] [INFO] uArzgeZYf7=running
[[07:40:12]] [INFO] Executing action 433/641: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[07:40:12]] [SUCCESS] Screenshot refreshed successfully
[[07:40:12]] [SUCCESS] Screenshot refreshed successfully
[[07:40:12]] [SUCCESS] Screenshot refreshed
[[07:40:12]] [INFO] Refreshing screenshot...
[[07:40:12]] [INFO] 3gJsiap2Ds=pass
[[07:40:08]] [INFO] 3gJsiap2Ds=running
[[07:40:08]] [INFO] Executing action 432/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[07:40:07]] [SUCCESS] Screenshot refreshed successfully
[[07:40:07]] [SUCCESS] Screenshot refreshed successfully
[[07:40:07]] [SUCCESS] Screenshot refreshed
[[07:40:07]] [INFO] Refreshing screenshot...
[[07:40:07]] [INFO] EReijW5iNX=pass
[[07:39:55]] [INFO] EReijW5iNX=running
[[07:39:55]] [INFO] Executing action 431/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:39:54]] [SUCCESS] Screenshot refreshed successfully
[[07:39:54]] [SUCCESS] Screenshot refreshed successfully
[[07:39:54]] [SUCCESS] Screenshot refreshed
[[07:39:54]] [INFO] Refreshing screenshot...
[[07:39:54]] [INFO] 94ikwhIEE2=pass
[[07:39:50]] [INFO] 94ikwhIEE2=running
[[07:39:50]] [INFO] Executing action 430/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:39:49]] [SUCCESS] Screenshot refreshed successfully
[[07:39:49]] [SUCCESS] Screenshot refreshed successfully
[[07:39:49]] [SUCCESS] Screenshot refreshed
[[07:39:49]] [INFO] Refreshing screenshot...
[[07:39:49]] [INFO] q8oldD8uZt=pass
[[07:39:46]] [INFO] q8oldD8uZt=running
[[07:39:46]] [INFO] Executing action 429/641: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[07:39:45]] [SUCCESS] Screenshot refreshed successfully
[[07:39:45]] [SUCCESS] Screenshot refreshed successfully
[[07:39:45]] [SUCCESS] Screenshot refreshed
[[07:39:45]] [INFO] Refreshing screenshot...
[[07:39:45]] [INFO] Jf2wJyOphY=pass
[[07:39:28]] [INFO] Jf2wJyOphY=running
[[07:39:28]] [INFO] Executing action 428/641: Tap on element with accessibility_id: Add to bag
[[07:39:28]] [SUCCESS] Screenshot refreshed successfully
[[07:39:28]] [SUCCESS] Screenshot refreshed successfully
[[07:39:28]] [SUCCESS] Screenshot refreshed
[[07:39:28]] [INFO] Refreshing screenshot...
[[07:39:28]] [INFO] eRCmRhc3re=pass
[[07:39:13]] [INFO] eRCmRhc3re=running
[[07:39:13]] [INFO] Executing action 427/641: Check if element with text="Broadway" exists
[[07:39:12]] [SUCCESS] Screenshot refreshed successfully
[[07:39:12]] [SUCCESS] Screenshot refreshed successfully
[[07:39:12]] [SUCCESS] Screenshot refreshed
[[07:39:12]] [INFO] Refreshing screenshot...
[[07:39:12]] [INFO] ORI6ZFMBK1=pass
[[07:39:08]] [INFO] ORI6ZFMBK1=running
[[07:39:08]] [INFO] Executing action 426/641: Tap on Text: "Save"
[[07:39:08]] [SUCCESS] Screenshot refreshed successfully
[[07:39:08]] [SUCCESS] Screenshot refreshed successfully
[[07:39:08]] [SUCCESS] Screenshot refreshed
[[07:39:08]] [INFO] Refreshing screenshot...
[[07:39:08]] [INFO] hr0IVckpYI=pass
[[07:39:03]] [INFO] hr0IVckpYI=running
[[07:39:03]] [INFO] Executing action 425/641: Wait till accessibility_id=btnSaveOrContinue
[[07:39:03]] [SUCCESS] Screenshot refreshed successfully
[[07:39:03]] [SUCCESS] Screenshot refreshed successfully
[[07:39:02]] [SUCCESS] Screenshot refreshed
[[07:39:02]] [INFO] Refreshing screenshot...
[[07:39:02]] [INFO] H0ODFz7sWJ=pass
[[07:38:58]] [INFO] H0ODFz7sWJ=running
[[07:38:58]] [INFO] Executing action 424/641: Tap on Text: "2000"
[[07:38:58]] [SUCCESS] Screenshot refreshed successfully
[[07:38:58]] [SUCCESS] Screenshot refreshed successfully
[[07:38:58]] [SUCCESS] Screenshot refreshed
[[07:38:58]] [INFO] Refreshing screenshot...
[[07:38:58]] [INFO] uZHvvAzVfx=pass
[[07:38:53]] [INFO] uZHvvAzVfx=running
[[07:38:53]] [INFO] Executing action 423/641: textClear action
[[07:38:52]] [SUCCESS] Screenshot refreshed successfully
[[07:38:52]] [SUCCESS] Screenshot refreshed successfully
[[07:38:52]] [SUCCESS] Screenshot refreshed
[[07:38:52]] [INFO] Refreshing screenshot...
[[07:38:52]] [INFO] WmNWcsWVHv=pass
[[07:38:47]] [INFO] WmNWcsWVHv=running
[[07:38:47]] [INFO] Executing action 422/641: Tap on element with accessibility_id: Search suburb or postcode
[[07:38:46]] [SUCCESS] Screenshot refreshed successfully
[[07:38:46]] [SUCCESS] Screenshot refreshed successfully
[[07:38:46]] [SUCCESS] Screenshot refreshed
[[07:38:46]] [INFO] Refreshing screenshot...
[[07:38:46]] [INFO] lnjoz8hHUU=pass
[[07:38:41]] [INFO] lnjoz8hHUU=running
[[07:38:41]] [INFO] Executing action 421/641: Tap on Text: "Edit"
[[07:38:41]] [SUCCESS] Screenshot refreshed successfully
[[07:38:41]] [SUCCESS] Screenshot refreshed successfully
[[07:38:41]] [SUCCESS] Screenshot refreshed
[[07:38:41]] [INFO] Refreshing screenshot...
[[07:38:41]] [INFO] letbbewlnA=pass
[[07:38:37]] [INFO] letbbewlnA=running
[[07:38:37]] [INFO] Executing action 420/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:38:36]] [SUCCESS] Screenshot refreshed successfully
[[07:38:36]] [SUCCESS] Screenshot refreshed successfully
[[07:38:36]] [SUCCESS] Screenshot refreshed
[[07:38:36]] [INFO] Refreshing screenshot...
[[07:38:36]] [INFO] trBISwJ8eZ=pass
[[07:38:32]] [INFO] trBISwJ8eZ=running
[[07:38:32]] [INFO] Executing action 419/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:38:32]] [SUCCESS] Screenshot refreshed successfully
[[07:38:32]] [SUCCESS] Screenshot refreshed successfully
[[07:38:32]] [SUCCESS] Screenshot refreshed
[[07:38:32]] [INFO] Refreshing screenshot...
[[07:38:32]] [INFO] foVGMl9wvu=pass
[[07:38:28]] [INFO] foVGMl9wvu=running
[[07:38:28]] [INFO] Executing action 418/641: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:38:28]] [SUCCESS] Screenshot refreshed successfully
[[07:38:28]] [SUCCESS] Screenshot refreshed successfully
[[07:38:27]] [SUCCESS] Screenshot refreshed
[[07:38:27]] [INFO] Refreshing screenshot...
[[07:38:27]] [INFO] 73NABkfWyY=pass
[[07:38:13]] [INFO] 73NABkfWyY=running
[[07:38:13]] [INFO] Executing action 417/641: Check if element with text="Tarneit" exists
[[07:38:12]] [SUCCESS] Screenshot refreshed successfully
[[07:38:12]] [SUCCESS] Screenshot refreshed successfully
[[07:38:12]] [SUCCESS] Screenshot refreshed
[[07:38:12]] [INFO] Refreshing screenshot...
[[07:38:12]] [INFO] pKjXoj4mNg=pass
[[07:38:08]] [INFO] pKjXoj4mNg=running
[[07:38:08]] [INFO] Executing action 416/641: Tap on Text: "Save"
[[07:38:08]] [SUCCESS] Screenshot refreshed successfully
[[07:38:08]] [SUCCESS] Screenshot refreshed successfully
[[07:38:07]] [SUCCESS] Screenshot refreshed
[[07:38:07]] [INFO] Refreshing screenshot...
[[07:38:07]] [INFO] M3dXqigqRv=pass
[[07:38:03]] [INFO] M3dXqigqRv=running
[[07:38:03]] [INFO] Executing action 415/641: Wait till accessibility_id=btnSaveOrContinue
[[07:38:02]] [SUCCESS] Screenshot refreshed successfully
[[07:38:02]] [SUCCESS] Screenshot refreshed successfully
[[07:38:02]] [SUCCESS] Screenshot refreshed
[[07:38:02]] [INFO] Refreshing screenshot...
[[07:38:02]] [INFO] GYRHQr7TWx=pass
[[07:37:58]] [INFO] GYRHQr7TWx=running
[[07:37:58]] [INFO] Executing action 414/641: Tap on Text: "current"
[[07:37:58]] [SUCCESS] Screenshot refreshed successfully
[[07:37:58]] [SUCCESS] Screenshot refreshed successfully
[[07:37:58]] [SUCCESS] Screenshot refreshed
[[07:37:58]] [INFO] Refreshing screenshot...
[[07:37:58]] [INFO] kiM0WyWE9I=pass
[[07:37:53]] [INFO] kiM0WyWE9I=running
[[07:37:53]] [INFO] Executing action 413/641: Wait till accessibility_id=btnCurrentLocationButton
[[07:37:53]] [SUCCESS] Screenshot refreshed successfully
[[07:37:53]] [SUCCESS] Screenshot refreshed successfully
[[07:37:52]] [SUCCESS] Screenshot refreshed
[[07:37:52]] [INFO] Refreshing screenshot...
[[07:37:52]] [INFO] VkUKQbf1Qt=pass
[[07:37:48]] [INFO] VkUKQbf1Qt=running
[[07:37:48]] [INFO] Executing action 412/641: Tap on Text: "Edit"
[[07:37:48]] [SUCCESS] Screenshot refreshed successfully
[[07:37:48]] [SUCCESS] Screenshot refreshed successfully
[[07:37:47]] [SUCCESS] Screenshot refreshed
[[07:37:47]] [INFO] Refreshing screenshot...
[[07:37:47]] [INFO] C6JHhLdWTv=pass
[[07:37:44]] [INFO] C6JHhLdWTv=running
[[07:37:44]] [INFO] Executing action 411/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:37:44]] [SUCCESS] Screenshot refreshed successfully
[[07:37:44]] [SUCCESS] Screenshot refreshed successfully
[[07:37:43]] [SUCCESS] Screenshot refreshed
[[07:37:43]] [INFO] Refreshing screenshot...
[[07:37:43]] [INFO] IupxLP2Jsr=pass
[[07:37:39]] [INFO] IupxLP2Jsr=running
[[07:37:39]] [INFO] Executing action 410/641: iOS Function: text - Text: "Uno card"
[[07:37:39]] [SUCCESS] Screenshot refreshed successfully
[[07:37:39]] [SUCCESS] Screenshot refreshed successfully
[[07:37:39]] [SUCCESS] Screenshot refreshed
[[07:37:39]] [INFO] Refreshing screenshot...
[[07:37:39]] [INFO] 70iOOakiG7=pass
[[07:37:34]] [INFO] 70iOOakiG7=running
[[07:37:34]] [INFO] Executing action 409/641: Tap on Text: "Find"
[[07:37:34]] [SUCCESS] Screenshot refreshed successfully
[[07:37:34]] [SUCCESS] Screenshot refreshed successfully
[[07:37:33]] [SUCCESS] Screenshot refreshed
[[07:37:33]] [INFO] Refreshing screenshot...
[[07:37:33]] [INFO] vL26X6PBjc=pass
[[07:37:27]] [INFO] vL26X6PBjc=running
[[07:37:27]] [INFO] Executing action 408/641: Tap if locator exists: accessibility_id="btnUpdate"
[[07:37:26]] [SUCCESS] Screenshot refreshed successfully
[[07:37:26]] [SUCCESS] Screenshot refreshed successfully
[[07:37:26]] [SUCCESS] Screenshot refreshed
[[07:37:26]] [INFO] Refreshing screenshot...
[[07:37:26]] [INFO] E2jpN7BioW=pass
[[07:37:22]] [INFO] E2jpN7BioW=running
[[07:37:22]] [INFO] Executing action 407/641: Tap on Text: "Save"
[[07:37:22]] [SUCCESS] Screenshot refreshed successfully
[[07:37:22]] [SUCCESS] Screenshot refreshed successfully
[[07:37:21]] [SUCCESS] Screenshot refreshed
[[07:37:21]] [INFO] Refreshing screenshot...
[[07:37:21]] [INFO] Sl6eiqZkRm=pass
[[07:37:17]] [INFO] Sl6eiqZkRm=running
[[07:37:17]] [INFO] Executing action 406/641: Wait till accessibility_id=btnSaveOrContinue
[[07:37:16]] [SUCCESS] Screenshot refreshed successfully
[[07:37:16]] [SUCCESS] Screenshot refreshed successfully
[[07:37:16]] [SUCCESS] Screenshot refreshed
[[07:37:16]] [INFO] Refreshing screenshot...
[[07:37:16]] [INFO] mw9GQ4mzRE=pass
[[07:37:12]] [INFO] mw9GQ4mzRE=running
[[07:37:12]] [INFO] Executing action 405/641: Tap on Text: "2000"
[[07:37:12]] [SUCCESS] Screenshot refreshed successfully
[[07:37:12]] [SUCCESS] Screenshot refreshed successfully
[[07:37:12]] [SUCCESS] Screenshot refreshed
[[07:37:12]] [INFO] Refreshing screenshot...
[[07:37:12]] [INFO] kbdEPCPYod=pass
[[07:37:07]] [INFO] kbdEPCPYod=running
[[07:37:07]] [INFO] Executing action 404/641: textClear action
[[07:37:06]] [SUCCESS] Screenshot refreshed successfully
[[07:37:06]] [SUCCESS] Screenshot refreshed successfully
[[07:37:06]] [SUCCESS] Screenshot refreshed
[[07:37:06]] [INFO] Refreshing screenshot...
[[07:37:06]] [INFO] 8WCusTZ8q9=pass
[[07:37:01]] [INFO] 8WCusTZ8q9=running
[[07:37:01]] [INFO] Executing action 403/641: Tap on element with accessibility_id: Search suburb or postcode
[[07:37:00]] [SUCCESS] Screenshot refreshed successfully
[[07:37:00]] [SUCCESS] Screenshot refreshed successfully
[[07:37:00]] [SUCCESS] Screenshot refreshed
[[07:37:00]] [INFO] Refreshing screenshot...
[[07:37:00]] [INFO] QMXBlswP6H=pass
[[07:36:56]] [INFO] QMXBlswP6H=running
[[07:36:56]] [INFO] Executing action 402/641: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[07:36:56]] [SUCCESS] Screenshot refreshed successfully
[[07:36:56]] [SUCCESS] Screenshot refreshed successfully
[[07:36:56]] [SUCCESS] Screenshot refreshed
[[07:36:56]] [INFO] Refreshing screenshot...
[[07:36:56]] [INFO] m0956RsrdM=pass
[[07:36:53]] [SUCCESS] Screenshot refreshed successfully
[[07:36:53]] [SUCCESS] Screenshot refreshed successfully
[[07:36:52]] [INFO] m0956RsrdM=running
[[07:36:52]] [INFO] Executing action 401/641: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[07:36:52]] [SUCCESS] Screenshot refreshed
[[07:36:52]] [INFO] Refreshing screenshot...
[[07:36:52]] [SUCCESS] Screenshot refreshed successfully
[[07:36:52]] [SUCCESS] Screenshot refreshed successfully
[[07:36:52]] [SUCCESS] Screenshot refreshed
[[07:36:52]] [INFO] Refreshing screenshot...
[[07:36:47]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[07:36:47]] [SUCCESS] Screenshot refreshed successfully
[[07:36:47]] [SUCCESS] Screenshot refreshed successfully
[[07:36:47]] [SUCCESS] Screenshot refreshed
[[07:36:47]] [INFO] Refreshing screenshot...
[[07:36:43]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:36:42]] [SUCCESS] Screenshot refreshed successfully
[[07:36:42]] [SUCCESS] Screenshot refreshed successfully
[[07:36:42]] [SUCCESS] Screenshot refreshed
[[07:36:42]] [INFO] Refreshing screenshot...
[[07:36:38]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[07:36:37]] [SUCCESS] Screenshot refreshed successfully
[[07:36:37]] [SUCCESS] Screenshot refreshed successfully
[[07:36:37]] [SUCCESS] Screenshot refreshed
[[07:36:37]] [INFO] Refreshing screenshot...
[[07:36:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:36:33]] [SUCCESS] Screenshot refreshed successfully
[[07:36:33]] [SUCCESS] Screenshot refreshed successfully
[[07:36:33]] [SUCCESS] Screenshot refreshed
[[07:36:33]] [INFO] Refreshing screenshot...
[[07:36:27]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:36:27]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[07:36:27]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[07:36:27]] [INFO] HlWGryBWT9=running
[[07:36:27]] [INFO] Executing action 400/641: Execute Test Case: Kmart-Signin (5 steps)
[[07:36:27]] [SUCCESS] Screenshot refreshed successfully
[[07:36:27]] [SUCCESS] Screenshot refreshed successfully
[[07:36:26]] [SUCCESS] Screenshot refreshed
[[07:36:26]] [INFO] Refreshing screenshot...
[[07:36:26]] [INFO] Azb1flbIJJ=pass
[[07:36:23]] [INFO] Azb1flbIJJ=running
[[07:36:23]] [INFO] Executing action 399/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:36:22]] [SUCCESS] Screenshot refreshed successfully
[[07:36:22]] [SUCCESS] Screenshot refreshed successfully
[[07:36:22]] [SUCCESS] Screenshot refreshed
[[07:36:22]] [INFO] Refreshing screenshot...
[[07:36:22]] [INFO] 2xC5fLfLe8=pass
[[07:36:20]] [INFO] 2xC5fLfLe8=running
[[07:36:20]] [INFO] Executing action 398/641: iOS Function: alert_accept
[[07:36:19]] [SUCCESS] Screenshot refreshed successfully
[[07:36:19]] [SUCCESS] Screenshot refreshed successfully
[[07:36:19]] [SUCCESS] Screenshot refreshed
[[07:36:19]] [INFO] Refreshing screenshot...
[[07:36:19]] [INFO] Y8vz7AJD1i=pass
[[07:36:12]] [INFO] Y8vz7AJD1i=running
[[07:36:12]] [INFO] Executing action 397/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:36:12]] [SUCCESS] Screenshot refreshed successfully
[[07:36:12]] [SUCCESS] Screenshot refreshed successfully
[[07:36:12]] [SUCCESS] Screenshot refreshed
[[07:36:12]] [INFO] Refreshing screenshot...
[[07:36:12]] [INFO] H9fy9qcFbZ=pass
[[07:35:58]] [SUCCESS] Screenshot refreshed successfully
[[07:35:58]] [SUCCESS] Screenshot refreshed successfully
[[07:35:58]] [INFO] H9fy9qcFbZ=running
[[07:35:58]] [INFO] Executing action 396/641: Restart app: env[appid]
[[07:35:58]] [SUCCESS] Screenshot refreshed
[[07:35:58]] [INFO] Refreshing screenshot...
[[07:35:58]] [SUCCESS] Screenshot refreshed successfully
[[07:35:58]] [SUCCESS] Screenshot refreshed successfully
[[07:35:57]] [SUCCESS] Screenshot refreshed
[[07:35:57]] [INFO] Refreshing screenshot...
[[07:35:55]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:35:55]] [SUCCESS] Screenshot refreshed successfully
[[07:35:55]] [SUCCESS] Screenshot refreshed successfully
[[07:35:55]] [SUCCESS] Screenshot refreshed
[[07:35:55]] [INFO] Refreshing screenshot...
[[07:35:42]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:35:42]] [SUCCESS] Screenshot refreshed successfully
[[07:35:42]] [SUCCESS] Screenshot refreshed successfully
[[07:35:42]] [SUCCESS] Screenshot refreshed
[[07:35:42]] [INFO] Refreshing screenshot...
[[07:35:38]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:35:38]] [SUCCESS] Screenshot refreshed successfully
[[07:35:38]] [SUCCESS] Screenshot refreshed successfully
[[07:35:38]] [SUCCESS] Screenshot refreshed
[[07:35:38]] [INFO] Refreshing screenshot...
[[07:35:34]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:35:34]] [SUCCESS] Screenshot refreshed successfully
[[07:35:34]] [SUCCESS] Screenshot refreshed successfully
[[07:35:33]] [SUCCESS] Screenshot refreshed
[[07:35:33]] [INFO] Refreshing screenshot...
[[07:35:27]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:35:27]] [SUCCESS] Screenshot refreshed successfully
[[07:35:27]] [SUCCESS] Screenshot refreshed successfully
[[07:35:26]] [SUCCESS] Screenshot refreshed
[[07:35:26]] [INFO] Refreshing screenshot...
[[07:35:21]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:35:21]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:35:21]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:35:21]] [INFO] OMgc2gHHyq=running
[[07:35:21]] [INFO] Executing action 395/641: cleanupSteps action
[[07:35:20]] [SUCCESS] Screenshot refreshed successfully
[[07:35:20]] [SUCCESS] Screenshot refreshed successfully
[[07:35:20]] [SUCCESS] Screenshot refreshed
[[07:35:20]] [INFO] Refreshing screenshot...
[[07:35:20]] [INFO] x4yLCZHaCR=pass
[[07:35:18]] [INFO] x4yLCZHaCR=running
[[07:35:18]] [INFO] Executing action 394/641: Terminate app: env[appid]
[[07:35:17]] [SUCCESS] Screenshot refreshed successfully
[[07:35:17]] [SUCCESS] Screenshot refreshed successfully
[[07:35:17]] [SUCCESS] Screenshot refreshed
[[07:35:17]] [INFO] Refreshing screenshot...
[[07:35:17]] [INFO] 2p13JoJbbA=pass
[[07:35:14]] [INFO] 2p13JoJbbA=running
[[07:35:14]] [INFO] Executing action 393/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:35:13]] [SUCCESS] Screenshot refreshed successfully
[[07:35:13]] [SUCCESS] Screenshot refreshed successfully
[[07:35:13]] [SUCCESS] Screenshot refreshed
[[07:35:13]] [INFO] Refreshing screenshot...
[[07:35:13]] [INFO] qHdMgerbTE=pass
[[07:35:10]] [INFO] qHdMgerbTE=running
[[07:35:10]] [INFO] Executing action 392/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:35:09]] [SUCCESS] Screenshot refreshed successfully
[[07:35:09]] [SUCCESS] Screenshot refreshed successfully
[[07:35:09]] [SUCCESS] Screenshot refreshed
[[07:35:09]] [INFO] Refreshing screenshot...
[[07:35:09]] [INFO] F4NGh9HrLw=pass
[[07:35:06]] [INFO] F4NGh9HrLw=running
[[07:35:06]] [INFO] Executing action 391/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:35:05]] [SUCCESS] Screenshot refreshed successfully
[[07:35:05]] [SUCCESS] Screenshot refreshed successfully
[[07:35:05]] [SUCCESS] Screenshot refreshed
[[07:35:05]] [INFO] Refreshing screenshot...
[[07:35:05]] [INFO] 7mnBGa2GCk=pass
[[07:34:54]] [INFO] 7mnBGa2GCk=running
[[07:34:54]] [INFO] Executing action 390/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Save my location"]"
[[07:34:53]] [SUCCESS] Screenshot refreshed successfully
[[07:34:53]] [SUCCESS] Screenshot refreshed successfully
[[07:34:53]] [SUCCESS] Screenshot refreshed
[[07:34:53]] [INFO] Refreshing screenshot...
[[07:34:53]] [INFO] tWq2Qzn22D=pass
[[07:34:49]] [INFO] tWq2Qzn22D=running
[[07:34:49]] [INFO] Executing action 389/641: Tap on image: env[device-back-img]
[[07:34:49]] [SUCCESS] Screenshot refreshed successfully
[[07:34:49]] [SUCCESS] Screenshot refreshed successfully
[[07:34:49]] [SUCCESS] Screenshot refreshed
[[07:34:49]] [INFO] Refreshing screenshot...
[[07:34:49]] [INFO] ysJIY9A3gq=pass
[[07:34:37]] [INFO] ysJIY9A3gq=running
[[07:34:37]] [INFO] Executing action 388/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]"
[[07:34:36]] [SUCCESS] Screenshot refreshed successfully
[[07:34:36]] [SUCCESS] Screenshot refreshed successfully
[[07:34:36]] [SUCCESS] Screenshot refreshed
[[07:34:36]] [INFO] Refreshing screenshot...
[[07:34:36]] [INFO] jmKjclMUWT=pass
[[07:34:32]] [INFO] jmKjclMUWT=running
[[07:34:32]] [INFO] Executing action 387/641: Tap on Text: "current"
[[07:34:32]] [SUCCESS] Screenshot refreshed successfully
[[07:34:32]] [SUCCESS] Screenshot refreshed successfully
[[07:34:32]] [SUCCESS] Screenshot refreshed
[[07:34:32]] [INFO] Refreshing screenshot...
[[07:34:32]] [INFO] UoH0wdtcLk=pass
[[07:34:27]] [INFO] UoH0wdtcLk=running
[[07:34:27]] [INFO] Executing action 386/641: Tap on Text: "Edit"
[[07:34:27]] [SUCCESS] Screenshot refreshed successfully
[[07:34:27]] [SUCCESS] Screenshot refreshed successfully
[[07:34:27]] [SUCCESS] Screenshot refreshed
[[07:34:27]] [INFO] Refreshing screenshot...
[[07:34:27]] [INFO] U48qCNydwd=pass
[[07:34:22]] [INFO] U48qCNydwd=running
[[07:34:22]] [INFO] Executing action 385/641: Restart app: env[appid]
[[07:34:22]] [SUCCESS] Screenshot refreshed successfully
[[07:34:22]] [SUCCESS] Screenshot refreshed successfully
[[07:34:22]] [SUCCESS] Screenshot refreshed
[[07:34:22]] [INFO] Refreshing screenshot...
[[07:34:22]] [INFO] XjclKOaCTh=pass
[[07:34:17]] [INFO] XjclKOaCTh=running
[[07:34:17]] [INFO] Executing action 384/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[07:34:17]] [SUCCESS] Screenshot refreshed successfully
[[07:34:17]] [SUCCESS] Screenshot refreshed successfully
[[07:34:17]] [SUCCESS] Screenshot refreshed
[[07:34:17]] [INFO] Refreshing screenshot...
[[07:34:17]] [INFO] q6cKxgMAIn=pass
[[07:34:13]] [INFO] q6cKxgMAIn=running
[[07:34:13]] [INFO] Executing action 383/641: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[07:34:13]] [SUCCESS] Screenshot refreshed successfully
[[07:34:13]] [SUCCESS] Screenshot refreshed successfully
[[07:34:13]] [SUCCESS] Screenshot refreshed
[[07:34:13]] [INFO] Refreshing screenshot...
[[07:34:13]] [INFO] zdh8hKYC1a=pass
[[07:34:09]] [INFO] zdh8hKYC1a=running
[[07:34:09]] [INFO] Executing action 382/641: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[07:34:09]] [SUCCESS] Screenshot refreshed successfully
[[07:34:09]] [SUCCESS] Screenshot refreshed successfully
[[07:34:09]] [SUCCESS] Screenshot refreshed
[[07:34:09]] [INFO] Refreshing screenshot...
[[07:34:09]] [INFO] P4b2BITpCf=pass
[[07:34:06]] [INFO] P4b2BITpCf=running
[[07:34:06]] [INFO] Executing action 381/641: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[07:34:05]] [SUCCESS] Screenshot refreshed successfully
[[07:34:05]] [SUCCESS] Screenshot refreshed successfully
[[07:34:05]] [SUCCESS] Screenshot refreshed
[[07:34:05]] [INFO] Refreshing screenshot...
[[07:34:05]] [INFO] inrxgdWzXr=pass
[[07:34:01]] [INFO] inrxgdWzXr=running
[[07:34:01]] [INFO] Executing action 380/641: Tap on Text: "Store"
[[07:34:01]] [SUCCESS] Screenshot refreshed successfully
[[07:34:01]] [SUCCESS] Screenshot refreshed successfully
[[07:34:01]] [SUCCESS] Screenshot refreshed
[[07:34:01]] [INFO] Refreshing screenshot...
[[07:34:01]] [INFO] inrxgdWzXr=pass
[[07:33:57]] [INFO] inrxgdWzXr=running
[[07:33:57]] [INFO] Executing action 379/641: Tap on Text: "receipts"
[[07:33:56]] [SUCCESS] Screenshot refreshed successfully
[[07:33:56]] [SUCCESS] Screenshot refreshed successfully
[[07:33:56]] [SUCCESS] Screenshot refreshed
[[07:33:56]] [INFO] Refreshing screenshot...
[[07:33:56]] [INFO] GEMv6goQtW=pass
[[07:33:52]] [INFO] GEMv6goQtW=running
[[07:33:52]] [INFO] Executing action 378/641: Tap on image: env[device-back-img]
[[07:33:52]] [SUCCESS] Screenshot refreshed successfully
[[07:33:52]] [SUCCESS] Screenshot refreshed successfully
[[07:33:52]] [SUCCESS] Screenshot refreshed
[[07:33:52]] [INFO] Refreshing screenshot...
[[07:33:52]] [INFO] DhWa2PCBXE=pass
[[07:33:49]] [INFO] DhWa2PCBXE=running
[[07:33:49]] [INFO] Executing action 377/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[07:33:49]] [SUCCESS] Screenshot refreshed successfully
[[07:33:49]] [SUCCESS] Screenshot refreshed successfully
[[07:33:49]] [SUCCESS] Screenshot refreshed
[[07:33:49]] [INFO] Refreshing screenshot...
[[07:33:49]] [INFO] pk2DLZFBmx=pass
[[07:33:45]] [INFO] pk2DLZFBmx=running
[[07:33:45]] [INFO] Executing action 376/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[07:33:44]] [SUCCESS] Screenshot refreshed successfully
[[07:33:44]] [SUCCESS] Screenshot refreshed successfully
[[07:33:44]] [SUCCESS] Screenshot refreshed
[[07:33:44]] [INFO] Refreshing screenshot...
[[07:33:44]] [INFO] ShJSdXvmVL=pass
[[07:33:40]] [INFO] ShJSdXvmVL=running
[[07:33:40]] [INFO] Executing action 375/641: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[07:33:40]] [SUCCESS] Screenshot refreshed successfully
[[07:33:40]] [SUCCESS] Screenshot refreshed successfully
[[07:33:40]] [SUCCESS] Screenshot refreshed
[[07:33:40]] [INFO] Refreshing screenshot...
[[07:33:40]] [INFO] A57bC3QuEM=pass
[[07:33:35]] [INFO] A57bC3QuEM=running
[[07:33:35]] [INFO] Executing action 374/641: iOS Function: text - Text: "Wonderbaby@5"
[[07:33:35]] [SUCCESS] Screenshot refreshed successfully
[[07:33:35]] [SUCCESS] Screenshot refreshed successfully
[[07:33:35]] [SUCCESS] Screenshot refreshed
[[07:33:35]] [INFO] Refreshing screenshot...
[[07:33:35]] [INFO] d6vTfR4Y0D=pass
[[07:33:31]] [INFO] d6vTfR4Y0D=running
[[07:33:31]] [INFO] Executing action 373/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:33:30]] [SUCCESS] Screenshot refreshed successfully
[[07:33:30]] [SUCCESS] Screenshot refreshed successfully
[[07:33:30]] [SUCCESS] Screenshot refreshed
[[07:33:30]] [INFO] Refreshing screenshot...
[[07:33:30]] [INFO] g2CqCO1Kr6=pass
[[07:33:26]] [INFO] g2CqCO1Kr6=running
[[07:33:26]] [INFO] Executing action 372/641: iOS Function: text - Text: "<EMAIL>"
[[07:33:25]] [SUCCESS] Screenshot refreshed successfully
[[07:33:25]] [SUCCESS] Screenshot refreshed successfully
[[07:33:25]] [SUCCESS] Screenshot refreshed
[[07:33:25]] [INFO] Refreshing screenshot...
[[07:33:25]] [INFO] u928vFzSni=pass
[[07:33:20]] [INFO] u928vFzSni=running
[[07:33:20]] [INFO] Executing action 371/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:33:20]] [SUCCESS] Screenshot refreshed successfully
[[07:33:20]] [SUCCESS] Screenshot refreshed successfully
[[07:33:20]] [SUCCESS] Screenshot refreshed
[[07:33:20]] [INFO] Refreshing screenshot...
[[07:33:20]] [INFO] s0WyiD1w0B=pass
[[07:33:17]] [INFO] s0WyiD1w0B=running
[[07:33:17]] [INFO] Executing action 370/641: iOS Function: alert_accept
[[07:33:17]] [SUCCESS] Screenshot refreshed successfully
[[07:33:17]] [SUCCESS] Screenshot refreshed successfully
[[07:33:17]] [SUCCESS] Screenshot refreshed
[[07:33:17]] [INFO] Refreshing screenshot...
[[07:33:17]] [INFO] gekNSY5O2E=pass
[[07:33:13]] [INFO] gekNSY5O2E=running
[[07:33:13]] [INFO] Executing action 369/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[07:33:13]] [SUCCESS] Screenshot refreshed successfully
[[07:33:13]] [SUCCESS] Screenshot refreshed successfully
[[07:33:12]] [SUCCESS] Screenshot refreshed
[[07:33:12]] [INFO] Refreshing screenshot...
[[07:33:12]] [INFO] VJJ3EXXotU=pass
[[07:33:09]] [INFO] VJJ3EXXotU=running
[[07:33:09]] [INFO] Executing action 368/641: Tap on image: env[device-back-img]
[[07:33:08]] [SUCCESS] Screenshot refreshed successfully
[[07:33:08]] [SUCCESS] Screenshot refreshed successfully
[[07:33:08]] [SUCCESS] Screenshot refreshed
[[07:33:08]] [INFO] Refreshing screenshot...
[[07:33:08]] [INFO] 83tV9A4NOn=pass
[[07:33:05]] [INFO] 83tV9A4NOn=running
[[07:33:05]] [INFO] Executing action 367/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[07:33:05]] [SUCCESS] Screenshot refreshed successfully
[[07:33:05]] [SUCCESS] Screenshot refreshed successfully
[[07:33:05]] [SUCCESS] Screenshot refreshed
[[07:33:05]] [INFO] Refreshing screenshot...
[[07:33:05]] [INFO] aNN0yYFLEd=pass
[[07:33:01]] [INFO] aNN0yYFLEd=running
[[07:33:01]] [INFO] Executing action 366/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[07:33:01]] [SUCCESS] Screenshot refreshed successfully
[[07:33:01]] [SUCCESS] Screenshot refreshed successfully
[[07:33:01]] [SUCCESS] Screenshot refreshed
[[07:33:01]] [INFO] Refreshing screenshot...
[[07:33:01]] [INFO] XJv08Gkucs=pass
[[07:32:57]] [INFO] XJv08Gkucs=running
[[07:32:57]] [INFO] Executing action 365/641: Input text: "<EMAIL>"
[[07:32:57]] [SUCCESS] Screenshot refreshed successfully
[[07:32:57]] [SUCCESS] Screenshot refreshed successfully
[[07:32:57]] [SUCCESS] Screenshot refreshed
[[07:32:57]] [INFO] Refreshing screenshot...
[[07:32:57]] [INFO] kAQ1yIIw3h=pass
[[07:32:53]] [INFO] kAQ1yIIw3h=running
[[07:32:53]] [INFO] Executing action 364/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[07:32:53]] [SUCCESS] Screenshot refreshed successfully
[[07:32:53]] [SUCCESS] Screenshot refreshed successfully
[[07:32:53]] [SUCCESS] Screenshot refreshed
[[07:32:53]] [INFO] Refreshing screenshot...
[[07:32:53]] [INFO] 7YbjwQH1Jc=pass
[[07:32:50]] [INFO] 7YbjwQH1Jc=running
[[07:32:50]] [INFO] Executing action 363/641: Input text: "env[searchorder]"
[[07:32:49]] [SUCCESS] Screenshot refreshed successfully
[[07:32:49]] [SUCCESS] Screenshot refreshed successfully
[[07:32:49]] [SUCCESS] Screenshot refreshed
[[07:32:49]] [INFO] Refreshing screenshot...
[[07:32:49]] [INFO] OmKfD9iBjD=pass
[[07:32:45]] [INFO] OmKfD9iBjD=running
[[07:32:45]] [INFO] Executing action 362/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[07:32:45]] [SUCCESS] Screenshot refreshed successfully
[[07:32:45]] [SUCCESS] Screenshot refreshed successfully
[[07:32:45]] [SUCCESS] Screenshot refreshed
[[07:32:45]] [INFO] Refreshing screenshot...
[[07:32:45]] [INFO] eHLWiRoqqS=pass
[[07:32:41]] [INFO] eHLWiRoqqS=running
[[07:32:41]] [INFO] Executing action 361/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[07:32:41]] [SUCCESS] Screenshot refreshed successfully
[[07:32:41]] [SUCCESS] Screenshot refreshed successfully
[[07:32:41]] [SUCCESS] Screenshot refreshed
[[07:32:41]] [INFO] Refreshing screenshot...
[[07:32:41]] [INFO] F4NGh9HrLw=pass
[[07:32:37]] [INFO] F4NGh9HrLw=running
[[07:32:37]] [INFO] Executing action 360/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:32:37]] [SUCCESS] Screenshot refreshed successfully
[[07:32:37]] [SUCCESS] Screenshot refreshed successfully
[[07:32:36]] [SUCCESS] Screenshot refreshed
[[07:32:36]] [INFO] Refreshing screenshot...
[[07:32:36]] [INFO] 74XW7x54ad=pass
[[07:32:32]] [INFO] 74XW7x54ad=running
[[07:32:32]] [INFO] Executing action 359/641: Tap on image: env[device-back-img]
[[07:32:32]] [SUCCESS] Screenshot refreshed successfully
[[07:32:32]] [SUCCESS] Screenshot refreshed successfully
[[07:32:32]] [SUCCESS] Screenshot refreshed
[[07:32:32]] [INFO] Refreshing screenshot...
[[07:32:32]] [INFO] xUbWFa8Ok2=pass
[[07:32:29]] [INFO] xUbWFa8Ok2=running
[[07:32:29]] [INFO] Executing action 358/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[07:32:29]] [SUCCESS] Screenshot refreshed successfully
[[07:32:29]] [SUCCESS] Screenshot refreshed successfully
[[07:32:29]] [SUCCESS] Screenshot refreshed
[[07:32:29]] [INFO] Refreshing screenshot...
[[07:32:29]] [INFO] RbNtEW6N9T=pass
[[07:32:26]] [INFO] RbNtEW6N9T=running
[[07:32:26]] [INFO] Executing action 357/641: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[07:32:26]] [SUCCESS] Screenshot refreshed successfully
[[07:32:26]] [SUCCESS] Screenshot refreshed successfully
[[07:32:25]] [SUCCESS] Screenshot refreshed
[[07:32:25]] [INFO] Refreshing screenshot...
[[07:32:25]] [INFO] F4NGh9HrLw=pass
[[07:32:22]] [INFO] F4NGh9HrLw=running
[[07:32:22]] [INFO] Executing action 356/641: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[07:32:22]] [SUCCESS] Screenshot refreshed successfully
[[07:32:22]] [SUCCESS] Screenshot refreshed successfully
[[07:32:22]] [SUCCESS] Screenshot refreshed
[[07:32:22]] [INFO] Refreshing screenshot...
[[07:32:22]] [INFO] RlDZFks4Lc=pass
[[07:32:20]] [INFO] RlDZFks4Lc=running
[[07:32:20]] [INFO] Executing action 355/641: iOS Function: alert_accept
[[07:32:20]] [SUCCESS] Screenshot refreshed successfully
[[07:32:20]] [SUCCESS] Screenshot refreshed successfully
[[07:32:19]] [SUCCESS] Screenshot refreshed
[[07:32:19]] [INFO] Refreshing screenshot...
[[07:32:19]] [INFO] Dzn2Q7JTe0=pass
[[07:32:15]] [INFO] Dzn2Q7JTe0=running
[[07:32:15]] [INFO] Executing action 354/641: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[07:32:15]] [SUCCESS] Screenshot refreshed successfully
[[07:32:15]] [SUCCESS] Screenshot refreshed successfully
[[07:32:15]] [SUCCESS] Screenshot refreshed
[[07:32:15]] [INFO] Refreshing screenshot...
[[07:32:15]] [INFO] H9fy9qcFbZ=pass
[[07:32:02]] [SUCCESS] Screenshot refreshed successfully
[[07:32:02]] [SUCCESS] Screenshot refreshed successfully
[[07:32:01]] [INFO] H9fy9qcFbZ=running
[[07:32:01]] [INFO] Executing action 353/641: Restart app: env[appid]
[[07:32:01]] [SUCCESS] Screenshot refreshed
[[07:32:01]] [INFO] Refreshing screenshot...
[[07:32:01]] [SUCCESS] Screenshot refreshed successfully
[[07:32:01]] [SUCCESS] Screenshot refreshed successfully
[[07:32:01]] [SUCCESS] Screenshot refreshed
[[07:32:01]] [INFO] Refreshing screenshot...
[[07:31:58]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:31:58]] [SUCCESS] Screenshot refreshed successfully
[[07:31:58]] [SUCCESS] Screenshot refreshed successfully
[[07:31:58]] [SUCCESS] Screenshot refreshed
[[07:31:58]] [INFO] Refreshing screenshot...
[[07:31:45]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:31:45]] [SUCCESS] Screenshot refreshed successfully
[[07:31:45]] [SUCCESS] Screenshot refreshed successfully
[[07:31:45]] [SUCCESS] Screenshot refreshed
[[07:31:45]] [INFO] Refreshing screenshot...
[[07:31:41]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:31:41]] [SUCCESS] Screenshot refreshed successfully
[[07:31:41]] [SUCCESS] Screenshot refreshed successfully
[[07:31:41]] [SUCCESS] Screenshot refreshed
[[07:31:41]] [INFO] Refreshing screenshot...
[[07:31:37]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:31:37]] [SUCCESS] Screenshot refreshed successfully
[[07:31:37]] [SUCCESS] Screenshot refreshed successfully
[[07:31:37]] [SUCCESS] Screenshot refreshed
[[07:31:37]] [INFO] Refreshing screenshot...
[[07:31:30]] [SUCCESS] Screenshot refreshed successfully
[[07:31:30]] [SUCCESS] Screenshot refreshed successfully
[[07:31:30]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:31:30]] [SUCCESS] Screenshot refreshed
[[07:31:30]] [INFO] Refreshing screenshot...
[[07:31:23]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:31:23]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:31:23]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:31:23]] [INFO] AeQaElnzUN=running
[[07:31:23]] [INFO] Executing action 352/641: cleanupSteps action
[[07:31:23]] [SUCCESS] Screenshot refreshed successfully
[[07:31:23]] [SUCCESS] Screenshot refreshed successfully
[[07:31:22]] [SUCCESS] Screenshot refreshed
[[07:31:22]] [INFO] Refreshing screenshot...
[[07:31:22]] [INFO] BracBsfa3Y=pass
[[07:31:18]] [INFO] BracBsfa3Y=running
[[07:31:18]] [INFO] Executing action 351/641: Tap on Text: "out"
[[07:31:18]] [SUCCESS] Screenshot refreshed successfully
[[07:31:18]] [SUCCESS] Screenshot refreshed successfully
[[07:31:18]] [SUCCESS] Screenshot refreshed
[[07:31:18]] [INFO] Refreshing screenshot...
[[07:31:18]] [INFO] s6tWdQ5URW=pass
[[07:31:11]] [INFO] s6tWdQ5URW=running
[[07:31:11]] [INFO] Executing action 350/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:31:11]] [SUCCESS] Screenshot refreshed successfully
[[07:31:11]] [SUCCESS] Screenshot refreshed successfully
[[07:31:11]] [SUCCESS] Screenshot refreshed
[[07:31:11]] [INFO] Refreshing screenshot...
[[07:31:11]] [INFO] wNGRrfUjpK=pass
[[07:31:07]] [INFO] wNGRrfUjpK=running
[[07:31:07]] [INFO] Executing action 349/641: Tap on image: env[device-back-img]
[[07:31:07]] [SUCCESS] Screenshot refreshed successfully
[[07:31:07]] [SUCCESS] Screenshot refreshed successfully
[[07:31:07]] [SUCCESS] Screenshot refreshed
[[07:31:07]] [INFO] Refreshing screenshot...
[[07:31:07]] [INFO] BracBsfa3Y=pass
[[07:31:03]] [INFO] BracBsfa3Y=running
[[07:31:03]] [INFO] Executing action 348/641: Tap on Text: "Customer"
[[07:31:02]] [SUCCESS] Screenshot refreshed successfully
[[07:31:02]] [SUCCESS] Screenshot refreshed successfully
[[07:31:02]] [SUCCESS] Screenshot refreshed
[[07:31:02]] [INFO] Refreshing screenshot...
[[07:31:02]] [INFO] H4WfwVU8YP=pass
[[07:30:58]] [INFO] H4WfwVU8YP=running
[[07:30:58]] [INFO] Executing action 347/641: Tap on image: banner-close-updated.png
[[07:30:58]] [SUCCESS] Screenshot refreshed successfully
[[07:30:58]] [SUCCESS] Screenshot refreshed successfully
[[07:30:57]] [SUCCESS] Screenshot refreshed
[[07:30:57]] [INFO] Refreshing screenshot...
[[07:30:57]] [INFO] ePyaYpttQA=pass
[[07:30:54]] [INFO] ePyaYpttQA=running
[[07:30:54]] [INFO] Executing action 346/641: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[07:30:54]] [SUCCESS] Screenshot refreshed successfully
[[07:30:54]] [SUCCESS] Screenshot refreshed successfully
[[07:30:54]] [SUCCESS] Screenshot refreshed
[[07:30:54]] [INFO] Refreshing screenshot...
[[07:30:54]] [INFO] BracBsfa3Y=pass
[[07:30:50]] [INFO] BracBsfa3Y=running
[[07:30:50]] [INFO] Executing action 345/641: Tap on Text: "Invite"
[[07:30:49]] [SUCCESS] Screenshot refreshed successfully
[[07:30:49]] [SUCCESS] Screenshot refreshed successfully
[[07:30:49]] [SUCCESS] Screenshot refreshed
[[07:30:49]] [INFO] Refreshing screenshot...
[[07:30:49]] [INFO] xVbCNStsOP=pass
[[07:30:46]] [INFO] xVbCNStsOP=running
[[07:30:46]] [INFO] Executing action 344/641: Tap on image: env[device-back-img]
[[07:30:45]] [SUCCESS] Screenshot refreshed successfully
[[07:30:45]] [SUCCESS] Screenshot refreshed successfully
[[07:30:45]] [SUCCESS] Screenshot refreshed
[[07:30:45]] [INFO] Refreshing screenshot...
[[07:30:45]] [INFO] 8kQkC2FGyZ=pass
[[07:30:42]] [INFO] 8kQkC2FGyZ=running
[[07:30:42]] [INFO] Executing action 343/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[07:30:42]] [SUCCESS] Screenshot refreshed successfully
[[07:30:42]] [SUCCESS] Screenshot refreshed successfully
[[07:30:42]] [SUCCESS] Screenshot refreshed
[[07:30:42]] [INFO] Refreshing screenshot...
[[07:30:42]] [INFO] PgjJCrKFYo=pass
[[07:30:37]] [INFO] PgjJCrKFYo=running
[[07:30:37]] [INFO] Executing action 342/641: Tap on Text: "VIC"
[[07:30:37]] [SUCCESS] Screenshot refreshed successfully
[[07:30:37]] [SUCCESS] Screenshot refreshed successfully
[[07:30:37]] [SUCCESS] Screenshot refreshed
[[07:30:37]] [INFO] Refreshing screenshot...
[[07:30:37]] [INFO] 3Si0csRNaw=pass
[[07:30:30]] [INFO] 3Si0csRNaw=running
[[07:30:30]] [INFO] Executing action 341/641: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[07:30:30]] [SUCCESS] Screenshot refreshed successfully
[[07:30:30]] [SUCCESS] Screenshot refreshed successfully
[[07:30:29]] [SUCCESS] Screenshot refreshed
[[07:30:29]] [INFO] Refreshing screenshot...
[[07:30:29]] [INFO] BracBsfa3Y=pass
[[07:30:25]] [INFO] BracBsfa3Y=running
[[07:30:25]] [INFO] Executing action 340/641: Tap on Text: "Nearby"
[[07:30:25]] [SUCCESS] Screenshot refreshed successfully
[[07:30:25]] [SUCCESS] Screenshot refreshed successfully
[[07:30:24]] [SUCCESS] Screenshot refreshed
[[07:30:24]] [INFO] Refreshing screenshot...
[[07:30:24]] [INFO] BracBsfa3Y=pass
[[07:30:21]] [INFO] BracBsfa3Y=running
[[07:30:21]] [INFO] Executing action 339/641: Tap on Text: "locator"
[[07:30:20]] [SUCCESS] Screenshot refreshed successfully
[[07:30:20]] [SUCCESS] Screenshot refreshed successfully
[[07:30:20]] [SUCCESS] Screenshot refreshed
[[07:30:20]] [INFO] Refreshing screenshot...
[[07:30:20]] [INFO] s6tWdQ5URW=pass
[[07:30:13]] [INFO] s6tWdQ5URW=running
[[07:30:13]] [INFO] Executing action 338/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:30:13]] [SUCCESS] Screenshot refreshed successfully
[[07:30:13]] [SUCCESS] Screenshot refreshed successfully
[[07:30:13]] [SUCCESS] Screenshot refreshed
[[07:30:13]] [INFO] Refreshing screenshot...
[[07:30:13]] [INFO] 2M0KHOVecv=pass
[[07:30:09]] [INFO] 2M0KHOVecv=running
[[07:30:09]] [INFO] Executing action 337/641: Check if element with accessibility_id="txtMy Flybuys card" exists
[[07:30:09]] [SUCCESS] Screenshot refreshed successfully
[[07:30:09]] [SUCCESS] Screenshot refreshed successfully
[[07:30:09]] [SUCCESS] Screenshot refreshed
[[07:30:09]] [INFO] Refreshing screenshot...
[[07:30:09]] [INFO] LBgsj3oLcu=pass
[[07:30:05]] [INFO] LBgsj3oLcu=running
[[07:30:05]] [INFO] Executing action 336/641: Tap on image: env[device-back-img]
[[07:30:05]] [SUCCESS] Screenshot refreshed successfully
[[07:30:05]] [SUCCESS] Screenshot refreshed successfully
[[07:30:05]] [SUCCESS] Screenshot refreshed
[[07:30:05]] [INFO] Refreshing screenshot...
[[07:30:05]] [INFO] biRyWs3nSs=pass
[[07:29:59]] [INFO] biRyWs3nSs=running
[[07:29:59]] [INFO] Executing action 335/641: Tap on element with accessibility_id: btnSaveFlybuysCard
[[07:29:59]] [SUCCESS] Screenshot refreshed successfully
[[07:29:59]] [SUCCESS] Screenshot refreshed successfully
[[07:29:59]] [SUCCESS] Screenshot refreshed
[[07:29:59]] [INFO] Refreshing screenshot...
[[07:29:59]] [INFO] 8cFGh3GD68=pass
[[07:29:53]] [INFO] 8cFGh3GD68=running
[[07:29:53]] [INFO] Executing action 334/641: Tap on element with accessibility_id: Done
[[07:29:53]] [SUCCESS] Screenshot refreshed successfully
[[07:29:53]] [SUCCESS] Screenshot refreshed successfully
[[07:29:52]] [SUCCESS] Screenshot refreshed
[[07:29:52]] [INFO] Refreshing screenshot...
[[07:29:52]] [INFO] sLe0Wurhgm=pass
[[07:29:49]] [INFO] sLe0Wurhgm=running
[[07:29:49]] [INFO] Executing action 333/641: Input text: "2791234567890"
[[07:29:49]] [SUCCESS] Screenshot refreshed successfully
[[07:29:49]] [SUCCESS] Screenshot refreshed successfully
[[07:29:49]] [SUCCESS] Screenshot refreshed
[[07:29:49]] [INFO] Refreshing screenshot...
[[07:29:49]] [INFO] Ey86YRVRzU=pass
[[07:29:43]] [INFO] Ey86YRVRzU=running
[[07:29:43]] [INFO] Executing action 332/641: Tap on element with accessibility_id: Flybuys barcode number
[[07:29:43]] [SUCCESS] Screenshot refreshed successfully
[[07:29:43]] [SUCCESS] Screenshot refreshed successfully
[[07:29:43]] [SUCCESS] Screenshot refreshed
[[07:29:43]] [INFO] Refreshing screenshot...
[[07:29:43]] [INFO] Gxhf3XGc6e=pass
[[07:29:37]] [INFO] Gxhf3XGc6e=running
[[07:29:37]] [INFO] Executing action 331/641: Tap on element with accessibility_id: btnLinkFlyBuys
[[07:29:37]] [SUCCESS] Screenshot refreshed successfully
[[07:29:37]] [SUCCESS] Screenshot refreshed successfully
[[07:29:37]] [SUCCESS] Screenshot refreshed
[[07:29:37]] [INFO] Refreshing screenshot...
[[07:29:37]] [INFO] BracBsfa3Y=pass
[[07:29:33]] [INFO] BracBsfa3Y=running
[[07:29:33]] [INFO] Executing action 330/641: Tap on Text: "Flybuys"
[[07:29:32]] [SUCCESS] Screenshot refreshed successfully
[[07:29:32]] [SUCCESS] Screenshot refreshed successfully
[[07:29:32]] [SUCCESS] Screenshot refreshed
[[07:29:32]] [INFO] Refreshing screenshot...
[[07:29:32]] [INFO] Ds5GfNVb3x=pass
[[07:29:27]] [INFO] Ds5GfNVb3x=running
[[07:29:27]] [INFO] Executing action 329/641: Tap on element with accessibility_id: btnRemove
[[07:29:26]] [SUCCESS] Screenshot refreshed successfully
[[07:29:26]] [SUCCESS] Screenshot refreshed successfully
[[07:29:26]] [SUCCESS] Screenshot refreshed
[[07:29:26]] [INFO] Refreshing screenshot...
[[07:29:26]] [INFO] 3ZFgwFaiXp=pass
[[07:29:20]] [INFO] 3ZFgwFaiXp=running
[[07:29:20]] [INFO] Executing action 328/641: Tap on element with accessibility_id: Remove card
[[07:29:20]] [SUCCESS] Screenshot refreshed successfully
[[07:29:20]] [SUCCESS] Screenshot refreshed successfully
[[07:29:20]] [SUCCESS] Screenshot refreshed
[[07:29:20]] [INFO] Refreshing screenshot...
[[07:29:20]] [INFO] 40hnWPsQ9P=pass
[[07:29:14]] [INFO] 40hnWPsQ9P=running
[[07:29:14]] [INFO] Executing action 327/641: Tap on element with accessibility_id: btneditFlybuysCard
[[07:29:14]] [SUCCESS] Screenshot refreshed successfully
[[07:29:14]] [SUCCESS] Screenshot refreshed successfully
[[07:29:14]] [SUCCESS] Screenshot refreshed
[[07:29:14]] [INFO] Refreshing screenshot...
[[07:29:14]] [INFO] 40hnWPsQ9P=pass
[[07:29:09]] [INFO] 40hnWPsQ9P=running
[[07:29:09]] [INFO] Executing action 326/641: Wait till accessibility_id=btneditFlybuysCard
[[07:29:09]] [SUCCESS] Screenshot refreshed successfully
[[07:29:09]] [SUCCESS] Screenshot refreshed successfully
[[07:29:09]] [SUCCESS] Screenshot refreshed
[[07:29:09]] [INFO] Refreshing screenshot...
[[07:29:09]] [INFO] BracBsfa3Y=pass
[[07:29:05]] [INFO] BracBsfa3Y=running
[[07:29:05]] [INFO] Executing action 325/641: Tap on Text: "Flybuys"
[[07:29:05]] [SUCCESS] Screenshot refreshed successfully
[[07:29:05]] [SUCCESS] Screenshot refreshed successfully
[[07:29:04]] [SUCCESS] Screenshot refreshed
[[07:29:04]] [INFO] Refreshing screenshot...
[[07:29:04]] [INFO] MkTFxfzubv=pass
[[07:29:01]] [INFO] MkTFxfzubv=running
[[07:29:01]] [INFO] Executing action 324/641: Tap on image: env[device-back-img]
[[07:29:00]] [SUCCESS] Screenshot refreshed successfully
[[07:29:00]] [SUCCESS] Screenshot refreshed successfully
[[07:29:00]] [SUCCESS] Screenshot refreshed
[[07:29:00]] [INFO] Refreshing screenshot...
[[07:29:00]] [INFO] EO3cMmdUyM=pass
[[07:28:57]] [INFO] EO3cMmdUyM=running
[[07:28:57]] [INFO] Executing action 323/641: Tap on image: env[device-back-img]
[[07:28:56]] [SUCCESS] Screenshot refreshed successfully
[[07:28:56]] [SUCCESS] Screenshot refreshed successfully
[[07:28:56]] [SUCCESS] Screenshot refreshed
[[07:28:56]] [INFO] Refreshing screenshot...
[[07:28:56]] [INFO] napKDohf3Z=pass
[[07:28:52]] [INFO] napKDohf3Z=running
[[07:28:52]] [INFO] Executing action 322/641: Tap on Text: "payment"
[[07:28:52]] [SUCCESS] Screenshot refreshed successfully
[[07:28:52]] [SUCCESS] Screenshot refreshed successfully
[[07:28:51]] [SUCCESS] Screenshot refreshed
[[07:28:51]] [INFO] Refreshing screenshot...
[[07:28:51]] [INFO] ekqt95ZRol=pass
[[07:28:48]] [INFO] ekqt95ZRol=running
[[07:28:48]] [INFO] Executing action 321/641: Tap on image: env[device-back-img]
[[07:28:48]] [SUCCESS] Screenshot refreshed successfully
[[07:28:48]] [SUCCESS] Screenshot refreshed successfully
[[07:28:47]] [SUCCESS] Screenshot refreshed
[[07:28:47]] [INFO] Refreshing screenshot...
[[07:28:47]] [INFO] 20qUCJgpE9=pass
[[07:28:43]] [INFO] 20qUCJgpE9=running
[[07:28:43]] [INFO] Executing action 320/641: Tap on Text: "address"
[[07:28:43]] [SUCCESS] Screenshot refreshed successfully
[[07:28:43]] [SUCCESS] Screenshot refreshed successfully
[[07:28:43]] [SUCCESS] Screenshot refreshed
[[07:28:43]] [INFO] Refreshing screenshot...
[[07:28:43]] [INFO] 6HR2weiXoT=pass
[[07:28:39]] [INFO] 6HR2weiXoT=running
[[07:28:39]] [INFO] Executing action 319/641: Tap on image: env[device-back-img]
[[07:28:39]] [SUCCESS] Screenshot refreshed successfully
[[07:28:39]] [SUCCESS] Screenshot refreshed successfully
[[07:28:39]] [SUCCESS] Screenshot refreshed
[[07:28:39]] [INFO] Refreshing screenshot...
[[07:28:39]] [INFO] 3hOTINBVMf=pass
[[07:28:35]] [INFO] 3hOTINBVMf=running
[[07:28:35]] [INFO] Executing action 318/641: Tap on Text: "details"
[[07:28:34]] [SUCCESS] Screenshot refreshed successfully
[[07:28:34]] [SUCCESS] Screenshot refreshed successfully
[[07:28:34]] [SUCCESS] Screenshot refreshed
[[07:28:34]] [INFO] Refreshing screenshot...
[[07:28:34]] [INFO] yJi0WxnERj=pass
[[07:28:30]] [INFO] yJi0WxnERj=running
[[07:28:30]] [INFO] Executing action 317/641: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[07:28:30]] [SUCCESS] Screenshot refreshed successfully
[[07:28:30]] [SUCCESS] Screenshot refreshed successfully
[[07:28:30]] [SUCCESS] Screenshot refreshed
[[07:28:30]] [INFO] Refreshing screenshot...
[[07:28:30]] [INFO] PbfHAtFQPP=pass
[[07:28:26]] [INFO] PbfHAtFQPP=running
[[07:28:26]] [INFO] Executing action 316/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:28:26]] [SUCCESS] Screenshot refreshed successfully
[[07:28:26]] [SUCCESS] Screenshot refreshed successfully
[[07:28:26]] [SUCCESS] Screenshot refreshed
[[07:28:26]] [INFO] Refreshing screenshot...
[[07:28:26]] [INFO] 6qZnk86hGg=pass
[[07:28:22]] [INFO] 6qZnk86hGg=running
[[07:28:22]] [INFO] Executing action 315/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:28:21]] [SUCCESS] Screenshot refreshed successfully
[[07:28:21]] [SUCCESS] Screenshot refreshed successfully
[[07:28:21]] [SUCCESS] Screenshot refreshed
[[07:28:21]] [INFO] Refreshing screenshot...
[[07:28:21]] [INFO] FAvQgIuHc1=pass
[[07:28:17]] [INFO] FAvQgIuHc1=running
[[07:28:17]] [INFO] Executing action 314/641: Tap on Text: "Return"
[[07:28:17]] [SUCCESS] Screenshot refreshed successfully
[[07:28:17]] [SUCCESS] Screenshot refreshed successfully
[[07:28:16]] [SUCCESS] Screenshot refreshed
[[07:28:16]] [INFO] Refreshing screenshot...
[[07:28:16]] [INFO] vmc01sHkbr=pass
[[07:28:10]] [INFO] vmc01sHkbr=running
[[07:28:10]] [INFO] Executing action 313/641: Wait for 5 ms
[[07:28:10]] [SUCCESS] Screenshot refreshed successfully
[[07:28:10]] [SUCCESS] Screenshot refreshed successfully
[[07:28:10]] [SUCCESS] Screenshot refreshed
[[07:28:10]] [INFO] Refreshing screenshot...
[[07:28:10]] [INFO] zeu0wd1vcF=pass
[[07:27:57]] [INFO] zeu0wd1vcF=running
[[07:27:57]] [INFO] Executing action 312/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:27:56]] [SUCCESS] Screenshot refreshed successfully
[[07:27:56]] [SUCCESS] Screenshot refreshed successfully
[[07:27:56]] [SUCCESS] Screenshot refreshed
[[07:27:56]] [INFO] Refreshing screenshot...
[[07:27:56]] [INFO] OwWeZes4aT=pass
[[07:27:53]] [INFO] OwWeZes4aT=running
[[07:27:53]] [INFO] Executing action 311/641: Tap on image: env[device-back-img]
[[07:27:52]] [SUCCESS] Screenshot refreshed successfully
[[07:27:52]] [SUCCESS] Screenshot refreshed successfully
[[07:27:52]] [SUCCESS] Screenshot refreshed
[[07:27:52]] [INFO] Refreshing screenshot...
[[07:27:52]] [INFO] aAaTtUE92h=pass
[[07:27:49]] [INFO] aAaTtUE92h=running
[[07:27:49]] [INFO] Executing action 310/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[07:27:49]] [SUCCESS] Screenshot refreshed successfully
[[07:27:49]] [SUCCESS] Screenshot refreshed successfully
[[07:27:49]] [SUCCESS] Screenshot refreshed
[[07:27:49]] [INFO] Refreshing screenshot...
[[07:27:49]] [INFO] 9iOZGMqAZK=pass
[[07:27:45]] [INFO] 9iOZGMqAZK=running
[[07:27:45]] [INFO] Executing action 309/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[07:27:45]] [SUCCESS] Screenshot refreshed successfully
[[07:27:45]] [SUCCESS] Screenshot refreshed successfully
[[07:27:44]] [SUCCESS] Screenshot refreshed
[[07:27:44]] [INFO] Refreshing screenshot...
[[07:27:44]] [INFO] mRTYzOFRRw=pass
[[07:27:42]] [INFO] mRTYzOFRRw=running
[[07:27:42]] [INFO] Executing action 308/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[07:27:41]] [SUCCESS] Screenshot refreshed successfully
[[07:27:41]] [SUCCESS] Screenshot refreshed successfully
[[07:27:41]] [SUCCESS] Screenshot refreshed
[[07:27:41]] [INFO] Refreshing screenshot...
[[07:27:41]] [INFO] 7g6MFJSGIO=pass
[[07:27:37]] [INFO] 7g6MFJSGIO=running
[[07:27:37]] [INFO] Executing action 307/641: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[07:27:37]] [SUCCESS] Screenshot refreshed successfully
[[07:27:37]] [SUCCESS] Screenshot refreshed successfully
[[07:27:37]] [SUCCESS] Screenshot refreshed
[[07:27:37]] [INFO] Refreshing screenshot...
[[07:27:37]] [INFO] zNwyPagPE1=pass
[[07:27:30]] [INFO] zNwyPagPE1=running
[[07:27:30]] [INFO] Executing action 306/641: Wait for 5 ms
[[07:27:30]] [SUCCESS] Screenshot refreshed successfully
[[07:27:30]] [SUCCESS] Screenshot refreshed successfully
[[07:27:30]] [SUCCESS] Screenshot refreshed
[[07:27:30]] [INFO] Refreshing screenshot...
[[07:27:30]] [INFO] qXsL3wzg6J=pass
[[07:27:26]] [INFO] qXsL3wzg6J=running
[[07:27:26]] [INFO] Executing action 305/641: Tap on image: env[device-back-img]
[[07:27:26]] [SUCCESS] Screenshot refreshed successfully
[[07:27:26]] [SUCCESS] Screenshot refreshed successfully
[[07:27:26]] [SUCCESS] Screenshot refreshed
[[07:27:26]] [INFO] Refreshing screenshot...
[[07:27:26]] [INFO] YuuQe2KupX=pass
[[07:27:21]] [INFO] YuuQe2KupX=running
[[07:27:21]] [INFO] Executing action 304/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[07:27:21]] [SUCCESS] Screenshot refreshed successfully
[[07:27:21]] [SUCCESS] Screenshot refreshed successfully
[[07:27:21]] [SUCCESS] Screenshot refreshed
[[07:27:21]] [INFO] Refreshing screenshot...
[[07:27:21]] [INFO] g0PE7Mofye=pass
[[07:27:15]] [INFO] g0PE7Mofye=running
[[07:27:15]] [INFO] Executing action 303/641: Tap on element with accessibility_id: Print order details
[[07:27:15]] [SUCCESS] Screenshot refreshed successfully
[[07:27:15]] [SUCCESS] Screenshot refreshed successfully
[[07:27:15]] [SUCCESS] Screenshot refreshed
[[07:27:15]] [INFO] Refreshing screenshot...
[[07:27:15]] [INFO] GgQaBLWYkb=pass
[[07:27:11]] [INFO] GgQaBLWYkb=running
[[07:27:11]] [INFO] Executing action 302/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[07:27:11]] [SUCCESS] Screenshot refreshed successfully
[[07:27:11]] [SUCCESS] Screenshot refreshed successfully
[[07:27:10]] [SUCCESS] Screenshot refreshed
[[07:27:10]] [INFO] Refreshing screenshot...
[[07:27:10]] [INFO] f3OrHHzTFN=pass
[[07:26:55]] [INFO] f3OrHHzTFN=running
[[07:26:55]] [INFO] Executing action 301/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[07:26:54]] [SUCCESS] Screenshot refreshed successfully
[[07:26:54]] [SUCCESS] Screenshot refreshed successfully
[[07:26:54]] [SUCCESS] Screenshot refreshed
[[07:26:54]] [INFO] Refreshing screenshot...
[[07:26:54]] [INFO] 7g6MFJSGIO=pass
[[07:26:50]] [INFO] 7g6MFJSGIO=running
[[07:26:50]] [INFO] Executing action 300/641: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[07:26:50]] [SUCCESS] Screenshot refreshed successfully
[[07:26:50]] [SUCCESS] Screenshot refreshed successfully
[[07:26:50]] [SUCCESS] Screenshot refreshed
[[07:26:50]] [INFO] Refreshing screenshot...
[[07:26:50]] [INFO] Z6g3sGuHTp=pass
[[07:26:43]] [INFO] Z6g3sGuHTp=running
[[07:26:43]] [INFO] Executing action 299/641: Wait for 5 ms
[[07:26:43]] [SUCCESS] Screenshot refreshed successfully
[[07:26:43]] [SUCCESS] Screenshot refreshed successfully
[[07:26:43]] [SUCCESS] Screenshot refreshed
[[07:26:43]] [INFO] Refreshing screenshot...
[[07:26:43]] [INFO] pFlYwTS53v=pass
[[07:26:39]] [INFO] pFlYwTS53v=running
[[07:26:39]] [INFO] Executing action 298/641: Tap on Text: "receipts"
[[07:26:39]] [SUCCESS] Screenshot refreshed successfully
[[07:26:39]] [SUCCESS] Screenshot refreshed successfully
[[07:26:38]] [SUCCESS] Screenshot refreshed
[[07:26:38]] [INFO] Refreshing screenshot...
[[07:26:38]] [INFO] V59u3l1wkM=pass
[[07:26:35]] [INFO] V59u3l1wkM=running
[[07:26:35]] [INFO] Executing action 297/641: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[07:26:35]] [SUCCESS] Screenshot refreshed successfully
[[07:26:35]] [SUCCESS] Screenshot refreshed successfully
[[07:26:35]] [SUCCESS] Screenshot refreshed
[[07:26:35]] [INFO] Refreshing screenshot...
[[07:26:35]] [INFO] sl3Wk1gK8X=pass
[[07:26:31]] [SUCCESS] Screenshot refreshed successfully
[[07:26:31]] [SUCCESS] Screenshot refreshed successfully
[[07:26:31]] [INFO] sl3Wk1gK8X=running
[[07:26:31]] [INFO] Executing action 296/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:26:31]] [SUCCESS] Screenshot refreshed
[[07:26:31]] [INFO] Refreshing screenshot...
[[07:26:30]] [SUCCESS] Screenshot refreshed successfully
[[07:26:30]] [SUCCESS] Screenshot refreshed successfully
[[07:26:30]] [SUCCESS] Screenshot refreshed
[[07:26:30]] [INFO] Refreshing screenshot...
[[07:26:26]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[07:26:26]] [SUCCESS] Screenshot refreshed successfully
[[07:26:26]] [SUCCESS] Screenshot refreshed successfully
[[07:26:25]] [SUCCESS] Screenshot refreshed
[[07:26:25]] [INFO] Refreshing screenshot...
[[07:26:21]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:26:21]] [SUCCESS] Screenshot refreshed successfully
[[07:26:21]] [SUCCESS] Screenshot refreshed successfully
[[07:26:21]] [SUCCESS] Screenshot refreshed
[[07:26:21]] [INFO] Refreshing screenshot...
[[07:26:16]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[07:26:16]] [SUCCESS] Screenshot refreshed successfully
[[07:26:16]] [SUCCESS] Screenshot refreshed successfully
[[07:26:16]] [SUCCESS] Screenshot refreshed
[[07:26:16]] [INFO] Refreshing screenshot...
[[07:26:12]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:26:11]] [SUCCESS] Screenshot refreshed successfully
[[07:26:11]] [SUCCESS] Screenshot refreshed successfully
[[07:26:11]] [SUCCESS] Screenshot refreshed
[[07:26:11]] [INFO] Refreshing screenshot...
[[07:26:06]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:26:06]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[07:26:06]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[07:26:06]] [INFO] vjK6GqOF3r=running
[[07:26:06]] [INFO] Executing action 295/641: Execute Test Case: Kmart-Signin (8 steps)
[[07:26:05]] [SUCCESS] Screenshot refreshed successfully
[[07:26:05]] [SUCCESS] Screenshot refreshed successfully
[[07:26:05]] [SUCCESS] Screenshot refreshed
[[07:26:05]] [INFO] Refreshing screenshot...
[[07:26:05]] [INFO] ly2oT3zqmf=pass
[[07:26:03]] [INFO] ly2oT3zqmf=running
[[07:26:03]] [INFO] Executing action 294/641: iOS Function: alert_accept
[[07:26:02]] [SUCCESS] Screenshot refreshed successfully
[[07:26:02]] [SUCCESS] Screenshot refreshed successfully
[[07:26:02]] [SUCCESS] Screenshot refreshed
[[07:26:02]] [INFO] Refreshing screenshot...
[[07:26:02]] [INFO] xAPeBnVHrT=pass
[[07:25:55]] [INFO] xAPeBnVHrT=running
[[07:25:55]] [INFO] Executing action 293/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:25:55]] [SUCCESS] Screenshot refreshed successfully
[[07:25:55]] [SUCCESS] Screenshot refreshed successfully
[[07:25:55]] [SUCCESS] Screenshot refreshed
[[07:25:55]] [INFO] Refreshing screenshot...
[[07:25:55]] [INFO] u6bRYZZFAv=pass
[[07:25:48]] [INFO] u6bRYZZFAv=running
[[07:25:48]] [INFO] Executing action 292/641: Wait for 5 ms
[[07:25:48]] [SUCCESS] Screenshot refreshed successfully
[[07:25:48]] [SUCCESS] Screenshot refreshed successfully
[[07:25:48]] [SUCCESS] Screenshot refreshed
[[07:25:48]] [INFO] Refreshing screenshot...
[[07:25:48]] [INFO] pjFNt3w5Fr=pass
[[07:25:34]] [SUCCESS] Screenshot refreshed successfully
[[07:25:34]] [SUCCESS] Screenshot refreshed successfully
[[07:25:34]] [INFO] pjFNt3w5Fr=running
[[07:25:34]] [INFO] Executing action 291/641: Restart app: env[appid]
[[07:25:34]] [SUCCESS] Screenshot refreshed
[[07:25:34]] [INFO] Refreshing screenshot...
[[07:25:34]] [SUCCESS] Screenshot refreshed successfully
[[07:25:34]] [SUCCESS] Screenshot refreshed successfully
[[07:25:34]] [SUCCESS] Screenshot refreshed
[[07:25:34]] [INFO] Refreshing screenshot...
[[07:25:31]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:25:31]] [SUCCESS] Screenshot refreshed successfully
[[07:25:31]] [SUCCESS] Screenshot refreshed successfully
[[07:25:31]] [SUCCESS] Screenshot refreshed
[[07:25:31]] [INFO] Refreshing screenshot...
[[07:25:18]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:25:18]] [SUCCESS] Screenshot refreshed successfully
[[07:25:18]] [SUCCESS] Screenshot refreshed successfully
[[07:25:18]] [SUCCESS] Screenshot refreshed
[[07:25:18]] [INFO] Refreshing screenshot...
[[07:25:14]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:25:14]] [SUCCESS] Screenshot refreshed successfully
[[07:25:14]] [SUCCESS] Screenshot refreshed successfully
[[07:25:14]] [SUCCESS] Screenshot refreshed
[[07:25:14]] [INFO] Refreshing screenshot...
[[07:25:10]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:25:10]] [SUCCESS] Screenshot refreshed successfully
[[07:25:10]] [SUCCESS] Screenshot refreshed successfully
[[07:25:09]] [SUCCESS] Screenshot refreshed
[[07:25:09]] [INFO] Refreshing screenshot...
[[07:25:03]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:25:03]] [SUCCESS] Screenshot refreshed successfully
[[07:25:03]] [SUCCESS] Screenshot refreshed successfully
[[07:25:02]] [SUCCESS] Screenshot refreshed
[[07:25:02]] [INFO] Refreshing screenshot...
[[07:24:57]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:24:57]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:24:57]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:24:57]] [INFO] PGvsG6rpU4=running
[[07:24:57]] [INFO] Executing action 290/641: cleanupSteps action
[[07:24:57]] [SUCCESS] Screenshot refreshed successfully
[[07:24:57]] [SUCCESS] Screenshot refreshed successfully
[[07:24:56]] [SUCCESS] Screenshot refreshed
[[07:24:56]] [INFO] Refreshing screenshot...
[[07:24:56]] [INFO] LzGkAcsQyE=pass
[[07:24:54]] [INFO] LzGkAcsQyE=running
[[07:24:54]] [INFO] Executing action 289/641: Terminate app: env[appid]
[[07:24:54]] [SUCCESS] Screenshot refreshed successfully
[[07:24:54]] [SUCCESS] Screenshot refreshed successfully
[[07:24:53]] [SUCCESS] Screenshot refreshed
[[07:24:53]] [INFO] Refreshing screenshot...
[[07:24:53]] [INFO] Bdhe5AoUlM=pass
[[07:24:49]] [INFO] Bdhe5AoUlM=running
[[07:24:49]] [INFO] Executing action 288/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:24:49]] [SUCCESS] Screenshot refreshed successfully
[[07:24:49]] [SUCCESS] Screenshot refreshed successfully
[[07:24:49]] [SUCCESS] Screenshot refreshed
[[07:24:49]] [INFO] Refreshing screenshot...
[[07:24:49]] [INFO] FciJcOsMsB=pass
[[07:24:42]] [INFO] FciJcOsMsB=running
[[07:24:42]] [INFO] Executing action 287/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:24:42]] [SUCCESS] Screenshot refreshed successfully
[[07:24:42]] [SUCCESS] Screenshot refreshed successfully
[[07:24:42]] [SUCCESS] Screenshot refreshed
[[07:24:42]] [INFO] Refreshing screenshot...
[[07:24:42]] [INFO] FARWZvOj0x=pass
[[07:24:38]] [INFO] FARWZvOj0x=running
[[07:24:38]] [INFO] Executing action 286/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:24:37]] [SUCCESS] Screenshot refreshed successfully
[[07:24:37]] [SUCCESS] Screenshot refreshed successfully
[[07:24:37]] [SUCCESS] Screenshot refreshed
[[07:24:37]] [INFO] Refreshing screenshot...
[[07:24:37]] [INFO] bZCkx4U9Gk=pass
[[07:24:33]] [INFO] bZCkx4U9Gk=running
[[07:24:33]] [INFO] Executing action 285/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:24:32]] [SUCCESS] Screenshot refreshed successfully
[[07:24:32]] [SUCCESS] Screenshot refreshed successfully
[[07:24:32]] [SUCCESS] Screenshot refreshed
[[07:24:32]] [INFO] Refreshing screenshot...
[[07:24:32]] [INFO] vwFwkK6ydQ=pass
[[07:24:28]] [INFO] vwFwkK6ydQ=running
[[07:24:28]] [INFO] Executing action 284/641: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[07:24:28]] [SUCCESS] Screenshot refreshed successfully
[[07:24:28]] [SUCCESS] Screenshot refreshed successfully
[[07:24:28]] [SUCCESS] Screenshot refreshed
[[07:24:28]] [INFO] Refreshing screenshot...
[[07:24:28]] [INFO] xLGm9FefWE=pass
[[07:24:23]] [INFO] xLGm9FefWE=running
[[07:24:23]] [INFO] Executing action 283/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[07:24:23]] [SUCCESS] Screenshot refreshed successfully
[[07:24:23]] [SUCCESS] Screenshot refreshed successfully
[[07:24:23]] [SUCCESS] Screenshot refreshed
[[07:24:23]] [INFO] Refreshing screenshot...
[[07:24:23]] [INFO] UtVRXwa86e=pass
[[07:24:16]] [INFO] UtVRXwa86e=running
[[07:24:16]] [INFO] Executing action 282/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[07:24:16]] [SUCCESS] Screenshot refreshed successfully
[[07:24:16]] [SUCCESS] Screenshot refreshed successfully
[[07:24:16]] [SUCCESS] Screenshot refreshed
[[07:24:16]] [INFO] Refreshing screenshot...
[[07:24:16]] [INFO] SDtskxyVpg=pass
[[07:24:12]] [INFO] SDtskxyVpg=running
[[07:24:12]] [INFO] Executing action 281/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:24:12]] [SUCCESS] Screenshot refreshed successfully
[[07:24:12]] [SUCCESS] Screenshot refreshed successfully
[[07:24:11]] [SUCCESS] Screenshot refreshed
[[07:24:11]] [INFO] Refreshing screenshot...
[[07:24:11]] [INFO] 6HhScBaqQp=pass
[[07:24:09]] [INFO] 6HhScBaqQp=running
[[07:24:09]] [INFO] Executing action 280/641: iOS Function: alert_accept
[[07:24:09]] [SUCCESS] Screenshot refreshed successfully
[[07:24:09]] [SUCCESS] Screenshot refreshed successfully
[[07:24:08]] [SUCCESS] Screenshot refreshed
[[07:24:08]] [INFO] Refreshing screenshot...
[[07:24:08]] [INFO] quzlwPw42x=pass
[[07:24:02]] [INFO] quzlwPw42x=running
[[07:24:02]] [INFO] Executing action 279/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:24:02]] [SUCCESS] Screenshot refreshed successfully
[[07:24:02]] [SUCCESS] Screenshot refreshed successfully
[[07:24:02]] [SUCCESS] Screenshot refreshed
[[07:24:02]] [INFO] Refreshing screenshot...
[[07:24:02]] [INFO] jQYHQIvQ8l=pass
[[07:23:58]] [INFO] jQYHQIvQ8l=running
[[07:23:58]] [INFO] Executing action 278/641: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[07:23:58]] [SUCCESS] Screenshot refreshed successfully
[[07:23:58]] [SUCCESS] Screenshot refreshed successfully
[[07:23:58]] [SUCCESS] Screenshot refreshed
[[07:23:58]] [INFO] Refreshing screenshot...
[[07:23:58]] [INFO] ts3qyFxyMf=pass
[[07:23:54]] [INFO] ts3qyFxyMf=running
[[07:23:54]] [INFO] Executing action 277/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:23:54]] [SUCCESS] Screenshot refreshed successfully
[[07:23:54]] [SUCCESS] Screenshot refreshed successfully
[[07:23:54]] [SUCCESS] Screenshot refreshed
[[07:23:54]] [INFO] Refreshing screenshot...
[[07:23:54]] [INFO] FciJcOsMsB=pass
[[07:23:47]] [INFO] FciJcOsMsB=running
[[07:23:47]] [INFO] Executing action 276/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:23:46]] [SUCCESS] Screenshot refreshed successfully
[[07:23:46]] [SUCCESS] Screenshot refreshed successfully
[[07:23:46]] [SUCCESS] Screenshot refreshed
[[07:23:46]] [INFO] Refreshing screenshot...
[[07:23:46]] [INFO] CWkqGp5ndO=pass
[[07:23:42]] [INFO] CWkqGp5ndO=running
[[07:23:42]] [INFO] Executing action 275/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:23:42]] [SUCCESS] Screenshot refreshed successfully
[[07:23:42]] [SUCCESS] Screenshot refreshed successfully
[[07:23:42]] [SUCCESS] Screenshot refreshed
[[07:23:42]] [INFO] Refreshing screenshot...
[[07:23:42]] [INFO] KfMHchi8cx=pass
[[07:23:35]] [INFO] KfMHchi8cx=running
[[07:23:35]] [INFO] Executing action 274/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:23:35]] [SUCCESS] Screenshot refreshed successfully
[[07:23:35]] [SUCCESS] Screenshot refreshed successfully
[[07:23:34]] [SUCCESS] Screenshot refreshed
[[07:23:34]] [INFO] Refreshing screenshot...
[[07:23:34]] [INFO] zsVeGHiIgX=pass
[[07:23:32]] [INFO] zsVeGHiIgX=running
[[07:23:32]] [INFO] Executing action 273/641: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[07:23:31]] [SUCCESS] Screenshot refreshed successfully
[[07:23:31]] [SUCCESS] Screenshot refreshed successfully
[[07:23:31]] [SUCCESS] Screenshot refreshed
[[07:23:31]] [INFO] Refreshing screenshot...
[[07:23:31]] [INFO] 5nsUXQ5L7u=pass
[[07:23:28]] [INFO] 5nsUXQ5L7u=running
[[07:23:28]] [INFO] Executing action 272/641: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[07:23:28]] [SUCCESS] Screenshot refreshed successfully
[[07:23:28]] [SUCCESS] Screenshot refreshed successfully
[[07:23:28]] [SUCCESS] Screenshot refreshed
[[07:23:28]] [INFO] Refreshing screenshot...
[[07:23:28]] [INFO] iSckENpXrN=pass
[[07:23:25]] [INFO] iSckENpXrN=running
[[07:23:25]] [INFO] Executing action 271/641: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[07:23:25]] [SUCCESS] Screenshot refreshed successfully
[[07:23:25]] [SUCCESS] Screenshot refreshed successfully
[[07:23:24]] [SUCCESS] Screenshot refreshed
[[07:23:24]] [INFO] Refreshing screenshot...
[[07:23:24]] [INFO] J7BPGVnRJI=pass
[[07:23:22]] [INFO] J7BPGVnRJI=running
[[07:23:22]] [INFO] Executing action 270/641: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[07:23:21]] [SUCCESS] Screenshot refreshed successfully
[[07:23:21]] [SUCCESS] Screenshot refreshed successfully
[[07:23:21]] [SUCCESS] Screenshot refreshed
[[07:23:21]] [INFO] Refreshing screenshot...
[[07:23:21]] [INFO] 0pwZCYAtOv=pass
[[07:23:18]] [INFO] 0pwZCYAtOv=running
[[07:23:18]] [INFO] Executing action 269/641: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[07:23:18]] [SUCCESS] Screenshot refreshed successfully
[[07:23:18]] [SUCCESS] Screenshot refreshed successfully
[[07:23:18]] [SUCCESS] Screenshot refreshed
[[07:23:18]] [INFO] Refreshing screenshot...
[[07:23:18]] [INFO] soKM0KayFJ=pass
[[07:23:15]] [INFO] soKM0KayFJ=running
[[07:23:15]] [INFO] Executing action 268/641: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[07:23:15]] [SUCCESS] Screenshot refreshed successfully
[[07:23:15]] [SUCCESS] Screenshot refreshed successfully
[[07:23:14]] [SUCCESS] Screenshot refreshed
[[07:23:14]] [INFO] Refreshing screenshot...
[[07:23:14]] [INFO] hnH3ayslCh=pass
[[07:23:11]] [INFO] hnH3ayslCh=running
[[07:23:11]] [INFO] Executing action 267/641: Tap on Text: "Passcode"
[[07:23:11]] [SUCCESS] Screenshot refreshed successfully
[[07:23:11]] [SUCCESS] Screenshot refreshed successfully
[[07:23:11]] [SUCCESS] Screenshot refreshed
[[07:23:11]] [INFO] Refreshing screenshot...
[[07:23:11]] [INFO] CzVeOTdAX9=pass
[[07:23:00]] [INFO] CzVeOTdAX9=running
[[07:23:00]] [INFO] Executing action 266/641: Wait for 10 ms
[[07:22:59]] [SUCCESS] Screenshot refreshed successfully
[[07:22:59]] [SUCCESS] Screenshot refreshed successfully
[[07:22:59]] [SUCCESS] Screenshot refreshed
[[07:22:59]] [INFO] Refreshing screenshot...
[[07:22:59]] [INFO] NL2gtj6qIu=pass
[[07:22:55]] [INFO] NL2gtj6qIu=running
[[07:22:55]] [INFO] Executing action 265/641: Tap on Text: "Apple"
[[07:22:55]] [SUCCESS] Screenshot refreshed successfully
[[07:22:55]] [SUCCESS] Screenshot refreshed successfully
[[07:22:54]] [SUCCESS] Screenshot refreshed
[[07:22:54]] [INFO] Refreshing screenshot...
[[07:22:54]] [INFO] VsSlyhXuVD=pass
[[07:22:50]] [INFO] VsSlyhXuVD=running
[[07:22:50]] [INFO] Executing action 264/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:22:50]] [SUCCESS] Screenshot refreshed successfully
[[07:22:50]] [SUCCESS] Screenshot refreshed successfully
[[07:22:50]] [SUCCESS] Screenshot refreshed
[[07:22:50]] [INFO] Refreshing screenshot...
[[07:22:50]] [INFO] CJ88OgjKXp=pass
[[07:22:46]] [INFO] CJ88OgjKXp=running
[[07:22:46]] [INFO] Executing action 263/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:22:46]] [SUCCESS] Screenshot refreshed successfully
[[07:22:46]] [SUCCESS] Screenshot refreshed successfully
[[07:22:45]] [SUCCESS] Screenshot refreshed
[[07:22:45]] [INFO] Refreshing screenshot...
[[07:22:45]] [INFO] AYiwFSLTBD=pass
[[07:22:43]] [INFO] AYiwFSLTBD=running
[[07:22:43]] [INFO] Executing action 262/641: iOS Function: alert_accept
[[07:22:43]] [SUCCESS] Screenshot refreshed successfully
[[07:22:43]] [SUCCESS] Screenshot refreshed successfully
[[07:22:42]] [SUCCESS] Screenshot refreshed
[[07:22:42]] [INFO] Refreshing screenshot...
[[07:22:42]] [INFO] HJzOYZNnGr=pass
[[07:22:37]] [INFO] HJzOYZNnGr=running
[[07:22:37]] [INFO] Executing action 261/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:22:36]] [SUCCESS] Screenshot refreshed successfully
[[07:22:36]] [SUCCESS] Screenshot refreshed successfully
[[07:22:36]] [SUCCESS] Screenshot refreshed
[[07:22:36]] [INFO] Refreshing screenshot...
[[07:22:36]] [INFO] taf19mtrUT=pass
[[07:22:33]] [INFO] taf19mtrUT=running
[[07:22:33]] [INFO] Executing action 260/641: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[07:22:32]] [SUCCESS] Screenshot refreshed successfully
[[07:22:32]] [SUCCESS] Screenshot refreshed successfully
[[07:22:32]] [SUCCESS] Screenshot refreshed
[[07:22:32]] [INFO] Refreshing screenshot...
[[07:22:32]] [INFO] oiPcknTonJ=pass
[[07:22:28]] [INFO] oiPcknTonJ=running
[[07:22:28]] [INFO] Executing action 259/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:22:28]] [SUCCESS] Screenshot refreshed successfully
[[07:22:28]] [SUCCESS] Screenshot refreshed successfully
[[07:22:28]] [SUCCESS] Screenshot refreshed
[[07:22:28]] [INFO] Refreshing screenshot...
[[07:22:28]] [INFO] FciJcOsMsB=pass
[[07:22:22]] [INFO] FciJcOsMsB=running
[[07:22:22]] [INFO] Executing action 258/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:22:22]] [SUCCESS] Screenshot refreshed successfully
[[07:22:22]] [SUCCESS] Screenshot refreshed successfully
[[07:22:22]] [SUCCESS] Screenshot refreshed
[[07:22:22]] [INFO] Refreshing screenshot...
[[07:22:22]] [INFO] 2qOXZcEmK8=pass
[[07:22:18]] [INFO] 2qOXZcEmK8=running
[[07:22:18]] [INFO] Executing action 257/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:22:18]] [SUCCESS] Screenshot refreshed successfully
[[07:22:18]] [SUCCESS] Screenshot refreshed successfully
[[07:22:17]] [SUCCESS] Screenshot refreshed
[[07:22:17]] [INFO] Refreshing screenshot...
[[07:22:17]] [INFO] M6HdLxu76S=pass
[[07:22:13]] [INFO] M6HdLxu76S=running
[[07:22:13]] [INFO] Executing action 256/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:22:13]] [SUCCESS] Screenshot refreshed successfully
[[07:22:13]] [SUCCESS] Screenshot refreshed successfully
[[07:22:13]] [SUCCESS] Screenshot refreshed
[[07:22:13]] [INFO] Refreshing screenshot...
[[07:22:13]] [INFO] 6wYIn0igez=pass
[[07:22:08]] [INFO] 6wYIn0igez=running
[[07:22:08]] [INFO] Executing action 255/641: iOS Function: text - Text: "Wonderbaby@6"
[[07:22:08]] [SUCCESS] Screenshot refreshed successfully
[[07:22:08]] [SUCCESS] Screenshot refreshed successfully
[[07:22:08]] [SUCCESS] Screenshot refreshed
[[07:22:08]] [INFO] Refreshing screenshot...
[[07:22:08]] [INFO] DaVBARRwft=pass
[[07:22:03]] [INFO] DaVBARRwft=running
[[07:22:03]] [INFO] Executing action 254/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[07:22:03]] [SUCCESS] Screenshot refreshed successfully
[[07:22:03]] [SUCCESS] Screenshot refreshed successfully
[[07:22:03]] [SUCCESS] Screenshot refreshed
[[07:22:03]] [INFO] Refreshing screenshot...
[[07:22:03]] [INFO] Gb2Do6AtSN=pass
[[07:21:58]] [INFO] Gb2Do6AtSN=running
[[07:21:58]] [INFO] Executing action 253/641: iOS Function: text - Text: "<EMAIL>"
[[07:21:58]] [SUCCESS] Screenshot refreshed successfully
[[07:21:58]] [SUCCESS] Screenshot refreshed successfully
[[07:21:58]] [SUCCESS] Screenshot refreshed
[[07:21:58]] [INFO] Refreshing screenshot...
[[07:21:58]] [INFO] y8ZMTkG38M=pass
[[07:21:54]] [INFO] y8ZMTkG38M=running
[[07:21:54]] [INFO] Executing action 252/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[07:21:53]] [SUCCESS] Screenshot refreshed successfully
[[07:21:53]] [SUCCESS] Screenshot refreshed successfully
[[07:21:53]] [SUCCESS] Screenshot refreshed
[[07:21:53]] [INFO] Refreshing screenshot...
[[07:21:53]] [INFO] UUhQjmzfO2=pass
[[07:21:49]] [INFO] UUhQjmzfO2=running
[[07:21:49]] [INFO] Executing action 251/641: Tap on Text: "OnePass"
[[07:21:49]] [SUCCESS] Screenshot refreshed successfully
[[07:21:49]] [SUCCESS] Screenshot refreshed successfully
[[07:21:48]] [SUCCESS] Screenshot refreshed
[[07:21:48]] [INFO] Refreshing screenshot...
[[07:21:48]] [INFO] NCyuT8W5Xz=pass
[[07:21:45]] [INFO] NCyuT8W5Xz=running
[[07:21:45]] [INFO] Executing action 250/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:21:44]] [SUCCESS] Screenshot refreshed successfully
[[07:21:44]] [SUCCESS] Screenshot refreshed successfully
[[07:21:44]] [SUCCESS] Screenshot refreshed
[[07:21:44]] [INFO] Refreshing screenshot...
[[07:21:44]] [INFO] 2kwu2VBmuZ=pass
[[07:21:42]] [INFO] 2kwu2VBmuZ=running
[[07:21:42]] [INFO] Executing action 249/641: iOS Function: alert_accept
[[07:21:41]] [SUCCESS] Screenshot refreshed successfully
[[07:21:41]] [SUCCESS] Screenshot refreshed successfully
[[07:21:41]] [SUCCESS] Screenshot refreshed
[[07:21:41]] [INFO] Refreshing screenshot...
[[07:21:41]] [INFO] cJDpd7aK3d=pass
[[07:21:35]] [INFO] cJDpd7aK3d=running
[[07:21:35]] [INFO] Executing action 248/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:21:35]] [SUCCESS] Screenshot refreshed successfully
[[07:21:35]] [SUCCESS] Screenshot refreshed successfully
[[07:21:35]] [SUCCESS] Screenshot refreshed
[[07:21:35]] [INFO] Refreshing screenshot...
[[07:21:35]] [INFO] FlEukNkjlS=pass
[[07:21:31]] [INFO] FlEukNkjlS=running
[[07:21:31]] [INFO] Executing action 247/641: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[07:21:31]] [SUCCESS] Screenshot refreshed successfully
[[07:21:31]] [SUCCESS] Screenshot refreshed successfully
[[07:21:31]] [SUCCESS] Screenshot refreshed
[[07:21:31]] [INFO] Refreshing screenshot...
[[07:21:31]] [INFO] LlRfimKPrn=pass
[[07:21:27]] [INFO] LlRfimKPrn=running
[[07:21:27]] [INFO] Executing action 246/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:21:27]] [SUCCESS] Screenshot refreshed successfully
[[07:21:27]] [SUCCESS] Screenshot refreshed successfully
[[07:21:26]] [SUCCESS] Screenshot refreshed
[[07:21:26]] [INFO] Refreshing screenshot...
[[07:21:26]] [INFO] FciJcOsMsB=pass
[[07:21:20]] [INFO] FciJcOsMsB=running
[[07:21:20]] [INFO] Executing action 245/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:21:19]] [SUCCESS] Screenshot refreshed successfully
[[07:21:19]] [SUCCESS] Screenshot refreshed successfully
[[07:21:19]] [SUCCESS] Screenshot refreshed
[[07:21:19]] [INFO] Refreshing screenshot...
[[07:21:19]] [INFO] 08NzsvhQXK=pass
[[07:21:15]] [INFO] 08NzsvhQXK=running
[[07:21:15]] [INFO] Executing action 244/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:21:15]] [SUCCESS] Screenshot refreshed successfully
[[07:21:15]] [SUCCESS] Screenshot refreshed successfully
[[07:21:15]] [SUCCESS] Screenshot refreshed
[[07:21:15]] [INFO] Refreshing screenshot...
[[07:21:15]] [INFO] IsGWxLFpIn=pass
[[07:21:10]] [INFO] IsGWxLFpIn=running
[[07:21:10]] [INFO] Executing action 243/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:21:10]] [SUCCESS] Screenshot refreshed successfully
[[07:21:10]] [SUCCESS] Screenshot refreshed successfully
[[07:21:10]] [SUCCESS] Screenshot refreshed
[[07:21:10]] [INFO] Refreshing screenshot...
[[07:21:10]] [INFO] dyECdbRifp=pass
[[07:21:06]] [INFO] dyECdbRifp=running
[[07:21:06]] [INFO] Executing action 242/641: iOS Function: text - Text: "Wonderbaby@5"
[[07:21:05]] [SUCCESS] Screenshot refreshed successfully
[[07:21:05]] [SUCCESS] Screenshot refreshed successfully
[[07:21:05]] [SUCCESS] Screenshot refreshed
[[07:21:05]] [INFO] Refreshing screenshot...
[[07:21:05]] [INFO] I5bRbYY1hD=pass
[[07:21:01]] [INFO] I5bRbYY1hD=running
[[07:21:01]] [INFO] Executing action 241/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:21:01]] [SUCCESS] Screenshot refreshed successfully
[[07:21:01]] [SUCCESS] Screenshot refreshed successfully
[[07:21:00]] [SUCCESS] Screenshot refreshed
[[07:21:00]] [INFO] Refreshing screenshot...
[[07:21:00]] [INFO] WMl5g82CCq=pass
[[07:20:56]] [INFO] WMl5g82CCq=running
[[07:20:56]] [INFO] Executing action 240/641: iOS Function: text - Text: "<EMAIL>"
[[07:20:56]] [SUCCESS] Screenshot refreshed successfully
[[07:20:56]] [SUCCESS] Screenshot refreshed successfully
[[07:20:55]] [SUCCESS] Screenshot refreshed
[[07:20:55]] [INFO] Refreshing screenshot...
[[07:20:55]] [INFO] 8OsQmoVYqW=pass
[[07:20:51]] [INFO] 8OsQmoVYqW=running
[[07:20:51]] [INFO] Executing action 239/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:20:51]] [SUCCESS] Screenshot refreshed successfully
[[07:20:51]] [SUCCESS] Screenshot refreshed successfully
[[07:20:51]] [SUCCESS] Screenshot refreshed
[[07:20:51]] [INFO] Refreshing screenshot...
[[07:20:51]] [INFO] ImienLpJEN=pass
[[07:20:47]] [INFO] ImienLpJEN=running
[[07:20:47]] [INFO] Executing action 238/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:20:46]] [SUCCESS] Screenshot refreshed successfully
[[07:20:46]] [SUCCESS] Screenshot refreshed successfully
[[07:20:46]] [SUCCESS] Screenshot refreshed
[[07:20:46]] [INFO] Refreshing screenshot...
[[07:20:46]] [INFO] q4hPXCBtx4=pass
[[07:20:44]] [INFO] q4hPXCBtx4=running
[[07:20:44]] [INFO] Executing action 237/641: iOS Function: alert_accept
[[07:20:44]] [SUCCESS] Screenshot refreshed successfully
[[07:20:44]] [SUCCESS] Screenshot refreshed successfully
[[07:20:43]] [SUCCESS] Screenshot refreshed
[[07:20:43]] [INFO] Refreshing screenshot...
[[07:20:43]] [INFO] 2cTZvK1psn=pass
[[07:20:37]] [INFO] 2cTZvK1psn=running
[[07:20:37]] [INFO] Executing action 236/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:20:37]] [SUCCESS] Screenshot refreshed successfully
[[07:20:37]] [SUCCESS] Screenshot refreshed successfully
[[07:20:36]] [SUCCESS] Screenshot refreshed
[[07:20:36]] [INFO] Refreshing screenshot...
[[07:20:36]] [INFO] Vxt7QOYeDD=pass
[[07:20:23]] [SUCCESS] Screenshot refreshed successfully
[[07:20:23]] [SUCCESS] Screenshot refreshed successfully
[[07:20:22]] [INFO] Vxt7QOYeDD=running
[[07:20:22]] [INFO] Executing action 235/641: Restart app: env[appid]
[[07:20:22]] [SUCCESS] Screenshot refreshed
[[07:20:22]] [INFO] Refreshing screenshot...
[[07:20:22]] [SUCCESS] Screenshot refreshed successfully
[[07:20:22]] [SUCCESS] Screenshot refreshed successfully
[[07:20:22]] [SUCCESS] Screenshot refreshed
[[07:20:22]] [INFO] Refreshing screenshot...
[[07:20:20]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:20:19]] [SUCCESS] Screenshot refreshed successfully
[[07:20:19]] [SUCCESS] Screenshot refreshed successfully
[[07:20:19]] [SUCCESS] Screenshot refreshed
[[07:20:19]] [INFO] Refreshing screenshot...
[[07:20:07]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:20:06]] [SUCCESS] Screenshot refreshed successfully
[[07:20:06]] [SUCCESS] Screenshot refreshed successfully
[[07:20:06]] [SUCCESS] Screenshot refreshed
[[07:20:06]] [INFO] Refreshing screenshot...
[[07:20:02]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:20:02]] [SUCCESS] Screenshot refreshed successfully
[[07:20:02]] [SUCCESS] Screenshot refreshed successfully
[[07:20:02]] [SUCCESS] Screenshot refreshed
[[07:20:02]] [INFO] Refreshing screenshot...
[[07:19:58]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:19:58]] [SUCCESS] Screenshot refreshed successfully
[[07:19:58]] [SUCCESS] Screenshot refreshed successfully
[[07:19:58]] [SUCCESS] Screenshot refreshed
[[07:19:58]] [INFO] Refreshing screenshot...
[[07:19:51]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:19:51]] [SUCCESS] Screenshot refreshed successfully
[[07:19:51]] [SUCCESS] Screenshot refreshed successfully
[[07:19:51]] [SUCCESS] Screenshot refreshed
[[07:19:51]] [INFO] Refreshing screenshot...
[[07:19:44]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:19:44]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:19:44]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:19:44]] [INFO] DYWpUY7xB6=running
[[07:19:44]] [INFO] Executing action 234/641: cleanupSteps action
[[07:19:44]] [SUCCESS] Screenshot refreshed successfully
[[07:19:44]] [SUCCESS] Screenshot refreshed successfully
[[07:19:44]] [SUCCESS] Screenshot refreshed
[[07:19:44]] [INFO] Refreshing screenshot...
[[07:19:44]] [INFO] OyUowAaBzD=pass
[[07:19:40]] [INFO] OyUowAaBzD=running
[[07:19:40]] [INFO] Executing action 233/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:19:39]] [SUCCESS] Screenshot refreshed successfully
[[07:19:39]] [SUCCESS] Screenshot refreshed successfully
[[07:19:39]] [SUCCESS] Screenshot refreshed
[[07:19:39]] [INFO] Refreshing screenshot...
[[07:19:39]] [INFO] Ob26qqcA0p=pass
[[07:19:33]] [INFO] Ob26qqcA0p=running
[[07:19:33]] [INFO] Executing action 232/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:19:32]] [SUCCESS] Screenshot refreshed successfully
[[07:19:32]] [SUCCESS] Screenshot refreshed successfully
[[07:19:32]] [SUCCESS] Screenshot refreshed
[[07:19:32]] [INFO] Refreshing screenshot...
[[07:19:32]] [INFO] k3mu9Mt7Ec=pass
[[07:19:28]] [INFO] k3mu9Mt7Ec=running
[[07:19:28]] [INFO] Executing action 231/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:19:28]] [SUCCESS] Screenshot refreshed successfully
[[07:19:28]] [SUCCESS] Screenshot refreshed successfully
[[07:19:28]] [SUCCESS] Screenshot refreshed
[[07:19:28]] [INFO] Refreshing screenshot...
[[07:19:28]] [INFO] yhmzeynQyu=pass
[[07:19:24]] [INFO] yhmzeynQyu=running
[[07:19:24]] [INFO] Executing action 230/641: Tap on Text: "Remove"
[[07:19:24]] [SUCCESS] Screenshot refreshed successfully
[[07:19:24]] [SUCCESS] Screenshot refreshed successfully
[[07:19:24]] [SUCCESS] Screenshot refreshed
[[07:19:24]] [INFO] Refreshing screenshot...
[[07:19:24]] [INFO] zCHZwhvc44=pass
[[07:19:18]] [INFO] zCHZwhvc44=running
[[07:19:18]] [INFO] Executing action 229/641: ifThenSteps action
[[07:19:18]] [SUCCESS] Screenshot refreshed successfully
[[07:19:18]] [SUCCESS] Screenshot refreshed successfully
[[07:19:17]] [SUCCESS] Screenshot refreshed
[[07:19:17]] [INFO] Refreshing screenshot...
[[07:19:17]] [INFO] yhmzeynQyu=pass
[[07:19:14]] [INFO] yhmzeynQyu=running
[[07:19:14]] [INFO] Executing action 228/641: Tap on Text: "Remove"
[[07:19:13]] [SUCCESS] Screenshot refreshed successfully
[[07:19:13]] [SUCCESS] Screenshot refreshed successfully
[[07:19:13]] [SUCCESS] Screenshot refreshed
[[07:19:13]] [INFO] Refreshing screenshot...
[[07:19:13]] [INFO] zCHZwhvc44=pass
[[07:19:07]] [INFO] zCHZwhvc44=running
[[07:19:07]] [INFO] Executing action 227/641: ifThenSteps action
[[07:19:07]] [SUCCESS] Screenshot refreshed successfully
[[07:19:07]] [SUCCESS] Screenshot refreshed successfully
[[07:19:07]] [SUCCESS] Screenshot refreshed
[[07:19:07]] [INFO] Refreshing screenshot...
[[07:19:07]] [INFO] F1olhgKhUt=pass
[[07:19:02]] [INFO] F1olhgKhUt=running
[[07:19:02]] [INFO] Executing action 226/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:19:02]] [SUCCESS] Screenshot refreshed successfully
[[07:19:02]] [SUCCESS] Screenshot refreshed successfully
[[07:19:02]] [SUCCESS] Screenshot refreshed
[[07:19:02]] [INFO] Refreshing screenshot...
[[07:19:02]] [INFO] 8umPSX0vrr=pass
[[07:18:58]] [INFO] 8umPSX0vrr=running
[[07:18:58]] [INFO] Executing action 225/641: Tap on image: banner-close-updated.png
[[07:18:58]] [SUCCESS] Screenshot refreshed successfully
[[07:18:58]] [SUCCESS] Screenshot refreshed successfully
[[07:18:58]] [SUCCESS] Screenshot refreshed
[[07:18:58]] [INFO] Refreshing screenshot...
[[07:18:58]] [INFO] pr9o8Zsm5p=pass
[[07:18:54]] [INFO] pr9o8Zsm5p=running
[[07:18:54]] [INFO] Executing action 224/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[07:18:53]] [SUCCESS] Screenshot refreshed successfully
[[07:18:53]] [SUCCESS] Screenshot refreshed successfully
[[07:18:53]] [SUCCESS] Screenshot refreshed
[[07:18:53]] [INFO] Refreshing screenshot...
[[07:18:53]] [INFO] Qbg9bipTGs=pass
[[07:18:50]] [INFO] Qbg9bipTGs=running
[[07:18:50]] [INFO] Executing action 223/641: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[07:18:49]] [SUCCESS] Screenshot refreshed successfully
[[07:18:49]] [SUCCESS] Screenshot refreshed successfully
[[07:18:49]] [SUCCESS] Screenshot refreshed
[[07:18:49]] [INFO] Refreshing screenshot...
[[07:18:49]] [INFO] Ob26qqcA0p=pass
[[07:18:45]] [INFO] Ob26qqcA0p=running
[[07:18:45]] [INFO] Executing action 222/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:18:44]] [SUCCESS] Screenshot refreshed successfully
[[07:18:44]] [SUCCESS] Screenshot refreshed successfully
[[07:18:44]] [SUCCESS] Screenshot refreshed
[[07:18:44]] [INFO] Refreshing screenshot...
[[07:18:44]] [INFO] ByviEQxEgr=pass
[[07:18:32]] [INFO] ByviEQxEgr=running
[[07:18:32]] [INFO] Executing action 221/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:18:31]] [SUCCESS] Screenshot refreshed successfully
[[07:18:31]] [SUCCESS] Screenshot refreshed successfully
[[07:18:31]] [SUCCESS] Screenshot refreshed
[[07:18:31]] [INFO] Refreshing screenshot...
[[07:18:31]] [INFO] lWIRxRm6HE=pass
[[07:18:27]] [INFO] lWIRxRm6HE=running
[[07:18:27]] [INFO] Executing action 220/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:18:26]] [SUCCESS] Screenshot refreshed successfully
[[07:18:26]] [SUCCESS] Screenshot refreshed successfully
[[07:18:26]] [SUCCESS] Screenshot refreshed
[[07:18:26]] [INFO] Refreshing screenshot...
[[07:18:26]] [INFO] uOt2cFGhGr=pass
[[07:18:22]] [INFO] uOt2cFGhGr=running
[[07:18:22]] [INFO] Executing action 219/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:18:22]] [SUCCESS] Screenshot refreshed successfully
[[07:18:22]] [SUCCESS] Screenshot refreshed successfully
[[07:18:22]] [SUCCESS] Screenshot refreshed
[[07:18:22]] [INFO] Refreshing screenshot...
[[07:18:22]] [INFO] Q0fomJIDoQ=pass
[[07:18:18]] [INFO] Q0fomJIDoQ=running
[[07:18:18]] [INFO] Executing action 218/641: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[07:18:18]] [SUCCESS] Screenshot refreshed successfully
[[07:18:18]] [SUCCESS] Screenshot refreshed successfully
[[07:18:17]] [SUCCESS] Screenshot refreshed
[[07:18:17]] [INFO] Refreshing screenshot...
[[07:18:17]] [INFO] yhmzeynQyu=pass
[[07:18:14]] [INFO] yhmzeynQyu=running
[[07:18:14]] [INFO] Executing action 217/641: Tap on Text: "Remove"
[[07:18:13]] [SUCCESS] Screenshot refreshed successfully
[[07:18:13]] [SUCCESS] Screenshot refreshed successfully
[[07:18:13]] [SUCCESS] Screenshot refreshed
[[07:18:13]] [INFO] Refreshing screenshot...
[[07:18:13]] [INFO] Q0fomJIDoQ=pass
[[07:18:09]] [INFO] Q0fomJIDoQ=running
[[07:18:09]] [INFO] Executing action 216/641: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[07:18:09]] [SUCCESS] Screenshot refreshed successfully
[[07:18:09]] [SUCCESS] Screenshot refreshed successfully
[[07:18:09]] [SUCCESS] Screenshot refreshed
[[07:18:09]] [INFO] Refreshing screenshot...
[[07:18:09]] [INFO] y4i304JeJj=pass
[[07:18:05]] [INFO] y4i304JeJj=running
[[07:18:05]] [INFO] Executing action 215/641: Tap on Text: "Move"
[[07:18:05]] [SUCCESS] Screenshot refreshed successfully
[[07:18:05]] [SUCCESS] Screenshot refreshed successfully
[[07:18:05]] [SUCCESS] Screenshot refreshed
[[07:18:05]] [INFO] Refreshing screenshot...
[[07:18:05]] [INFO] Q0fomJIDoQ=pass
[[07:18:01]] [INFO] Q0fomJIDoQ=running
[[07:18:01]] [INFO] Executing action 214/641: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[07:18:01]] [SUCCESS] Screenshot refreshed successfully
[[07:18:01]] [SUCCESS] Screenshot refreshed successfully
[[07:18:00]] [SUCCESS] Screenshot refreshed
[[07:18:00]] [INFO] Refreshing screenshot...
[[07:18:00]] [INFO] Q0fomJIDoQ=pass
[[07:17:57]] [INFO] Q0fomJIDoQ=running
[[07:17:57]] [INFO] Executing action 213/641: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[07:17:57]] [SUCCESS] Screenshot refreshed successfully
[[07:17:57]] [SUCCESS] Screenshot refreshed successfully
[[07:17:57]] [SUCCESS] Screenshot refreshed
[[07:17:57]] [INFO] Refreshing screenshot...
[[07:17:57]] [INFO] F1olhgKhUt=pass
[[07:17:52]] [INFO] F1olhgKhUt=running
[[07:17:52]] [INFO] Executing action 212/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:17:52]] [SUCCESS] Screenshot refreshed successfully
[[07:17:52]] [SUCCESS] Screenshot refreshed successfully
[[07:17:52]] [SUCCESS] Screenshot refreshed
[[07:17:52]] [INFO] Refreshing screenshot...
[[07:17:52]] [INFO] WbxRVpWtjw=pass
[[07:17:47]] [INFO] WbxRVpWtjw=running
[[07:17:47]] [INFO] Executing action 211/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[07:17:47]] [SUCCESS] Screenshot refreshed successfully
[[07:17:47]] [SUCCESS] Screenshot refreshed successfully
[[07:17:47]] [SUCCESS] Screenshot refreshed
[[07:17:47]] [INFO] Refreshing screenshot...
[[07:17:47]] [INFO] H3IAmq3r3i=pass
[[07:17:39]] [INFO] H3IAmq3r3i=running
[[07:17:39]] [INFO] Executing action 210/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[07:17:39]] [SUCCESS] Screenshot refreshed successfully
[[07:17:39]] [SUCCESS] Screenshot refreshed successfully
[[07:17:39]] [SUCCESS] Screenshot refreshed
[[07:17:39]] [INFO] Refreshing screenshot...
[[07:17:39]] [INFO] uOt2cFGhGr=pass
[[07:17:35]] [INFO] uOt2cFGhGr=running
[[07:17:35]] [INFO] Executing action 209/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:17:34]] [SUCCESS] Screenshot refreshed successfully
[[07:17:34]] [SUCCESS] Screenshot refreshed successfully
[[07:17:34]] [SUCCESS] Screenshot refreshed
[[07:17:34]] [INFO] Refreshing screenshot...
[[07:17:34]] [INFO] eLxHVWKeDQ=pass
[[07:17:31]] [INFO] eLxHVWKeDQ=running
[[07:17:31]] [INFO] Executing action 208/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:17:30]] [SUCCESS] Screenshot refreshed successfully
[[07:17:30]] [SUCCESS] Screenshot refreshed successfully
[[07:17:30]] [SUCCESS] Screenshot refreshed
[[07:17:30]] [INFO] Refreshing screenshot...
[[07:17:30]] [INFO] ghzdMuwrHj=pass
[[07:17:26]] [INFO] ghzdMuwrHj=running
[[07:17:26]] [INFO] Executing action 207/641: iOS Function: text - Text: "P_43386093"
[[07:17:26]] [SUCCESS] Screenshot refreshed successfully
[[07:17:26]] [SUCCESS] Screenshot refreshed successfully
[[07:17:26]] [SUCCESS] Screenshot refreshed
[[07:17:26]] [INFO] Refreshing screenshot...
[[07:17:26]] [INFO] fMzoZJg9I7=pass
[[07:17:21]] [INFO] fMzoZJg9I7=running
[[07:17:21]] [INFO] Executing action 206/641: Tap on Text: "Find"
[[07:17:20]] [SUCCESS] Screenshot refreshed successfully
[[07:17:20]] [SUCCESS] Screenshot refreshed successfully
[[07:17:20]] [SUCCESS] Screenshot refreshed
[[07:17:20]] [INFO] Refreshing screenshot...
[[07:17:20]] [INFO] j1JjmfPRaE=pass
[[07:17:15]] [INFO] j1JjmfPRaE=running
[[07:17:15]] [INFO] Executing action 205/641: Restart app: env[appid]
[[07:17:15]] [SUCCESS] Screenshot refreshed successfully
[[07:17:15]] [SUCCESS] Screenshot refreshed successfully
[[07:17:15]] [SUCCESS] Screenshot refreshed
[[07:17:15]] [INFO] Refreshing screenshot...
[[07:17:15]] [INFO] WbxRVpWtjw=pass
[[07:17:10]] [INFO] WbxRVpWtjw=running
[[07:17:10]] [INFO] Executing action 204/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[07:17:10]] [SUCCESS] Screenshot refreshed successfully
[[07:17:10]] [SUCCESS] Screenshot refreshed successfully
[[07:17:10]] [SUCCESS] Screenshot refreshed
[[07:17:10]] [INFO] Refreshing screenshot...
[[07:17:10]] [INFO] H3IAmq3r3i=pass
[[07:17:02]] [INFO] H3IAmq3r3i=running
[[07:17:02]] [INFO] Executing action 203/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[07:17:02]] [SUCCESS] Screenshot refreshed successfully
[[07:17:02]] [SUCCESS] Screenshot refreshed successfully
[[07:17:02]] [SUCCESS] Screenshot refreshed
[[07:17:02]] [INFO] Refreshing screenshot...
[[07:17:02]] [INFO] ITHvSyXXmu=pass
[[07:16:58]] [INFO] ITHvSyXXmu=running
[[07:16:58]] [INFO] Executing action 202/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:16:58]] [SUCCESS] Screenshot refreshed successfully
[[07:16:58]] [SUCCESS] Screenshot refreshed successfully
[[07:16:57]] [SUCCESS] Screenshot refreshed
[[07:16:57]] [INFO] Refreshing screenshot...
[[07:16:57]] [INFO] eLxHVWKeDQ=pass
[[07:16:41]] [INFO] eLxHVWKeDQ=running
[[07:16:41]] [INFO] Executing action 201/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[07:16:40]] [SUCCESS] Screenshot refreshed successfully
[[07:16:40]] [SUCCESS] Screenshot refreshed successfully
[[07:16:40]] [SUCCESS] Screenshot refreshed
[[07:16:40]] [INFO] Refreshing screenshot...
[[07:16:40]] [INFO] WbxRVpWtjw=pass
[[07:16:36]] [INFO] WbxRVpWtjw=running
[[07:16:36]] [INFO] Executing action 200/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[07:16:35]] [SUCCESS] Screenshot refreshed successfully
[[07:16:35]] [SUCCESS] Screenshot refreshed successfully
[[07:16:35]] [SUCCESS] Screenshot refreshed
[[07:16:35]] [INFO] Refreshing screenshot...
[[07:16:35]] [INFO] H3IAmq3r3i=pass
[[07:16:28]] [INFO] H3IAmq3r3i=running
[[07:16:28]] [INFO] Executing action 199/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[07:16:27]] [SUCCESS] Screenshot refreshed successfully
[[07:16:27]] [SUCCESS] Screenshot refreshed successfully
[[07:16:27]] [SUCCESS] Screenshot refreshed
[[07:16:27]] [INFO] Refreshing screenshot...
[[07:16:27]] [INFO] ITHvSyXXmu=pass
[[07:16:23]] [INFO] ITHvSyXXmu=running
[[07:16:23]] [INFO] Executing action 198/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:16:23]] [SUCCESS] Screenshot refreshed successfully
[[07:16:23]] [SUCCESS] Screenshot refreshed successfully
[[07:16:23]] [SUCCESS] Screenshot refreshed
[[07:16:23]] [INFO] Refreshing screenshot...
[[07:16:23]] [INFO] eLxHVWKeDQ=pass
[[07:16:19]] [INFO] eLxHVWKeDQ=running
[[07:16:19]] [INFO] Executing action 197/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:16:18]] [SUCCESS] Screenshot refreshed successfully
[[07:16:18]] [SUCCESS] Screenshot refreshed successfully
[[07:16:18]] [SUCCESS] Screenshot refreshed
[[07:16:18]] [INFO] Refreshing screenshot...
[[07:16:18]] [INFO] nAB6Q8LAdv=pass
[[07:16:14]] [INFO] nAB6Q8LAdv=running
[[07:16:14]] [INFO] Executing action 196/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:16:14]] [SUCCESS] Screenshot refreshed successfully
[[07:16:14]] [SUCCESS] Screenshot refreshed successfully
[[07:16:14]] [SUCCESS] Screenshot refreshed
[[07:16:14]] [INFO] Refreshing screenshot...
[[07:16:14]] [INFO] sc2KH9bG6H=pass
[[07:16:10]] [INFO] sc2KH9bG6H=running
[[07:16:10]] [INFO] Executing action 195/641: iOS Function: text - Text: "Uno card"
[[07:16:09]] [SUCCESS] Screenshot refreshed successfully
[[07:16:09]] [SUCCESS] Screenshot refreshed successfully
[[07:16:09]] [SUCCESS] Screenshot refreshed
[[07:16:09]] [INFO] Refreshing screenshot...
[[07:16:09]] [INFO] rqLJpAP0mA=pass
[[07:16:04]] [INFO] rqLJpAP0mA=running
[[07:16:04]] [INFO] Executing action 194/641: Tap on Text: "Find"
[[07:16:04]] [SUCCESS] Screenshot refreshed successfully
[[07:16:04]] [SUCCESS] Screenshot refreshed successfully
[[07:16:04]] [SUCCESS] Screenshot refreshed
[[07:16:04]] [INFO] Refreshing screenshot...
[[07:16:04]] [INFO] yiKyF5FJwN=pass
[[07:16:01]] [INFO] yiKyF5FJwN=running
[[07:16:01]] [INFO] Executing action 193/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:16:01]] [SUCCESS] Screenshot refreshed successfully
[[07:16:01]] [SUCCESS] Screenshot refreshed successfully
[[07:16:00]] [SUCCESS] Screenshot refreshed
[[07:16:00]] [INFO] Refreshing screenshot...
[[07:16:00]] [INFO] sTtseHOKfa=pass
[[07:15:56]] [INFO] sTtseHOKfa=running
[[07:15:56]] [INFO] Executing action 192/641: iOS Function: text - Text: "Wonderbaby@5"
[[07:15:56]] [SUCCESS] Screenshot refreshed successfully
[[07:15:56]] [SUCCESS] Screenshot refreshed successfully
[[07:15:56]] [SUCCESS] Screenshot refreshed
[[07:15:56]] [INFO] Refreshing screenshot...
[[07:15:56]] [INFO] T3MmUw30SF=pass
[[07:15:52]] [INFO] T3MmUw30SF=running
[[07:15:52]] [INFO] Executing action 191/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:15:51]] [SUCCESS] Screenshot refreshed successfully
[[07:15:51]] [SUCCESS] Screenshot refreshed successfully
[[07:15:51]] [SUCCESS] Screenshot refreshed
[[07:15:51]] [INFO] Refreshing screenshot...
[[07:15:51]] [INFO] PPIBJbaXNx=pass
[[07:15:46]] [INFO] PPIBJbaXNx=running
[[07:15:46]] [INFO] Executing action 190/641: iOS Function: text - Text: "<EMAIL>"
[[07:15:46]] [SUCCESS] Screenshot refreshed successfully
[[07:15:46]] [SUCCESS] Screenshot refreshed successfully
[[07:15:46]] [SUCCESS] Screenshot refreshed
[[07:15:46]] [INFO] Refreshing screenshot...
[[07:15:46]] [INFO] LDkFLWks00=pass
[[07:15:42]] [INFO] LDkFLWks00=running
[[07:15:42]] [INFO] Executing action 189/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:15:42]] [SUCCESS] Screenshot refreshed successfully
[[07:15:42]] [SUCCESS] Screenshot refreshed successfully
[[07:15:41]] [SUCCESS] Screenshot refreshed
[[07:15:41]] [INFO] Refreshing screenshot...
[[07:15:41]] [INFO] 3caMBvQX7k=pass
[[07:15:38]] [INFO] 3caMBvQX7k=running
[[07:15:38]] [INFO] Executing action 188/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:15:38]] [SUCCESS] Screenshot refreshed successfully
[[07:15:38]] [SUCCESS] Screenshot refreshed successfully
[[07:15:37]] [SUCCESS] Screenshot refreshed
[[07:15:37]] [INFO] Refreshing screenshot...
[[07:15:37]] [INFO] yUJyVO5Wev=pass
[[07:15:35]] [INFO] yUJyVO5Wev=running
[[07:15:35]] [INFO] Executing action 187/641: iOS Function: alert_accept
[[07:15:35]] [SUCCESS] Screenshot refreshed successfully
[[07:15:35]] [SUCCESS] Screenshot refreshed successfully
[[07:15:34]] [SUCCESS] Screenshot refreshed
[[07:15:34]] [INFO] Refreshing screenshot...
[[07:15:34]] [INFO] rkL0oz4kiL=pass
[[07:15:28]] [INFO] rkL0oz4kiL=running
[[07:15:28]] [INFO] Executing action 186/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:15:28]] [SUCCESS] Screenshot refreshed successfully
[[07:15:28]] [SUCCESS] Screenshot refreshed successfully
[[07:15:27]] [SUCCESS] Screenshot refreshed
[[07:15:27]] [INFO] Refreshing screenshot...
[[07:15:27]] [INFO] HotUJOd6oB=pass
[[07:15:14]] [SUCCESS] Screenshot refreshed successfully
[[07:15:14]] [SUCCESS] Screenshot refreshed successfully
[[07:15:14]] [INFO] HotUJOd6oB=running
[[07:15:14]] [INFO] Executing action 185/641: Restart app: env[appid]
[[07:15:14]] [SUCCESS] Screenshot refreshed
[[07:15:14]] [INFO] Refreshing screenshot...
[[07:15:13]] [SUCCESS] Screenshot refreshed successfully
[[07:15:13]] [SUCCESS] Screenshot refreshed successfully
[[07:15:13]] [SUCCESS] Screenshot refreshed
[[07:15:13]] [INFO] Refreshing screenshot...
[[07:15:11]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:15:10]] [SUCCESS] Screenshot refreshed successfully
[[07:15:10]] [SUCCESS] Screenshot refreshed successfully
[[07:15:10]] [SUCCESS] Screenshot refreshed
[[07:15:10]] [INFO] Refreshing screenshot...
[[07:14:58]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:14:57]] [SUCCESS] Screenshot refreshed successfully
[[07:14:57]] [SUCCESS] Screenshot refreshed successfully
[[07:14:57]] [SUCCESS] Screenshot refreshed
[[07:14:57]] [INFO] Refreshing screenshot...
[[07:14:54]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:14:53]] [SUCCESS] Screenshot refreshed successfully
[[07:14:53]] [SUCCESS] Screenshot refreshed successfully
[[07:14:53]] [SUCCESS] Screenshot refreshed
[[07:14:53]] [INFO] Refreshing screenshot...
[[07:14:49]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:14:49]] [SUCCESS] Screenshot refreshed successfully
[[07:14:49]] [SUCCESS] Screenshot refreshed successfully
[[07:14:49]] [SUCCESS] Screenshot refreshed
[[07:14:49]] [INFO] Refreshing screenshot...
[[07:14:42]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:14:42]] [SUCCESS] Screenshot refreshed successfully
[[07:14:42]] [SUCCESS] Screenshot refreshed successfully
[[07:14:42]] [SUCCESS] Screenshot refreshed
[[07:14:42]] [INFO] Refreshing screenshot...
[[07:14:35]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:14:35]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:14:35]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:14:35]] [INFO] IR7wnjW7C8=running
[[07:14:35]] [INFO] Executing action 184/641: cleanupSteps action
[[07:14:35]] [SUCCESS] Screenshot refreshed successfully
[[07:14:35]] [SUCCESS] Screenshot refreshed successfully
[[07:14:35]] [SUCCESS] Screenshot refreshed
[[07:14:35]] [INFO] Refreshing screenshot...
[[07:14:35]] [INFO] 7WYExJTqjp=pass
[[07:14:31]] [INFO] 7WYExJTqjp=running
[[07:14:31]] [INFO] Executing action 183/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:14:31]] [SUCCESS] Screenshot refreshed successfully
[[07:14:31]] [SUCCESS] Screenshot refreshed successfully
[[07:14:30]] [SUCCESS] Screenshot refreshed
[[07:14:30]] [INFO] Refreshing screenshot...
[[07:14:30]] [INFO] 4WfPFN961S=pass
[[07:14:24]] [INFO] 4WfPFN961S=running
[[07:14:24]] [INFO] Executing action 182/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:14:23]] [SUCCESS] Screenshot refreshed successfully
[[07:14:23]] [SUCCESS] Screenshot refreshed successfully
[[07:14:23]] [SUCCESS] Screenshot refreshed
[[07:14:23]] [INFO] Refreshing screenshot...
[[07:14:23]] [INFO] NurQsFoMkE=pass
[[07:14:19]] [INFO] NurQsFoMkE=running
[[07:14:19]] [INFO] Executing action 181/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:14:19]] [SUCCESS] Screenshot refreshed successfully
[[07:14:19]] [SUCCESS] Screenshot refreshed successfully
[[07:14:19]] [SUCCESS] Screenshot refreshed
[[07:14:19]] [INFO] Refreshing screenshot...
[[07:14:19]] [INFO] CkfAScJNq8=pass
[[07:14:15]] [INFO] CkfAScJNq8=running
[[07:14:15]] [INFO] Executing action 180/641: Tap on image: env[closebtnimage]
[[07:14:15]] [SUCCESS] Screenshot refreshed successfully
[[07:14:15]] [SUCCESS] Screenshot refreshed successfully
[[07:14:15]] [SUCCESS] Screenshot refreshed
[[07:14:15]] [INFO] Refreshing screenshot...
[[07:14:15]] [INFO] 1NWfFsDiTQ=pass
[[07:14:11]] [INFO] 1NWfFsDiTQ=running
[[07:14:11]] [INFO] Executing action 179/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:14:10]] [SUCCESS] Screenshot refreshed successfully
[[07:14:10]] [SUCCESS] Screenshot refreshed successfully
[[07:14:10]] [SUCCESS] Screenshot refreshed
[[07:14:10]] [INFO] Refreshing screenshot...
[[07:14:10]] [INFO] tufIibCj03=pass
[[07:14:06]] [INFO] tufIibCj03=running
[[07:14:06]] [INFO] Executing action 178/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[07:14:06]] [SUCCESS] Screenshot refreshed successfully
[[07:14:06]] [SUCCESS] Screenshot refreshed successfully
[[07:14:06]] [SUCCESS] Screenshot refreshed
[[07:14:06]] [INFO] Refreshing screenshot...
[[07:14:06]] [INFO] uNbKV4slh0=pass
[[07:13:54]] [SUCCESS] Screenshot refreshed successfully
[[07:13:54]] [SUCCESS] Screenshot refreshed successfully
[[07:13:54]] [INFO] uNbKV4slh0=running
[[07:13:54]] [INFO] Executing action 177/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:13:54]] [SUCCESS] Screenshot refreshed
[[07:13:54]] [INFO] Refreshing screenshot...
[[07:13:53]] [SUCCESS] Screenshot refreshed successfully
[[07:13:53]] [SUCCESS] Screenshot refreshed successfully
[[07:13:53]] [SUCCESS] Screenshot refreshed
[[07:13:53]] [INFO] Refreshing screenshot...
[[07:13:49]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[07:13:49]] [SUCCESS] Screenshot refreshed successfully
[[07:13:49]] [SUCCESS] Screenshot refreshed successfully
[[07:13:49]] [SUCCESS] Screenshot refreshed
[[07:13:49]] [INFO] Refreshing screenshot...
[[07:13:45]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:13:44]] [SUCCESS] Screenshot refreshed successfully
[[07:13:44]] [SUCCESS] Screenshot refreshed successfully
[[07:13:44]] [SUCCESS] Screenshot refreshed
[[07:13:44]] [INFO] Refreshing screenshot...
[[07:13:40]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[07:13:39]] [SUCCESS] Screenshot refreshed successfully
[[07:13:39]] [SUCCESS] Screenshot refreshed successfully
[[07:13:39]] [SUCCESS] Screenshot refreshed
[[07:13:39]] [INFO] Refreshing screenshot...
[[07:13:35]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:13:35]] [SUCCESS] Screenshot refreshed successfully
[[07:13:35]] [SUCCESS] Screenshot refreshed successfully
[[07:13:35]] [SUCCESS] Screenshot refreshed
[[07:13:35]] [INFO] Refreshing screenshot...
[[07:13:29]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:13:29]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[07:13:29]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[07:13:29]] [INFO] L5CIZqzpQK=running
[[07:13:29]] [INFO] Executing action 176/641: Execute Test Case: Kmart-Signin (5 steps)
[[07:13:29]] [SUCCESS] Screenshot refreshed successfully
[[07:13:29]] [SUCCESS] Screenshot refreshed successfully
[[07:13:28]] [SUCCESS] Screenshot refreshed
[[07:13:28]] [INFO] Refreshing screenshot...
[[07:13:28]] [INFO] q9ZiyYoE5B=pass
[[07:13:26]] [INFO] q9ZiyYoE5B=running
[[07:13:26]] [INFO] Executing action 175/641: iOS Function: alert_accept
[[07:13:26]] [SUCCESS] Screenshot refreshed successfully
[[07:13:26]] [SUCCESS] Screenshot refreshed successfully
[[07:13:26]] [SUCCESS] Screenshot refreshed
[[07:13:26]] [INFO] Refreshing screenshot...
[[07:13:26]] [INFO] STEdg5jOU8=pass
[[07:13:21]] [INFO] STEdg5jOU8=running
[[07:13:21]] [INFO] Executing action 174/641: Tap on Text: "in"
[[07:13:21]] [SUCCESS] Screenshot refreshed successfully
[[07:13:21]] [SUCCESS] Screenshot refreshed successfully
[[07:13:21]] [SUCCESS] Screenshot refreshed
[[07:13:21]] [INFO] Refreshing screenshot...
[[07:13:21]] [INFO] LDH2hlTZT9=pass
[[07:13:15]] [INFO] LDH2hlTZT9=running
[[07:13:15]] [INFO] Executing action 173/641: Wait for 5 ms
[[07:13:14]] [SUCCESS] Screenshot refreshed successfully
[[07:13:14]] [SUCCESS] Screenshot refreshed successfully
[[07:13:14]] [SUCCESS] Screenshot refreshed
[[07:13:14]] [INFO] Refreshing screenshot...
[[07:13:14]] [INFO] 5Dk9h5bQWl=pass
[[07:13:08]] [INFO] 5Dk9h5bQWl=running
[[07:13:08]] [INFO] Executing action 172/641: Tap on element with accessibility_id: Continue to details
[[07:13:08]] [SUCCESS] Screenshot refreshed successfully
[[07:13:08]] [SUCCESS] Screenshot refreshed successfully
[[07:13:08]] [SUCCESS] Screenshot refreshed
[[07:13:08]] [INFO] Refreshing screenshot...
[[07:13:08]] [INFO] VMzFZ2uTwl=pass
[[07:13:00]] [SUCCESS] Screenshot refreshed successfully
[[07:13:00]] [SUCCESS] Screenshot refreshed successfully
[[07:13:00]] [INFO] VMzFZ2uTwl=running
[[07:13:00]] [INFO] Executing action 171/641: Swipe up till element accessibilityid: "Continue to details" is visible
[[07:13:00]] [SUCCESS] Screenshot refreshed
[[07:13:00]] [INFO] Refreshing screenshot...
[[07:12:59]] [SUCCESS] Screenshot refreshed successfully
[[07:12:59]] [SUCCESS] Screenshot refreshed successfully
[[07:12:59]] [SUCCESS] Screenshot refreshed
[[07:12:59]] [INFO] Refreshing screenshot...
[[07:12:56]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:12:56]] [SUCCESS] Screenshot refreshed successfully
[[07:12:56]] [SUCCESS] Screenshot refreshed successfully
[[07:12:55]] [SUCCESS] Screenshot refreshed
[[07:12:55]] [INFO] Refreshing screenshot...
[[07:12:44]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:12:43]] [SUCCESS] Screenshot refreshed successfully
[[07:12:43]] [SUCCESS] Screenshot refreshed successfully
[[07:12:43]] [SUCCESS] Screenshot refreshed
[[07:12:43]] [INFO] Refreshing screenshot...
[[07:12:37]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[07:12:36]] [SUCCESS] Screenshot refreshed successfully
[[07:12:36]] [SUCCESS] Screenshot refreshed successfully
[[07:12:36]] [SUCCESS] Screenshot refreshed
[[07:12:36]] [INFO] Refreshing screenshot...
[[07:12:32]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:12:32]] [SUCCESS] Screenshot refreshed successfully
[[07:12:32]] [SUCCESS] Screenshot refreshed successfully
[[07:12:32]] [SUCCESS] Screenshot refreshed
[[07:12:32]] [INFO] Refreshing screenshot...
[[07:12:28]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[07:12:28]] [SUCCESS] Screenshot refreshed successfully
[[07:12:28]] [SUCCESS] Screenshot refreshed successfully
[[07:12:27]] [SUCCESS] Screenshot refreshed
[[07:12:27]] [INFO] Refreshing screenshot...
[[07:12:24]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:12:24]] [SUCCESS] Screenshot refreshed successfully
[[07:12:24]] [SUCCESS] Screenshot refreshed successfully
[[07:12:24]] [SUCCESS] Screenshot refreshed
[[07:12:24]] [INFO] Refreshing screenshot...
[[07:12:20]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[07:12:19]] [SUCCESS] Screenshot refreshed successfully
[[07:12:19]] [SUCCESS] Screenshot refreshed successfully
[[07:12:19]] [SUCCESS] Screenshot refreshed
[[07:12:19]] [INFO] Refreshing screenshot...
[[07:12:12]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[07:12:12]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[07:12:12]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[07:12:12]] [INFO] 1XUKKmBanM=running
[[07:12:12]] [INFO] Executing action 170/641: Execute Test Case: Search and Add (Notebooks) (8 steps)
[[07:12:12]] [SUCCESS] Screenshot refreshed successfully
[[07:12:12]] [SUCCESS] Screenshot refreshed successfully
[[07:12:12]] [SUCCESS] Screenshot refreshed
[[07:12:12]] [INFO] Refreshing screenshot...
[[07:12:12]] [INFO] NurQsFoMkE=pass
[[07:12:08]] [INFO] NurQsFoMkE=running
[[07:12:08]] [INFO] Executing action 169/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:12:08]] [SUCCESS] Screenshot refreshed successfully
[[07:12:08]] [SUCCESS] Screenshot refreshed successfully
[[07:12:07]] [SUCCESS] Screenshot refreshed
[[07:12:07]] [INFO] Refreshing screenshot...
[[07:12:07]] [INFO] 7QpmNS6hif=pass
[[07:12:03]] [INFO] 7QpmNS6hif=running
[[07:12:03]] [INFO] Executing action 168/641: Restart app: env[appid]
[[07:12:03]] [SUCCESS] Screenshot refreshed successfully
[[07:12:03]] [SUCCESS] Screenshot refreshed successfully
[[07:12:02]] [SUCCESS] Screenshot refreshed
[[07:12:02]] [INFO] Refreshing screenshot...
[[07:12:02]] [INFO] 7WYExJTqjp=pass
[[07:11:58]] [INFO] 7WYExJTqjp=running
[[07:11:58]] [INFO] Executing action 167/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:11:58]] [SUCCESS] Screenshot refreshed successfully
[[07:11:58]] [SUCCESS] Screenshot refreshed successfully
[[07:11:58]] [SUCCESS] Screenshot refreshed
[[07:11:58]] [INFO] Refreshing screenshot...
[[07:11:58]] [INFO] 4WfPFN961S=pass
[[07:11:51]] [INFO] 4WfPFN961S=running
[[07:11:51]] [INFO] Executing action 166/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:11:51]] [SUCCESS] Screenshot refreshed successfully
[[07:11:51]] [SUCCESS] Screenshot refreshed successfully
[[07:11:51]] [SUCCESS] Screenshot refreshed
[[07:11:51]] [INFO] Refreshing screenshot...
[[07:11:51]] [INFO] NurQsFoMkE=pass
[[07:11:46]] [INFO] NurQsFoMkE=running
[[07:11:46]] [INFO] Executing action 165/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:11:46]] [SUCCESS] Screenshot refreshed successfully
[[07:11:46]] [SUCCESS] Screenshot refreshed successfully
[[07:11:46]] [SUCCESS] Screenshot refreshed
[[07:11:46]] [INFO] Refreshing screenshot...
[[07:11:46]] [INFO] CkfAScJNq8=pass
[[07:11:42]] [INFO] CkfAScJNq8=running
[[07:11:42]] [INFO] Executing action 164/641: Tap on image: env[closebtnimage]
[[07:11:42]] [SUCCESS] Screenshot refreshed successfully
[[07:11:42]] [SUCCESS] Screenshot refreshed successfully
[[07:11:41]] [SUCCESS] Screenshot refreshed
[[07:11:41]] [INFO] Refreshing screenshot...
[[07:11:41]] [INFO] 1NWfFsDiTQ=pass
[[07:11:37]] [INFO] 1NWfFsDiTQ=running
[[07:11:37]] [INFO] Executing action 163/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:11:37]] [SUCCESS] Screenshot refreshed successfully
[[07:11:37]] [SUCCESS] Screenshot refreshed successfully
[[07:11:37]] [SUCCESS] Screenshot refreshed
[[07:11:37]] [INFO] Refreshing screenshot...
[[07:11:37]] [INFO] tufIibCj03=pass
[[07:11:33]] [INFO] tufIibCj03=running
[[07:11:33]] [INFO] Executing action 162/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[07:11:33]] [SUCCESS] Screenshot refreshed successfully
[[07:11:33]] [SUCCESS] Screenshot refreshed successfully
[[07:11:32]] [SUCCESS] Screenshot refreshed
[[07:11:32]] [INFO] Refreshing screenshot...
[[07:11:32]] [INFO] g8u66qfKkX=pass
[[07:11:29]] [INFO] g8u66qfKkX=running
[[07:11:29]] [INFO] Executing action 161/641: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:11:29]] [SUCCESS] Screenshot refreshed successfully
[[07:11:29]] [SUCCESS] Screenshot refreshed successfully
[[07:11:28]] [SUCCESS] Screenshot refreshed
[[07:11:28]] [INFO] Refreshing screenshot...
[[07:11:28]] [INFO] mg4S62Rdtq=pass
[[07:11:16]] [INFO] mg4S62Rdtq=running
[[07:11:16]] [INFO] Executing action 160/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:11:16]] [SUCCESS] Screenshot refreshed successfully
[[07:11:16]] [SUCCESS] Screenshot refreshed successfully
[[07:11:15]] [SUCCESS] Screenshot refreshed
[[07:11:15]] [INFO] Refreshing screenshot...
[[07:11:15]] [INFO] pCPTAtSZbf=pass
[[07:11:11]] [INFO] pCPTAtSZbf=running
[[07:11:11]] [INFO] Executing action 159/641: iOS Function: text - Text: "Wonderbaby@5"
[[07:11:11]] [SUCCESS] Screenshot refreshed successfully
[[07:11:11]] [SUCCESS] Screenshot refreshed successfully
[[07:11:11]] [SUCCESS] Screenshot refreshed
[[07:11:11]] [INFO] Refreshing screenshot...
[[07:11:11]] [INFO] DaVBARRwft=pass
[[07:11:07]] [INFO] DaVBARRwft=running
[[07:11:07]] [INFO] Executing action 158/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[07:11:06]] [SUCCESS] Screenshot refreshed successfully
[[07:11:06]] [SUCCESS] Screenshot refreshed successfully
[[07:11:06]] [SUCCESS] Screenshot refreshed
[[07:11:06]] [INFO] Refreshing screenshot...
[[07:11:06]] [INFO] e1RoZWCZJb=pass
[[07:11:01]] [INFO] e1RoZWCZJb=running
[[07:11:01]] [INFO] Executing action 157/641: iOS Function: text - Text: "<EMAIL>"
[[07:11:01]] [SUCCESS] Screenshot refreshed successfully
[[07:11:01]] [SUCCESS] Screenshot refreshed successfully
[[07:11:01]] [SUCCESS] Screenshot refreshed
[[07:11:01]] [INFO] Refreshing screenshot...
[[07:11:01]] [INFO] 50Z2jrodNd=pass
[[07:10:57]] [INFO] 50Z2jrodNd=running
[[07:10:57]] [INFO] Executing action 156/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:10:57]] [SUCCESS] Screenshot refreshed successfully
[[07:10:57]] [SUCCESS] Screenshot refreshed successfully
[[07:10:56]] [SUCCESS] Screenshot refreshed
[[07:10:56]] [INFO] Refreshing screenshot...
[[07:10:56]] [INFO] q9ZiyYoE5B=pass
[[07:10:54]] [INFO] q9ZiyYoE5B=running
[[07:10:54]] [INFO] Executing action 155/641: iOS Function: alert_accept
[[07:10:54]] [SUCCESS] Screenshot refreshed successfully
[[07:10:54]] [SUCCESS] Screenshot refreshed successfully
[[07:10:53]] [SUCCESS] Screenshot refreshed
[[07:10:53]] [INFO] Refreshing screenshot...
[[07:10:53]] [INFO] 6PL8P3rT57=pass
[[07:10:49]] [INFO] 6PL8P3rT57=running
[[07:10:49]] [INFO] Executing action 154/641: Tap on Text: "Sign"
[[07:10:49]] [SUCCESS] Screenshot refreshed successfully
[[07:10:49]] [SUCCESS] Screenshot refreshed successfully
[[07:10:49]] [SUCCESS] Screenshot refreshed
[[07:10:49]] [INFO] Refreshing screenshot...
[[07:10:49]] [INFO] 2YGctqXNED=pass
[[07:10:42]] [INFO] 2YGctqXNED=running
[[07:10:42]] [INFO] Executing action 153/641: Tap on element with accessibility_id: Continue to details
[[07:10:42]] [SUCCESS] Screenshot refreshed successfully
[[07:10:42]] [SUCCESS] Screenshot refreshed successfully
[[07:10:42]] [SUCCESS] Screenshot refreshed
[[07:10:42]] [INFO] Refreshing screenshot...
[[07:10:42]] [INFO] 2YGctqXNED=pass
[[07:10:33]] [INFO] 2YGctqXNED=running
[[07:10:33]] [INFO] Executing action 152/641: Swipe up till element accessibilityid: "Continue to details" is visible
[[07:10:33]] [SUCCESS] Screenshot refreshed successfully
[[07:10:33]] [SUCCESS] Screenshot refreshed successfully
[[07:10:33]] [SUCCESS] Screenshot refreshed
[[07:10:33]] [INFO] Refreshing screenshot...
[[07:10:33]] [INFO] tufIibCj03=pass
[[07:10:29]] [INFO] tufIibCj03=running
[[07:10:29]] [INFO] Executing action 151/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[07:10:28]] [SUCCESS] Screenshot refreshed successfully
[[07:10:28]] [SUCCESS] Screenshot refreshed successfully
[[07:10:28]] [SUCCESS] Screenshot refreshed
[[07:10:28]] [INFO] Refreshing screenshot...
[[07:10:28]] [INFO] g8u66qfKkX=pass
[[07:10:25]] [INFO] g8u66qfKkX=running
[[07:10:25]] [INFO] Executing action 150/641: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:10:25]] [SUCCESS] Screenshot refreshed successfully
[[07:10:25]] [SUCCESS] Screenshot refreshed successfully
[[07:10:24]] [SUCCESS] Screenshot refreshed
[[07:10:24]] [INFO] Refreshing screenshot...
[[07:10:24]] [INFO] ZBXuV4sJUR=pass
[[07:10:12]] [INFO] ZBXuV4sJUR=running
[[07:10:12]] [INFO] Executing action 149/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[07:10:12]] [SUCCESS] Screenshot refreshed successfully
[[07:10:12]] [SUCCESS] Screenshot refreshed successfully
[[07:10:11]] [SUCCESS] Screenshot refreshed
[[07:10:11]] [INFO] Refreshing screenshot...
[[07:10:11]] [INFO] XryN8qR1DX=pass
[[07:10:07]] [INFO] XryN8qR1DX=running
[[07:10:07]] [INFO] Executing action 148/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:10:07]] [SUCCESS] Screenshot refreshed successfully
[[07:10:07]] [SUCCESS] Screenshot refreshed successfully
[[07:10:06]] [SUCCESS] Screenshot refreshed
[[07:10:06]] [INFO] Refreshing screenshot...
[[07:10:06]] [INFO] XcWXIMtv1E=pass
[[07:10:00]] [INFO] XcWXIMtv1E=running
[[07:10:00]] [INFO] Executing action 147/641: Wait for 5 ms
[[07:10:00]] [SUCCESS] Screenshot refreshed successfully
[[07:10:00]] [SUCCESS] Screenshot refreshed successfully
[[07:10:00]] [SUCCESS] Screenshot refreshed
[[07:10:00]] [INFO] Refreshing screenshot...
[[07:10:00]] [INFO] S1cQQxksEj=pass
[[07:09:52]] [INFO] S1cQQxksEj=running
[[07:09:52]] [INFO] Executing action 146/641: Tap on element with accessibility_id: Add to bag
[[07:09:52]] [SUCCESS] Screenshot refreshed successfully
[[07:09:52]] [SUCCESS] Screenshot refreshed successfully
[[07:09:51]] [SUCCESS] Screenshot refreshed
[[07:09:51]] [INFO] Refreshing screenshot...
[[07:09:51]] [INFO] K2w9XUGwnb=pass
[[07:09:43]] [INFO] K2w9XUGwnb=running
[[07:09:43]] [INFO] Executing action 145/641: Swipe up till element accessibility_id: "Add to bag" is visible
[[07:09:43]] [SUCCESS] Screenshot refreshed successfully
[[07:09:43]] [SUCCESS] Screenshot refreshed successfully
[[07:09:42]] [SUCCESS] Screenshot refreshed
[[07:09:42]] [INFO] Refreshing screenshot...
[[07:09:42]] [INFO] BTYxjEaZEk=pass
[[07:09:38]] [INFO] BTYxjEaZEk=running
[[07:09:38]] [INFO] Executing action 144/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:09:38]] [SUCCESS] Screenshot refreshed successfully
[[07:09:38]] [SUCCESS] Screenshot refreshed successfully
[[07:09:38]] [SUCCESS] Screenshot refreshed
[[07:09:38]] [INFO] Refreshing screenshot...
[[07:09:38]] [INFO] YC6bBrKQgq=pass
[[07:09:34]] [INFO] YC6bBrKQgq=running
[[07:09:34]] [INFO] Executing action 143/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:09:34]] [SUCCESS] Screenshot refreshed successfully
[[07:09:34]] [SUCCESS] Screenshot refreshed successfully
[[07:09:34]] [SUCCESS] Screenshot refreshed
[[07:09:34]] [INFO] Refreshing screenshot...
[[07:09:34]] [INFO] aRgHcQcLDP=pass
[[07:09:30]] [INFO] aRgHcQcLDP=running
[[07:09:30]] [INFO] Executing action 142/641: iOS Function: text - Text: "uno card"
[[07:09:29]] [SUCCESS] Screenshot refreshed successfully
[[07:09:29]] [SUCCESS] Screenshot refreshed successfully
[[07:09:29]] [SUCCESS] Screenshot refreshed
[[07:09:29]] [INFO] Refreshing screenshot...
[[07:09:29]] [INFO] 4PZC1vVWJW=pass
[[07:09:24]] [INFO] 4PZC1vVWJW=running
[[07:09:24]] [INFO] Executing action 141/641: Tap on Text: "Find"
[[07:09:24]] [SUCCESS] Screenshot refreshed successfully
[[07:09:24]] [SUCCESS] Screenshot refreshed successfully
[[07:09:24]] [SUCCESS] Screenshot refreshed
[[07:09:24]] [INFO] Refreshing screenshot...
[[07:09:24]] [INFO] XryN8qR1DX=pass
[[07:09:20]] [INFO] XryN8qR1DX=running
[[07:09:20]] [INFO] Executing action 140/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:09:20]] [SUCCESS] Screenshot refreshed successfully
[[07:09:20]] [SUCCESS] Screenshot refreshed successfully
[[07:09:19]] [SUCCESS] Screenshot refreshed
[[07:09:19]] [INFO] Refreshing screenshot...
[[07:09:19]] [INFO] 7WYExJTqjp=pass
[[07:09:16]] [INFO] 7WYExJTqjp=running
[[07:09:16]] [INFO] Executing action 139/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:09:15]] [SUCCESS] Screenshot refreshed successfully
[[07:09:15]] [SUCCESS] Screenshot refreshed successfully
[[07:09:15]] [SUCCESS] Screenshot refreshed
[[07:09:15]] [INFO] Refreshing screenshot...
[[07:09:15]] [INFO] 4WfPFN961S=pass
[[07:09:08]] [INFO] 4WfPFN961S=running
[[07:09:08]] [INFO] Executing action 138/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:09:08]] [SUCCESS] Screenshot refreshed successfully
[[07:09:08]] [SUCCESS] Screenshot refreshed successfully
[[07:09:08]] [SUCCESS] Screenshot refreshed
[[07:09:08]] [INFO] Refreshing screenshot...
[[07:09:08]] [INFO] NurQsFoMkE=pass
[[07:09:03]] [SUCCESS] Screenshot refreshed successfully
[[07:09:03]] [SUCCESS] Screenshot refreshed successfully
[[07:09:03]] [INFO] NurQsFoMkE=running
[[07:09:03]] [INFO] Executing action 137/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:09:03]] [SUCCESS] Screenshot refreshed
[[07:09:03]] [INFO] Refreshing screenshot...
[[07:09:02]] [SUCCESS] Screenshot refreshed successfully
[[07:09:02]] [SUCCESS] Screenshot refreshed successfully
[[07:09:02]] [SUCCESS] Screenshot refreshed
[[07:09:02]] [INFO] Refreshing screenshot...
[[07:08:58]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[07:08:58]] [SUCCESS] Screenshot refreshed successfully
[[07:08:58]] [SUCCESS] Screenshot refreshed successfully
[[07:08:57]] [SUCCESS] Screenshot refreshed
[[07:08:57]] [INFO] Refreshing screenshot...
[[07:08:53]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:08:53]] [SUCCESS] Screenshot refreshed successfully
[[07:08:53]] [SUCCESS] Screenshot refreshed successfully
[[07:08:53]] [SUCCESS] Screenshot refreshed
[[07:08:53]] [INFO] Refreshing screenshot...
[[07:08:48]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[07:08:48]] [SUCCESS] Screenshot refreshed successfully
[[07:08:48]] [SUCCESS] Screenshot refreshed successfully
[[07:08:48]] [SUCCESS] Screenshot refreshed
[[07:08:48]] [INFO] Refreshing screenshot...
[[07:08:44]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:08:43]] [SUCCESS] Screenshot refreshed successfully
[[07:08:43]] [SUCCESS] Screenshot refreshed successfully
[[07:08:43]] [SUCCESS] Screenshot refreshed
[[07:08:43]] [INFO] Refreshing screenshot...
[[07:08:38]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:08:38]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[07:08:38]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[07:08:38]] [INFO] APqAlKbucp=running
[[07:08:38]] [INFO] Executing action 136/641: Execute Test Case: Kmart-Signin (5 steps)
[[07:08:37]] [SUCCESS] Screenshot refreshed successfully
[[07:08:37]] [SUCCESS] Screenshot refreshed successfully
[[07:08:37]] [SUCCESS] Screenshot refreshed
[[07:08:37]] [INFO] Refreshing screenshot...
[[07:08:37]] [INFO] byEe7qbCpq=pass
[[07:08:35]] [INFO] byEe7qbCpq=running
[[07:08:35]] [INFO] Executing action 135/641: iOS Function: alert_accept
[[07:08:34]] [SUCCESS] Screenshot refreshed successfully
[[07:08:34]] [SUCCESS] Screenshot refreshed successfully
[[07:08:34]] [SUCCESS] Screenshot refreshed
[[07:08:34]] [INFO] Refreshing screenshot...
[[07:08:34]] [INFO] L6wTorOX8B=pass
[[07:08:31]] [INFO] L6wTorOX8B=running
[[07:08:31]] [INFO] Executing action 134/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[07:08:30]] [SUCCESS] Screenshot refreshed successfully
[[07:08:30]] [SUCCESS] Screenshot refreshed successfully
[[07:08:30]] [SUCCESS] Screenshot refreshed
[[07:08:30]] [INFO] Refreshing screenshot...
[[07:08:30]] [INFO] XryN8qR1DX=pass
[[07:08:26]] [INFO] XryN8qR1DX=running
[[07:08:26]] [INFO] Executing action 133/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:08:26]] [SUCCESS] Screenshot refreshed successfully
[[07:08:26]] [SUCCESS] Screenshot refreshed successfully
[[07:08:26]] [SUCCESS] Screenshot refreshed
[[07:08:26]] [INFO] Refreshing screenshot...
[[07:08:26]] [INFO] lCSewtjn1z=pass
[[07:08:21]] [INFO] lCSewtjn1z=running
[[07:08:21]] [INFO] Executing action 132/641: Restart app: env[appid]
[[07:08:21]] [SUCCESS] Screenshot refreshed successfully
[[07:08:21]] [SUCCESS] Screenshot refreshed successfully
[[07:08:21]] [SUCCESS] Screenshot refreshed
[[07:08:21]] [INFO] Refreshing screenshot...
[[07:08:21]] [INFO] IJh702cxG0=pass
[[07:08:17]] [INFO] IJh702cxG0=running
[[07:08:17]] [INFO] Executing action 131/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:08:17]] [SUCCESS] Screenshot refreshed successfully
[[07:08:17]] [SUCCESS] Screenshot refreshed successfully
[[07:08:16]] [SUCCESS] Screenshot refreshed
[[07:08:16]] [INFO] Refreshing screenshot...
[[07:08:16]] [INFO] 4WfPFN961S=pass
[[07:08:10]] [INFO] 4WfPFN961S=running
[[07:08:10]] [INFO] Executing action 130/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:08:09]] [SUCCESS] Screenshot refreshed successfully
[[07:08:09]] [SUCCESS] Screenshot refreshed successfully
[[07:08:09]] [SUCCESS] Screenshot refreshed
[[07:08:09]] [INFO] Refreshing screenshot...
[[07:08:09]] [INFO] AOcOOSuOsB=pass
[[07:08:05]] [INFO] AOcOOSuOsB=running
[[07:08:05]] [INFO] Executing action 129/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:08:04]] [SUCCESS] Screenshot refreshed successfully
[[07:08:04]] [SUCCESS] Screenshot refreshed successfully
[[07:08:04]] [SUCCESS] Screenshot refreshed
[[07:08:04]] [INFO] Refreshing screenshot...
[[07:08:04]] [INFO] AOcOOSuOsB=pass
[[07:07:57]] [INFO] AOcOOSuOsB=running
[[07:07:57]] [INFO] Executing action 128/641: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:07:57]] [SUCCESS] Screenshot refreshed successfully
[[07:07:57]] [SUCCESS] Screenshot refreshed successfully
[[07:07:57]] [SUCCESS] Screenshot refreshed
[[07:07:57]] [INFO] Refreshing screenshot...
[[07:07:57]] [INFO] N2yjynioko=pass
[[07:07:52]] [INFO] N2yjynioko=running
[[07:07:52]] [INFO] Executing action 127/641: iOS Function: text - Text: "Wonderbaby@5"
[[07:07:52]] [SUCCESS] Screenshot refreshed successfully
[[07:07:52]] [SUCCESS] Screenshot refreshed successfully
[[07:07:52]] [SUCCESS] Screenshot refreshed
[[07:07:52]] [INFO] Refreshing screenshot...
[[07:07:52]] [INFO] SHaIduBnay=pass
[[07:07:48]] [INFO] SHaIduBnay=running
[[07:07:48]] [INFO] Executing action 126/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[07:07:48]] [SUCCESS] Screenshot refreshed successfully
[[07:07:48]] [SUCCESS] Screenshot refreshed successfully
[[07:07:47]] [SUCCESS] Screenshot refreshed
[[07:07:47]] [INFO] Refreshing screenshot...
[[07:07:47]] [INFO] wuIMlAwYVA=pass
[[07:07:43]] [INFO] wuIMlAwYVA=running
[[07:07:43]] [INFO] Executing action 125/641: iOS Function: text - Text: "env[uname1]"
[[07:07:42]] [SUCCESS] Screenshot refreshed successfully
[[07:07:42]] [SUCCESS] Screenshot refreshed successfully
[[07:07:42]] [SUCCESS] Screenshot refreshed
[[07:07:42]] [INFO] Refreshing screenshot...
[[07:07:42]] [INFO] 50Z2jrodNd=pass
[[07:07:38]] [INFO] 50Z2jrodNd=running
[[07:07:38]] [INFO] Executing action 124/641: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[07:07:38]] [SUCCESS] Screenshot refreshed successfully
[[07:07:38]] [SUCCESS] Screenshot refreshed successfully
[[07:07:37]] [SUCCESS] Screenshot refreshed
[[07:07:37]] [INFO] Refreshing screenshot...
[[07:07:37]] [INFO] VK2oI6mXSB=pass
[[07:07:34]] [INFO] VK2oI6mXSB=running
[[07:07:34]] [INFO] Executing action 123/641: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[07:07:34]] [SUCCESS] Screenshot refreshed successfully
[[07:07:34]] [SUCCESS] Screenshot refreshed successfully
[[07:07:33]] [SUCCESS] Screenshot refreshed
[[07:07:33]] [INFO] Refreshing screenshot...
[[07:07:33]] [INFO] q9ZiyYoE5B=pass
[[07:07:31]] [INFO] q9ZiyYoE5B=running
[[07:07:31]] [INFO] Executing action 122/641: iOS Function: alert_accept
[[07:07:31]] [SUCCESS] Screenshot refreshed successfully
[[07:07:31]] [SUCCESS] Screenshot refreshed successfully
[[07:07:30]] [SUCCESS] Screenshot refreshed
[[07:07:30]] [INFO] Refreshing screenshot...
[[07:07:30]] [INFO] 4PZC1vVWJW=pass
[[07:07:26]] [INFO] 4PZC1vVWJW=running
[[07:07:26]] [INFO] Executing action 121/641: Tap on Text: "Sign"
[[07:07:25]] [SUCCESS] Screenshot refreshed successfully
[[07:07:25]] [SUCCESS] Screenshot refreshed successfully
[[07:07:25]] [SUCCESS] Screenshot refreshed
[[07:07:25]] [INFO] Refreshing screenshot...
[[07:07:25]] [INFO] mcscWdhpn2=pass
[[07:07:08]] [INFO] mcscWdhpn2=running
[[07:07:08]] [INFO] Executing action 120/641: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[07:07:08]] [SUCCESS] Screenshot refreshed successfully
[[07:07:08]] [SUCCESS] Screenshot refreshed successfully
[[07:07:07]] [SUCCESS] Screenshot refreshed
[[07:07:07]] [INFO] Refreshing screenshot...
[[07:07:07]] [INFO] 6zUBxjSFym=pass
[[07:07:03]] [INFO] 6zUBxjSFym=running
[[07:07:03]] [INFO] Executing action 119/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:07:03]] [SUCCESS] Screenshot refreshed successfully
[[07:07:03]] [SUCCESS] Screenshot refreshed successfully
[[07:07:03]] [SUCCESS] Screenshot refreshed
[[07:07:03]] [INFO] Refreshing screenshot...
[[07:07:03]] [INFO] BTYxjEaZEk=pass
[[07:06:59]] [INFO] BTYxjEaZEk=running
[[07:06:59]] [INFO] Executing action 118/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:06:59]] [SUCCESS] Screenshot refreshed successfully
[[07:06:59]] [SUCCESS] Screenshot refreshed successfully
[[07:06:58]] [SUCCESS] Screenshot refreshed
[[07:06:58]] [INFO] Refreshing screenshot...
[[07:06:58]] [INFO] YC6bBrKQgq=pass
[[07:06:55]] [INFO] YC6bBrKQgq=running
[[07:06:55]] [INFO] Executing action 117/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:06:55]] [SUCCESS] Screenshot refreshed successfully
[[07:06:55]] [SUCCESS] Screenshot refreshed successfully
[[07:06:54]] [SUCCESS] Screenshot refreshed
[[07:06:54]] [INFO] Refreshing screenshot...
[[07:06:54]] [INFO] aRgHcQcLDP=pass
[[07:06:50]] [INFO] aRgHcQcLDP=running
[[07:06:50]] [INFO] Executing action 116/641: iOS Function: text - Text: "uno card"
[[07:06:50]] [SUCCESS] Screenshot refreshed successfully
[[07:06:50]] [SUCCESS] Screenshot refreshed successfully
[[07:06:50]] [SUCCESS] Screenshot refreshed
[[07:06:50]] [INFO] Refreshing screenshot...
[[07:06:50]] [INFO] 4PZC1vVWJW=pass
[[07:06:45]] [INFO] 4PZC1vVWJW=running
[[07:06:45]] [INFO] Executing action 115/641: Tap on Text: "Find"
[[07:06:45]] [SUCCESS] Screenshot refreshed successfully
[[07:06:45]] [SUCCESS] Screenshot refreshed successfully
[[07:06:45]] [SUCCESS] Screenshot refreshed
[[07:06:45]] [INFO] Refreshing screenshot...
[[07:06:45]] [INFO] lCSewtjn1z=pass
[[07:06:40]] [INFO] lCSewtjn1z=running
[[07:06:40]] [INFO] Executing action 114/641: Restart app: env[appid]
[[07:06:40]] [SUCCESS] Screenshot refreshed successfully
[[07:06:40]] [SUCCESS] Screenshot refreshed successfully
[[07:06:39]] [SUCCESS] Screenshot refreshed
[[07:06:39]] [INFO] Refreshing screenshot...
[[07:06:39]] [INFO] A1Wz7p1iVG=pass
[[07:06:36]] [INFO] A1Wz7p1iVG=running
[[07:06:36]] [INFO] Executing action 113/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:06:35]] [SUCCESS] Screenshot refreshed successfully
[[07:06:35]] [SUCCESS] Screenshot refreshed successfully
[[07:06:35]] [SUCCESS] Screenshot refreshed
[[07:06:35]] [INFO] Refreshing screenshot...
[[07:06:35]] [INFO] ehyLmdZWP2=pass
[[07:06:28]] [INFO] ehyLmdZWP2=running
[[07:06:28]] [INFO] Executing action 112/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:06:28]] [SUCCESS] Screenshot refreshed successfully
[[07:06:28]] [SUCCESS] Screenshot refreshed successfully
[[07:06:28]] [SUCCESS] Screenshot refreshed
[[07:06:28]] [INFO] Refreshing screenshot...
[[07:06:28]] [INFO] ydRnBBO1vR=pass
[[07:06:24]] [INFO] ydRnBBO1vR=running
[[07:06:24]] [INFO] Executing action 111/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:06:24]] [SUCCESS] Screenshot refreshed successfully
[[07:06:24]] [SUCCESS] Screenshot refreshed successfully
[[07:06:23]] [SUCCESS] Screenshot refreshed
[[07:06:23]] [INFO] Refreshing screenshot...
[[07:06:23]] [INFO] quZwUwj3a8=pass
[[07:06:20]] [INFO] quZwUwj3a8=running
[[07:06:20]] [INFO] Executing action 110/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[07:06:20]] [SUCCESS] Screenshot refreshed successfully
[[07:06:20]] [SUCCESS] Screenshot refreshed successfully
[[07:06:20]] [SUCCESS] Screenshot refreshed
[[07:06:20]] [INFO] Refreshing screenshot...
[[07:06:20]] [INFO] FHRlQXe58T=pass
[[07:06:16]] [INFO] FHRlQXe58T=running
[[07:06:16]] [INFO] Executing action 109/641: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:06:15]] [SUCCESS] Screenshot refreshed successfully
[[07:06:15]] [SUCCESS] Screenshot refreshed successfully
[[07:06:15]] [SUCCESS] Screenshot refreshed
[[07:06:15]] [INFO] Refreshing screenshot...
[[07:06:15]] [INFO] 8uojw2klHA=pass
[[07:06:11]] [INFO] 8uojw2klHA=running
[[07:06:11]] [INFO] Executing action 108/641: iOS Function: text - Text: "env[pwd]"
[[07:06:11]] [SUCCESS] Screenshot refreshed successfully
[[07:06:11]] [SUCCESS] Screenshot refreshed successfully
[[07:06:10]] [SUCCESS] Screenshot refreshed
[[07:06:10]] [INFO] Refreshing screenshot...
[[07:06:10]] [INFO] SHaIduBnay=pass
[[07:06:06]] [INFO] SHaIduBnay=running
[[07:06:06]] [INFO] Executing action 107/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:06:06]] [SUCCESS] Screenshot refreshed successfully
[[07:06:06]] [SUCCESS] Screenshot refreshed successfully
[[07:06:06]] [SUCCESS] Screenshot refreshed
[[07:06:06]] [INFO] Refreshing screenshot...
[[07:06:06]] [INFO] TGoXyeQtB7=pass
[[07:06:01]] [INFO] TGoXyeQtB7=running
[[07:06:01]] [INFO] Executing action 106/641: iOS Function: text - Text: "env[uname]"
[[07:06:01]] [SUCCESS] Screenshot refreshed successfully
[[07:06:01]] [SUCCESS] Screenshot refreshed successfully
[[07:06:01]] [SUCCESS] Screenshot refreshed
[[07:06:01]] [INFO] Refreshing screenshot...
[[07:06:01]] [INFO] rLCI6NVxSc=pass
[[07:05:57]] [INFO] rLCI6NVxSc=running
[[07:05:57]] [INFO] Executing action 105/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:05:57]] [SUCCESS] Screenshot refreshed successfully
[[07:05:57]] [SUCCESS] Screenshot refreshed successfully
[[07:05:56]] [SUCCESS] Screenshot refreshed
[[07:05:56]] [INFO] Refreshing screenshot...
[[07:05:56]] [INFO] 6mHVWI3j5e=pass
[[07:05:53]] [INFO] 6mHVWI3j5e=running
[[07:05:53]] [INFO] Executing action 104/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:05:52]] [SUCCESS] Screenshot refreshed successfully
[[07:05:52]] [SUCCESS] Screenshot refreshed successfully
[[07:05:52]] [SUCCESS] Screenshot refreshed
[[07:05:52]] [INFO] Refreshing screenshot...
[[07:05:52]] [INFO] rJVGLpLWM3=pass
[[07:05:50]] [INFO] rJVGLpLWM3=running
[[07:05:50]] [INFO] Executing action 103/641: iOS Function: alert_accept
[[07:05:49]] [SUCCESS] Screenshot refreshed successfully
[[07:05:49]] [SUCCESS] Screenshot refreshed successfully
[[07:05:49]] [SUCCESS] Screenshot refreshed
[[07:05:49]] [INFO] Refreshing screenshot...
[[07:05:49]] [INFO] WlISsMf9QA=pass
[[07:05:46]] [INFO] WlISsMf9QA=running
[[07:05:46]] [INFO] Executing action 102/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[07:05:45]] [SUCCESS] Screenshot refreshed successfully
[[07:05:45]] [SUCCESS] Screenshot refreshed successfully
[[07:05:45]] [SUCCESS] Screenshot refreshed
[[07:05:45]] [INFO] Refreshing screenshot...
[[07:05:45]] [INFO] IvqPpScAJa=pass
[[07:05:41]] [INFO] IvqPpScAJa=running
[[07:05:41]] [INFO] Executing action 101/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:05:41]] [SUCCESS] Screenshot refreshed successfully
[[07:05:41]] [SUCCESS] Screenshot refreshed successfully
[[07:05:41]] [SUCCESS] Screenshot refreshed
[[07:05:41]] [INFO] Refreshing screenshot...
[[07:05:41]] [INFO] bGo3feCwBQ=pass
[[07:05:37]] [INFO] bGo3feCwBQ=running
[[07:05:37]] [INFO] Executing action 100/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:05:37]] [SUCCESS] Screenshot refreshed successfully
[[07:05:37]] [SUCCESS] Screenshot refreshed successfully
[[07:05:36]] [SUCCESS] Screenshot refreshed
[[07:05:36]] [INFO] Refreshing screenshot...
[[07:05:36]] [INFO] 4WfPFN961S=pass
[[07:05:30]] [INFO] 4WfPFN961S=running
[[07:05:30]] [INFO] Executing action 99/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:05:29]] [SUCCESS] Screenshot refreshed successfully
[[07:05:29]] [SUCCESS] Screenshot refreshed successfully
[[07:05:29]] [SUCCESS] Screenshot refreshed
[[07:05:29]] [INFO] Refreshing screenshot...
[[07:05:29]] [INFO] F0gZF1jEnT=pass
[[07:05:25]] [INFO] F0gZF1jEnT=running
[[07:05:25]] [INFO] Executing action 98/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:05:25]] [SUCCESS] Screenshot refreshed successfully
[[07:05:25]] [SUCCESS] Screenshot refreshed successfully
[[07:05:25]] [SUCCESS] Screenshot refreshed
[[07:05:25]] [INFO] Refreshing screenshot...
[[07:05:25]] [INFO] EDHl0X27Wi=pass
[[07:05:21]] [INFO] EDHl0X27Wi=running
[[07:05:21]] [INFO] Executing action 97/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[07:05:21]] [SUCCESS] Screenshot refreshed successfully
[[07:05:21]] [SUCCESS] Screenshot refreshed successfully
[[07:05:21]] [SUCCESS] Screenshot refreshed
[[07:05:21]] [INFO] Refreshing screenshot...
[[07:05:21]] [INFO] j8NXU87gV3=pass
[[07:05:17]] [INFO] j8NXU87gV3=running
[[07:05:17]] [INFO] Executing action 96/641: iOS Function: text - Text: "env[pwd]"
[[07:05:16]] [SUCCESS] Screenshot refreshed successfully
[[07:05:16]] [SUCCESS] Screenshot refreshed successfully
[[07:05:16]] [SUCCESS] Screenshot refreshed
[[07:05:16]] [INFO] Refreshing screenshot...
[[07:05:16]] [INFO] dpVaKL19uc=pass
[[07:05:12]] [INFO] dpVaKL19uc=running
[[07:05:12]] [INFO] Executing action 95/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:05:12]] [SUCCESS] Screenshot refreshed successfully
[[07:05:12]] [SUCCESS] Screenshot refreshed successfully
[[07:05:12]] [SUCCESS] Screenshot refreshed
[[07:05:12]] [INFO] Refreshing screenshot...
[[07:05:12]] [INFO] eOm1WExcrK=pass
[[07:05:07]] [INFO] eOm1WExcrK=running
[[07:05:07]] [INFO] Executing action 94/641: iOS Function: text - Text: "env[uname]"
[[07:05:07]] [SUCCESS] Screenshot refreshed successfully
[[07:05:07]] [SUCCESS] Screenshot refreshed successfully
[[07:05:06]] [SUCCESS] Screenshot refreshed
[[07:05:06]] [INFO] Refreshing screenshot...
[[07:05:06]] [INFO] 50Z2jrodNd=pass
[[07:05:02]] [INFO] 50Z2jrodNd=running
[[07:05:02]] [INFO] Executing action 93/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:05:02]] [SUCCESS] Screenshot refreshed successfully
[[07:05:02]] [SUCCESS] Screenshot refreshed successfully
[[07:05:02]] [SUCCESS] Screenshot refreshed
[[07:05:02]] [INFO] Refreshing screenshot...
[[07:05:02]] [INFO] eJnHS9n9VL=pass
[[07:04:58]] [INFO] eJnHS9n9VL=running
[[07:04:58]] [INFO] Executing action 92/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:04:58]] [SUCCESS] Screenshot refreshed successfully
[[07:04:58]] [SUCCESS] Screenshot refreshed successfully
[[07:04:57]] [SUCCESS] Screenshot refreshed
[[07:04:57]] [INFO] Refreshing screenshot...
[[07:04:57]] [INFO] XuLgjNG74w=pass
[[07:04:55]] [INFO] XuLgjNG74w=running
[[07:04:55]] [INFO] Executing action 91/641: iOS Function: alert_accept
[[07:04:55]] [SUCCESS] Screenshot refreshed successfully
[[07:04:55]] [SUCCESS] Screenshot refreshed successfully
[[07:04:54]] [SUCCESS] Screenshot refreshed
[[07:04:54]] [INFO] Refreshing screenshot...
[[07:04:54]] [INFO] qA1ap4n1m4=pass
[[07:04:48]] [INFO] qA1ap4n1m4=running
[[07:04:48]] [INFO] Executing action 90/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:04:48]] [SUCCESS] Screenshot refreshed successfully
[[07:04:48]] [SUCCESS] Screenshot refreshed successfully
[[07:04:47]] [SUCCESS] Screenshot refreshed
[[07:04:47]] [INFO] Refreshing screenshot...
[[07:04:47]] [INFO] JXFxYCr98V=pass
[[07:04:42]] [INFO] JXFxYCr98V=running
[[07:04:42]] [INFO] Executing action 89/641: Restart app: env[appid]
[[07:04:40]] [INFO] === RETRYING TEST CASE: All_Sign_ins_20250501131834.json (Attempt 2 of 3) ===
[[07:04:40]] [INFO] AOcOOSuOsB=fail
[[07:04:40]] [ERROR] Action 128 failed: Element with xpath '//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]' not found within timeout of 30.0 seconds
[[07:04:09]] [INFO] AOcOOSuOsB=running
[[07:04:09]] [INFO] Executing action 128/641: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:04:08]] [SUCCESS] Screenshot refreshed successfully
[[07:04:08]] [SUCCESS] Screenshot refreshed successfully
[[07:04:08]] [SUCCESS] Screenshot refreshed
[[07:04:08]] [INFO] Refreshing screenshot...
[[07:04:08]] [INFO] N2yjynioko=pass
[[07:04:04]] [INFO] N2yjynioko=running
[[07:04:04]] [INFO] Executing action 127/641: iOS Function: text - Text: "Wonderbaby@5"
[[07:04:03]] [SUCCESS] Screenshot refreshed successfully
[[07:04:03]] [SUCCESS] Screenshot refreshed successfully
[[07:04:03]] [SUCCESS] Screenshot refreshed
[[07:04:03]] [INFO] Refreshing screenshot...
[[07:04:03]] [INFO] SHaIduBnay=pass
[[07:03:59]] [INFO] SHaIduBnay=running
[[07:03:59]] [INFO] Executing action 126/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[07:03:59]] [SUCCESS] Screenshot refreshed successfully
[[07:03:59]] [SUCCESS] Screenshot refreshed successfully
[[07:03:58]] [SUCCESS] Screenshot refreshed
[[07:03:58]] [INFO] Refreshing screenshot...
[[07:03:58]] [INFO] wuIMlAwYVA=pass
[[07:03:54]] [INFO] wuIMlAwYVA=running
[[07:03:54]] [INFO] Executing action 125/641: iOS Function: text - Text: "env[uname1]"
[[07:03:53]] [SUCCESS] Screenshot refreshed successfully
[[07:03:53]] [SUCCESS] Screenshot refreshed successfully
[[07:03:53]] [SUCCESS] Screenshot refreshed
[[07:03:53]] [INFO] Refreshing screenshot...
[[07:03:53]] [INFO] 50Z2jrodNd=pass
[[07:03:49]] [INFO] 50Z2jrodNd=running
[[07:03:49]] [INFO] Executing action 124/641: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[07:03:49]] [SUCCESS] Screenshot refreshed successfully
[[07:03:49]] [SUCCESS] Screenshot refreshed successfully
[[07:03:49]] [SUCCESS] Screenshot refreshed
[[07:03:49]] [INFO] Refreshing screenshot...
[[07:03:49]] [INFO] VK2oI6mXSB=pass
[[07:03:45]] [INFO] VK2oI6mXSB=running
[[07:03:45]] [INFO] Executing action 123/641: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[07:03:45]] [SUCCESS] Screenshot refreshed successfully
[[07:03:45]] [SUCCESS] Screenshot refreshed successfully
[[07:03:44]] [SUCCESS] Screenshot refreshed
[[07:03:44]] [INFO] Refreshing screenshot...
[[07:03:44]] [INFO] q9ZiyYoE5B=pass
[[07:03:42]] [INFO] q9ZiyYoE5B=running
[[07:03:42]] [INFO] Executing action 122/641: iOS Function: alert_accept
[[07:03:42]] [SUCCESS] Screenshot refreshed successfully
[[07:03:42]] [SUCCESS] Screenshot refreshed successfully
[[07:03:42]] [SUCCESS] Screenshot refreshed
[[07:03:42]] [INFO] Refreshing screenshot...
[[07:03:42]] [INFO] 4PZC1vVWJW=pass
[[07:03:37]] [INFO] 4PZC1vVWJW=running
[[07:03:37]] [INFO] Executing action 121/641: Tap on Text: "Sign"
[[07:03:36]] [SUCCESS] Screenshot refreshed successfully
[[07:03:36]] [SUCCESS] Screenshot refreshed successfully
[[07:03:36]] [SUCCESS] Screenshot refreshed
[[07:03:36]] [INFO] Refreshing screenshot...
[[07:03:36]] [INFO] mcscWdhpn2=pass
[[07:03:19]] [INFO] mcscWdhpn2=running
[[07:03:19]] [INFO] Executing action 120/641: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[07:03:19]] [SUCCESS] Screenshot refreshed successfully
[[07:03:19]] [SUCCESS] Screenshot refreshed successfully
[[07:03:18]] [SUCCESS] Screenshot refreshed
[[07:03:18]] [INFO] Refreshing screenshot...
[[07:03:18]] [INFO] 6zUBxjSFym=pass
[[07:03:14]] [INFO] 6zUBxjSFym=running
[[07:03:14]] [INFO] Executing action 119/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:03:14]] [SUCCESS] Screenshot refreshed successfully
[[07:03:14]] [SUCCESS] Screenshot refreshed successfully
[[07:03:14]] [SUCCESS] Screenshot refreshed
[[07:03:14]] [INFO] Refreshing screenshot...
[[07:03:14]] [INFO] BTYxjEaZEk=pass
[[07:03:10]] [INFO] BTYxjEaZEk=running
[[07:03:10]] [INFO] Executing action 118/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:03:10]] [SUCCESS] Screenshot refreshed successfully
[[07:03:10]] [SUCCESS] Screenshot refreshed successfully
[[07:03:09]] [SUCCESS] Screenshot refreshed
[[07:03:09]] [INFO] Refreshing screenshot...
[[07:03:09]] [INFO] YC6bBrKQgq=pass
[[07:03:06]] [INFO] YC6bBrKQgq=running
[[07:03:06]] [INFO] Executing action 117/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:03:06]] [SUCCESS] Screenshot refreshed successfully
[[07:03:06]] [SUCCESS] Screenshot refreshed successfully
[[07:03:05]] [SUCCESS] Screenshot refreshed
[[07:03:05]] [INFO] Refreshing screenshot...
[[07:03:05]] [INFO] aRgHcQcLDP=pass
[[07:03:01]] [INFO] aRgHcQcLDP=running
[[07:03:01]] [INFO] Executing action 116/641: iOS Function: text - Text: "uno card"
[[07:03:01]] [SUCCESS] Screenshot refreshed successfully
[[07:03:01]] [SUCCESS] Screenshot refreshed successfully
[[07:03:01]] [SUCCESS] Screenshot refreshed
[[07:03:01]] [INFO] Refreshing screenshot...
[[07:03:01]] [INFO] 4PZC1vVWJW=pass
[[07:02:56]] [INFO] 4PZC1vVWJW=running
[[07:02:56]] [INFO] Executing action 115/641: Tap on Text: "Find"
[[07:02:56]] [SUCCESS] Screenshot refreshed successfully
[[07:02:56]] [SUCCESS] Screenshot refreshed successfully
[[07:02:56]] [SUCCESS] Screenshot refreshed
[[07:02:56]] [INFO] Refreshing screenshot...
[[07:02:56]] [INFO] lCSewtjn1z=pass
[[07:02:51]] [INFO] lCSewtjn1z=running
[[07:02:51]] [INFO] Executing action 114/641: Restart app: env[appid]
[[07:02:51]] [SUCCESS] Screenshot refreshed successfully
[[07:02:51]] [SUCCESS] Screenshot refreshed successfully
[[07:02:51]] [SUCCESS] Screenshot refreshed
[[07:02:51]] [INFO] Refreshing screenshot...
[[07:02:51]] [INFO] A1Wz7p1iVG=pass
[[07:02:47]] [INFO] A1Wz7p1iVG=running
[[07:02:47]] [INFO] Executing action 113/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:02:46]] [SUCCESS] Screenshot refreshed successfully
[[07:02:46]] [SUCCESS] Screenshot refreshed successfully
[[07:02:46]] [SUCCESS] Screenshot refreshed
[[07:02:46]] [INFO] Refreshing screenshot...
[[07:02:46]] [INFO] ehyLmdZWP2=pass
[[07:02:39]] [INFO] ehyLmdZWP2=running
[[07:02:39]] [INFO] Executing action 112/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:02:39]] [SUCCESS] Screenshot refreshed successfully
[[07:02:39]] [SUCCESS] Screenshot refreshed successfully
[[07:02:39]] [SUCCESS] Screenshot refreshed
[[07:02:39]] [INFO] Refreshing screenshot...
[[07:02:39]] [INFO] ydRnBBO1vR=pass
[[07:02:35]] [INFO] ydRnBBO1vR=running
[[07:02:35]] [INFO] Executing action 111/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:02:35]] [SUCCESS] Screenshot refreshed successfully
[[07:02:35]] [SUCCESS] Screenshot refreshed successfully
[[07:02:35]] [SUCCESS] Screenshot refreshed
[[07:02:35]] [INFO] Refreshing screenshot...
[[07:02:35]] [INFO] quZwUwj3a8=pass
[[07:02:31]] [INFO] quZwUwj3a8=running
[[07:02:31]] [INFO] Executing action 110/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[07:02:31]] [SUCCESS] Screenshot refreshed successfully
[[07:02:31]] [SUCCESS] Screenshot refreshed successfully
[[07:02:31]] [SUCCESS] Screenshot refreshed
[[07:02:31]] [INFO] Refreshing screenshot...
[[07:02:31]] [INFO] FHRlQXe58T=pass
[[07:02:27]] [INFO] FHRlQXe58T=running
[[07:02:27]] [INFO] Executing action 109/641: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:02:27]] [SUCCESS] Screenshot refreshed successfully
[[07:02:27]] [SUCCESS] Screenshot refreshed successfully
[[07:02:27]] [SUCCESS] Screenshot refreshed
[[07:02:27]] [INFO] Refreshing screenshot...
[[07:02:27]] [INFO] 8uojw2klHA=pass
[[07:02:22]] [INFO] 8uojw2klHA=running
[[07:02:22]] [INFO] Executing action 108/641: iOS Function: text - Text: "env[pwd]"
[[07:02:22]] [SUCCESS] Screenshot refreshed successfully
[[07:02:22]] [SUCCESS] Screenshot refreshed successfully
[[07:02:22]] [SUCCESS] Screenshot refreshed
[[07:02:22]] [INFO] Refreshing screenshot...
[[07:02:22]] [INFO] SHaIduBnay=pass
[[07:02:18]] [INFO] SHaIduBnay=running
[[07:02:18]] [INFO] Executing action 107/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:02:17]] [SUCCESS] Screenshot refreshed successfully
[[07:02:17]] [SUCCESS] Screenshot refreshed successfully
[[07:02:17]] [SUCCESS] Screenshot refreshed
[[07:02:17]] [INFO] Refreshing screenshot...
[[07:02:17]] [INFO] TGoXyeQtB7=pass
[[07:02:13]] [INFO] TGoXyeQtB7=running
[[07:02:13]] [INFO] Executing action 106/641: iOS Function: text - Text: "env[uname]"
[[07:02:12]] [SUCCESS] Screenshot refreshed successfully
[[07:02:12]] [SUCCESS] Screenshot refreshed successfully
[[07:02:12]] [SUCCESS] Screenshot refreshed
[[07:02:12]] [INFO] Refreshing screenshot...
[[07:02:12]] [INFO] rLCI6NVxSc=pass
[[07:02:08]] [INFO] rLCI6NVxSc=running
[[07:02:08]] [INFO] Executing action 105/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:02:08]] [SUCCESS] Screenshot refreshed successfully
[[07:02:08]] [SUCCESS] Screenshot refreshed successfully
[[07:02:08]] [SUCCESS] Screenshot refreshed
[[07:02:08]] [INFO] Refreshing screenshot...
[[07:02:08]] [INFO] 6mHVWI3j5e=pass
[[07:02:04]] [INFO] 6mHVWI3j5e=running
[[07:02:04]] [INFO] Executing action 104/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:02:03]] [SUCCESS] Screenshot refreshed successfully
[[07:02:03]] [SUCCESS] Screenshot refreshed successfully
[[07:02:03]] [SUCCESS] Screenshot refreshed
[[07:02:03]] [INFO] Refreshing screenshot...
[[07:02:03]] [INFO] rJVGLpLWM3=pass
[[07:02:01]] [INFO] rJVGLpLWM3=running
[[07:02:01]] [INFO] Executing action 103/641: iOS Function: alert_accept
[[07:02:00]] [SUCCESS] Screenshot refreshed successfully
[[07:02:00]] [SUCCESS] Screenshot refreshed successfully
[[07:02:00]] [SUCCESS] Screenshot refreshed
[[07:02:00]] [INFO] Refreshing screenshot...
[[07:02:00]] [INFO] WlISsMf9QA=pass
[[07:01:57]] [INFO] WlISsMf9QA=running
[[07:01:57]] [INFO] Executing action 102/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[07:01:56]] [SUCCESS] Screenshot refreshed successfully
[[07:01:56]] [SUCCESS] Screenshot refreshed successfully
[[07:01:56]] [SUCCESS] Screenshot refreshed
[[07:01:56]] [INFO] Refreshing screenshot...
[[07:01:56]] [INFO] IvqPpScAJa=pass
[[07:01:52]] [INFO] IvqPpScAJa=running
[[07:01:52]] [INFO] Executing action 101/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:01:52]] [SUCCESS] Screenshot refreshed successfully
[[07:01:52]] [SUCCESS] Screenshot refreshed successfully
[[07:01:52]] [SUCCESS] Screenshot refreshed
[[07:01:52]] [INFO] Refreshing screenshot...
[[07:01:52]] [INFO] bGo3feCwBQ=pass
[[07:01:48]] [INFO] bGo3feCwBQ=running
[[07:01:48]] [INFO] Executing action 100/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:01:48]] [SUCCESS] Screenshot refreshed successfully
[[07:01:48]] [SUCCESS] Screenshot refreshed successfully
[[07:01:48]] [SUCCESS] Screenshot refreshed
[[07:01:48]] [INFO] Refreshing screenshot...
[[07:01:48]] [INFO] 4WfPFN961S=pass
[[07:01:41]] [INFO] 4WfPFN961S=running
[[07:01:41]] [INFO] Executing action 99/641: Swipe from (50%, 70%) to (50%, 30%)
[[07:01:40]] [SUCCESS] Screenshot refreshed successfully
[[07:01:40]] [SUCCESS] Screenshot refreshed successfully
[[07:01:40]] [SUCCESS] Screenshot refreshed
[[07:01:40]] [INFO] Refreshing screenshot...
[[07:01:40]] [INFO] F0gZF1jEnT=pass
[[07:01:36]] [INFO] F0gZF1jEnT=running
[[07:01:36]] [INFO] Executing action 98/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:01:36]] [SUCCESS] Screenshot refreshed successfully
[[07:01:36]] [SUCCESS] Screenshot refreshed successfully
[[07:01:36]] [SUCCESS] Screenshot refreshed
[[07:01:36]] [INFO] Refreshing screenshot...
[[07:01:36]] [INFO] EDHl0X27Wi=pass
[[07:01:31]] [INFO] EDHl0X27Wi=running
[[07:01:31]] [INFO] Executing action 97/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[07:01:31]] [SUCCESS] Screenshot refreshed successfully
[[07:01:31]] [SUCCESS] Screenshot refreshed successfully
[[07:01:31]] [SUCCESS] Screenshot refreshed
[[07:01:31]] [INFO] Refreshing screenshot...
[[07:01:31]] [INFO] j8NXU87gV3=pass
[[07:01:26]] [INFO] j8NXU87gV3=running
[[07:01:26]] [INFO] Executing action 96/641: iOS Function: text - Text: "env[pwd]"
[[07:01:26]] [SUCCESS] Screenshot refreshed successfully
[[07:01:26]] [SUCCESS] Screenshot refreshed successfully
[[07:01:26]] [SUCCESS] Screenshot refreshed
[[07:01:26]] [INFO] Refreshing screenshot...
[[07:01:26]] [INFO] dpVaKL19uc=pass
[[07:01:22]] [INFO] dpVaKL19uc=running
[[07:01:22]] [INFO] Executing action 95/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:01:22]] [SUCCESS] Screenshot refreshed successfully
[[07:01:22]] [SUCCESS] Screenshot refreshed successfully
[[07:01:21]] [SUCCESS] Screenshot refreshed
[[07:01:21]] [INFO] Refreshing screenshot...
[[07:01:21]] [INFO] eOm1WExcrK=pass
[[07:01:17]] [INFO] eOm1WExcrK=running
[[07:01:17]] [INFO] Executing action 94/641: iOS Function: text - Text: "env[uname]"
[[07:01:16]] [SUCCESS] Screenshot refreshed successfully
[[07:01:16]] [SUCCESS] Screenshot refreshed successfully
[[07:01:16]] [SUCCESS] Screenshot refreshed
[[07:01:16]] [INFO] Refreshing screenshot...
[[07:01:16]] [INFO] 50Z2jrodNd=pass
[[07:01:12]] [INFO] 50Z2jrodNd=running
[[07:01:12]] [INFO] Executing action 93/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:01:12]] [SUCCESS] Screenshot refreshed successfully
[[07:01:12]] [SUCCESS] Screenshot refreshed successfully
[[07:01:12]] [SUCCESS] Screenshot refreshed
[[07:01:12]] [INFO] Refreshing screenshot...
[[07:01:12]] [INFO] eJnHS9n9VL=pass
[[07:01:08]] [INFO] eJnHS9n9VL=running
[[07:01:08]] [INFO] Executing action 92/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:01:07]] [SUCCESS] Screenshot refreshed successfully
[[07:01:07]] [SUCCESS] Screenshot refreshed successfully
[[07:01:07]] [SUCCESS] Screenshot refreshed
[[07:01:07]] [INFO] Refreshing screenshot...
[[07:01:07]] [INFO] XuLgjNG74w=pass
[[07:01:05]] [INFO] XuLgjNG74w=running
[[07:01:05]] [INFO] Executing action 91/641: iOS Function: alert_accept
[[07:01:04]] [SUCCESS] Screenshot refreshed successfully
[[07:01:04]] [SUCCESS] Screenshot refreshed successfully
[[07:01:04]] [SUCCESS] Screenshot refreshed
[[07:01:04]] [INFO] Refreshing screenshot...
[[07:01:04]] [INFO] qA1ap4n1m4=pass
[[07:00:57]] [INFO] qA1ap4n1m4=running
[[07:00:57]] [INFO] Executing action 90/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:00:57]] [SUCCESS] Screenshot refreshed successfully
[[07:00:57]] [SUCCESS] Screenshot refreshed successfully
[[07:00:57]] [SUCCESS] Screenshot refreshed
[[07:00:57]] [INFO] Refreshing screenshot...
[[07:00:57]] [INFO] JXFxYCr98V=pass
[[07:00:44]] [SUCCESS] Screenshot refreshed successfully
[[07:00:44]] [SUCCESS] Screenshot refreshed successfully
[[07:00:43]] [INFO] JXFxYCr98V=running
[[07:00:43]] [INFO] Executing action 89/641: Restart app: env[appid]
[[07:00:43]] [SUCCESS] Screenshot refreshed
[[07:00:43]] [INFO] Refreshing screenshot...
[[07:00:43]] [SUCCESS] Screenshot refreshed successfully
[[07:00:43]] [SUCCESS] Screenshot refreshed successfully
[[07:00:43]] [SUCCESS] Screenshot refreshed
[[07:00:43]] [INFO] Refreshing screenshot...
[[07:00:40]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[07:00:40]] [SUCCESS] Screenshot refreshed successfully
[[07:00:40]] [SUCCESS] Screenshot refreshed successfully
[[07:00:40]] [SUCCESS] Screenshot refreshed
[[07:00:40]] [INFO] Refreshing screenshot...
[[07:00:27]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[07:00:27]] [SUCCESS] Screenshot refreshed successfully
[[07:00:27]] [SUCCESS] Screenshot refreshed successfully
[[07:00:27]] [SUCCESS] Screenshot refreshed
[[07:00:27]] [INFO] Refreshing screenshot...
[[07:00:23]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[07:00:23]] [SUCCESS] Screenshot refreshed successfully
[[07:00:23]] [SUCCESS] Screenshot refreshed successfully
[[07:00:23]] [SUCCESS] Screenshot refreshed
[[07:00:23]] [INFO] Refreshing screenshot...
[[07:00:19]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:00:19]] [SUCCESS] Screenshot refreshed successfully
[[07:00:19]] [SUCCESS] Screenshot refreshed successfully
[[07:00:19]] [SUCCESS] Screenshot refreshed
[[07:00:19]] [INFO] Refreshing screenshot...
[[07:00:12]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[07:00:12]] [SUCCESS] Screenshot refreshed successfully
[[07:00:12]] [SUCCESS] Screenshot refreshed successfully
[[07:00:12]] [SUCCESS] Screenshot refreshed
[[07:00:12]] [INFO] Refreshing screenshot...
[[07:00:05]] [SUCCESS] Screenshot refreshed successfully
[[07:00:05]] [SUCCESS] Screenshot refreshed successfully
[[07:00:05]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[07:00:05]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[07:00:05]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[07:00:05]] [INFO] hbIlJIWlVN=running
[[07:00:05]] [INFO] Executing action 88/641: cleanupSteps action
[[07:00:05]] [SUCCESS] Screenshot refreshed
[[07:00:05]] [INFO] Refreshing screenshot...
[[07:00:05]] [SUCCESS] Screenshot refreshed successfully
[[07:00:05]] [SUCCESS] Screenshot refreshed successfully
[[07:00:05]] [SUCCESS] Screenshot refreshed
[[07:00:05]] [INFO] Refreshing screenshot...
[[07:00:01]] [INFO] Executing Multi Step action step 36/36: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:00:00]] [SUCCESS] Screenshot refreshed successfully
[[07:00:00]] [SUCCESS] Screenshot refreshed successfully
[[07:00:00]] [SUCCESS] Screenshot refreshed
[[07:00:00]] [INFO] Refreshing screenshot...
[[06:59:56]] [INFO] Executing Multi Step action step 35/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[06:59:56]] [SUCCESS] Screenshot refreshed successfully
[[06:59:56]] [SUCCESS] Screenshot refreshed successfully
[[06:59:56]] [SUCCESS] Screenshot refreshed
[[06:59:56]] [INFO] Refreshing screenshot...
[[06:59:43]] [INFO] Executing Multi Step action step 34/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[06:59:43]] [SUCCESS] Screenshot refreshed successfully
[[06:59:43]] [SUCCESS] Screenshot refreshed successfully
[[06:59:43]] [SUCCESS] Screenshot refreshed
[[06:59:43]] [INFO] Refreshing screenshot...
[[06:59:39]] [INFO] Executing Multi Step action step 33/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[06:59:39]] [SUCCESS] Screenshot refreshed successfully
[[06:59:39]] [SUCCESS] Screenshot refreshed successfully
[[06:59:38]] [SUCCESS] Screenshot refreshed
[[06:59:38]] [INFO] Refreshing screenshot...
[[06:59:34]] [INFO] Executing Multi Step action step 32/36: Tap on image: banner-close-updated.png
[[06:59:34]] [SUCCESS] Screenshot refreshed successfully
[[06:59:34]] [SUCCESS] Screenshot refreshed successfully
[[06:59:34]] [SUCCESS] Screenshot refreshed
[[06:59:34]] [INFO] Refreshing screenshot...
[[06:59:24]] [INFO] Executing Multi Step action step 31/36: Swipe from (50%, 70%) to (50%, 30%)
[[06:59:23]] [SUCCESS] Screenshot refreshed successfully
[[06:59:23]] [SUCCESS] Screenshot refreshed successfully
[[06:59:23]] [SUCCESS] Screenshot refreshed
[[06:59:23]] [INFO] Refreshing screenshot...
[[06:59:19]] [INFO] Executing Multi Step action step 30/36: Tap on image: env[delivery-address-img]
[[06:59:19]] [SUCCESS] Screenshot refreshed successfully
[[06:59:19]] [SUCCESS] Screenshot refreshed successfully
[[06:59:19]] [SUCCESS] Screenshot refreshed
[[06:59:19]] [INFO] Refreshing screenshot...
[[06:59:15]] [INFO] Executing Multi Step action step 29/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[06:59:14]] [SUCCESS] Screenshot refreshed successfully
[[06:59:14]] [SUCCESS] Screenshot refreshed successfully
[[06:59:14]] [SUCCESS] Screenshot refreshed
[[06:59:14]] [INFO] Refreshing screenshot...
[[06:59:07]] [INFO] Executing Multi Step action step 28/36: Tap and Type at (54, 314): "305 238 Flinders"
[[06:59:07]] [SUCCESS] Screenshot refreshed successfully
[[06:59:07]] [SUCCESS] Screenshot refreshed successfully
[[06:59:07]] [SUCCESS] Screenshot refreshed
[[06:59:07]] [INFO] Refreshing screenshot...
[[06:59:02]] [INFO] Executing Multi Step action step 27/36: Tap on Text: "address"
[[06:59:02]] [SUCCESS] Screenshot refreshed successfully
[[06:59:02]] [SUCCESS] Screenshot refreshed successfully
[[06:59:02]] [SUCCESS] Screenshot refreshed
[[06:59:02]] [INFO] Refreshing screenshot...
[[06:58:58]] [INFO] Executing Multi Step action step 26/36: iOS Function: text - Text: " "
[[06:58:57]] [SUCCESS] Screenshot refreshed successfully
[[06:58:57]] [SUCCESS] Screenshot refreshed successfully
[[06:58:57]] [SUCCESS] Screenshot refreshed
[[06:58:57]] [INFO] Refreshing screenshot...
[[06:58:53]] [INFO] Executing Multi Step action step 25/36: textClear action
[[06:58:52]] [SUCCESS] Screenshot refreshed successfully
[[06:58:52]] [SUCCESS] Screenshot refreshed successfully
[[06:58:52]] [SUCCESS] Screenshot refreshed
[[06:58:52]] [INFO] Refreshing screenshot...
[[06:58:48]] [INFO] Executing Multi Step action step 24/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[06:58:48]] [SUCCESS] Screenshot refreshed successfully
[[06:58:48]] [SUCCESS] Screenshot refreshed successfully
[[06:58:48]] [SUCCESS] Screenshot refreshed
[[06:58:48]] [INFO] Refreshing screenshot...
[[06:58:43]] [INFO] Executing Multi Step action step 23/36: textClear action
[[06:58:43]] [SUCCESS] Screenshot refreshed successfully
[[06:58:43]] [SUCCESS] Screenshot refreshed successfully
[[06:58:43]] [SUCCESS] Screenshot refreshed
[[06:58:43]] [INFO] Refreshing screenshot...
[[06:58:39]] [INFO] Executing Multi Step action step 22/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[06:58:38]] [SUCCESS] Screenshot refreshed successfully
[[06:58:38]] [SUCCESS] Screenshot refreshed successfully
[[06:58:38]] [SUCCESS] Screenshot refreshed
[[06:58:38]] [INFO] Refreshing screenshot...
[[06:58:34]] [INFO] Executing Multi Step action step 21/36: textClear action
[[06:58:34]] [SUCCESS] Screenshot refreshed successfully
[[06:58:34]] [SUCCESS] Screenshot refreshed successfully
[[06:58:33]] [SUCCESS] Screenshot refreshed
[[06:58:33]] [INFO] Refreshing screenshot...
[[06:58:29]] [INFO] Executing Multi Step action step 20/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[06:58:29]] [SUCCESS] Screenshot refreshed successfully
[[06:58:29]] [SUCCESS] Screenshot refreshed successfully
[[06:58:29]] [SUCCESS] Screenshot refreshed
[[06:58:29]] [INFO] Refreshing screenshot...
[[06:58:24]] [INFO] Executing Multi Step action step 19/36: textClear action
[[06:58:24]] [SUCCESS] Screenshot refreshed successfully
[[06:58:24]] [SUCCESS] Screenshot refreshed successfully
[[06:58:24]] [SUCCESS] Screenshot refreshed
[[06:58:24]] [INFO] Refreshing screenshot...
[[06:58:20]] [INFO] Executing Multi Step action step 18/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[06:58:20]] [SUCCESS] Screenshot refreshed successfully
[[06:58:20]] [SUCCESS] Screenshot refreshed successfully
[[06:58:20]] [SUCCESS] Screenshot refreshed
[[06:58:20]] [INFO] Refreshing screenshot...
[[06:58:16]] [INFO] Executing Multi Step action step 17/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[06:58:15]] [SUCCESS] Screenshot refreshed successfully
[[06:58:15]] [SUCCESS] Screenshot refreshed successfully
[[06:58:15]] [SUCCESS] Screenshot refreshed
[[06:58:15]] [INFO] Refreshing screenshot...
[[06:58:04]] [INFO] Executing Multi Step action step 16/36: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[06:58:04]] [SUCCESS] Screenshot refreshed successfully
[[06:58:04]] [SUCCESS] Screenshot refreshed successfully
[[06:58:04]] [SUCCESS] Screenshot refreshed
[[06:58:04]] [INFO] Refreshing screenshot...
[[06:58:00]] [INFO] Executing Multi Step action step 15/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[06:57:59]] [SUCCESS] Screenshot refreshed successfully
[[06:57:59]] [SUCCESS] Screenshot refreshed successfully
[[06:57:59]] [SUCCESS] Screenshot refreshed
[[06:57:59]] [INFO] Refreshing screenshot...
[[06:57:56]] [INFO] Executing Multi Step action step 14/36: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[06:57:55]] [SUCCESS] Screenshot refreshed successfully
[[06:57:55]] [SUCCESS] Screenshot refreshed successfully
[[06:57:55]] [SUCCESS] Screenshot refreshed
[[06:57:55]] [INFO] Refreshing screenshot...
[[06:57:43]] [INFO] Executing Multi Step action step 13/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[06:57:43]] [SUCCESS] Screenshot refreshed successfully
[[06:57:43]] [SUCCESS] Screenshot refreshed successfully
[[06:57:42]] [SUCCESS] Screenshot refreshed
[[06:57:42]] [INFO] Refreshing screenshot...
[[06:57:39]] [INFO] Executing Multi Step action step 12/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[06:57:38]] [SUCCESS] Screenshot refreshed successfully
[[06:57:38]] [SUCCESS] Screenshot refreshed successfully
[[06:57:38]] [SUCCESS] Screenshot refreshed
[[06:57:38]] [INFO] Refreshing screenshot...
[[06:57:32]] [INFO] Executing Multi Step action step 11/36: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[06:57:31]] [SUCCESS] Screenshot refreshed successfully
[[06:57:31]] [SUCCESS] Screenshot refreshed successfully
[[06:57:31]] [SUCCESS] Screenshot refreshed
[[06:57:31]] [INFO] Refreshing screenshot...
[[06:57:28]] [INFO] Executing Multi Step action step 10/36: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[06:57:27]] [SUCCESS] Screenshot refreshed successfully
[[06:57:27]] [SUCCESS] Screenshot refreshed successfully
[[06:57:27]] [SUCCESS] Screenshot refreshed
[[06:57:27]] [INFO] Refreshing screenshot...
[[06:57:23]] [INFO] Executing Multi Step action step 9/36: iOS Function: text - Text: "Uno card"
[[06:57:23]] [SUCCESS] Screenshot refreshed successfully
[[06:57:23]] [SUCCESS] Screenshot refreshed successfully
[[06:57:23]] [SUCCESS] Screenshot refreshed
[[06:57:23]] [INFO] Refreshing screenshot...
[[06:57:18]] [INFO] Executing Multi Step action step 8/36: Tap on Text: "Find"
[[06:57:17]] [SUCCESS] Screenshot refreshed successfully
[[06:57:17]] [SUCCESS] Screenshot refreshed successfully
[[06:57:17]] [SUCCESS] Screenshot refreshed
[[06:57:17]] [INFO] Refreshing screenshot...
[[06:57:05]] [INFO] Executing Multi Step action step 7/36: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]"
[[06:57:05]] [SUCCESS] Screenshot refreshed successfully
[[06:57:05]] [SUCCESS] Screenshot refreshed successfully
[[06:57:05]] [SUCCESS] Screenshot refreshed
[[06:57:05]] [INFO] Refreshing screenshot...
[[06:56:53]] [INFO] Executing Multi Step action step 6/36: Tap if locator exists: accessibility_id="btnUpdate"
[[06:56:53]] [SUCCESS] Screenshot refreshed successfully
[[06:56:53]] [SUCCESS] Screenshot refreshed successfully
[[06:56:52]] [SUCCESS] Screenshot refreshed
[[06:56:52]] [INFO] Refreshing screenshot...
[[06:56:41]] [INFO] Executing Multi Step action step 5/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[06:56:41]] [SUCCESS] Screenshot refreshed successfully
[[06:56:41]] [SUCCESS] Screenshot refreshed successfully
[[06:56:40]] [SUCCESS] Screenshot refreshed
[[06:56:40]] [INFO] Refreshing screenshot...
[[06:56:36]] [INFO] Executing Multi Step action step 4/36: Tap on Text: "Save"
[[06:56:36]] [SUCCESS] Screenshot refreshed successfully
[[06:56:36]] [SUCCESS] Screenshot refreshed successfully
[[06:56:35]] [SUCCESS] Screenshot refreshed
[[06:56:35]] [INFO] Refreshing screenshot...
[[06:56:30]] [INFO] Executing Multi Step action step 3/36: Tap on element with accessibility_id: btnCurrentLocationButton
[[06:56:30]] [SUCCESS] Screenshot refreshed successfully
[[06:56:30]] [SUCCESS] Screenshot refreshed successfully
[[06:56:29]] [SUCCESS] Screenshot refreshed
[[06:56:29]] [INFO] Refreshing screenshot...
[[06:56:25]] [INFO] Executing Multi Step action step 2/36: Wait till accessibility_id=btnCurrentLocationButton
[[06:56:25]] [SUCCESS] Screenshot refreshed successfully
[[06:56:25]] [SUCCESS] Screenshot refreshed successfully
[[06:56:24]] [SUCCESS] Screenshot refreshed
[[06:56:24]] [INFO] Refreshing screenshot...
[[06:56:18]] [INFO] Executing Multi Step action step 1/36: Tap on Text: "Edit"
[[06:56:18]] [INFO] Loaded 36 steps from test case: Delivery  Buy
[[06:56:18]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[06:56:18]] [INFO] 8ZYdW2lMKv=running
[[06:56:18]] [INFO] Executing action 87/641: Execute Test Case: Delivery  Buy (36 steps)
[[06:56:18]] [SUCCESS] Screenshot refreshed successfully
[[06:56:18]] [SUCCESS] Screenshot refreshed successfully
[[06:56:17]] [SUCCESS] Screenshot refreshed
[[06:56:17]] [INFO] Refreshing screenshot...
[[06:56:17]] [INFO] cKNu2QoRC1=pass
[[06:56:13]] [INFO] cKNu2QoRC1=running
[[06:56:13]] [INFO] Executing action 86/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[06:56:13]] [SUCCESS] Screenshot refreshed successfully
[[06:56:13]] [SUCCESS] Screenshot refreshed successfully
[[06:56:13]] [SUCCESS] Screenshot refreshed
[[06:56:13]] [INFO] Refreshing screenshot...
[[06:56:13]] [INFO] OyUowAaBzD=pass
[[06:56:09]] [INFO] OyUowAaBzD=running
[[06:56:09]] [INFO] Executing action 85/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[06:56:09]] [SUCCESS] Screenshot refreshed successfully
[[06:56:09]] [SUCCESS] Screenshot refreshed successfully
[[06:56:09]] [SUCCESS] Screenshot refreshed
[[06:56:09]] [INFO] Refreshing screenshot...
[[06:56:09]] [INFO] Ob26qqcA0p=pass
[[06:56:02]] [INFO] Ob26qqcA0p=running
[[06:56:02]] [INFO] Executing action 84/641: Swipe from (50%, 70%) to (50%, 30%)
[[06:56:02]] [SUCCESS] Screenshot refreshed successfully
[[06:56:02]] [SUCCESS] Screenshot refreshed successfully
[[06:56:01]] [SUCCESS] Screenshot refreshed
[[06:56:01]] [INFO] Refreshing screenshot...
[[06:56:01]] [INFO] k3mu9Mt7Ec=pass
[[06:55:57]] [INFO] k3mu9Mt7Ec=running
[[06:55:57]] [INFO] Executing action 83/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[06:55:57]] [SUCCESS] Screenshot refreshed successfully
[[06:55:57]] [SUCCESS] Screenshot refreshed successfully
[[06:55:57]] [SUCCESS] Screenshot refreshed
[[06:55:57]] [INFO] Refreshing screenshot...
[[06:55:57]] [INFO] 8umPSX0vrr=pass
[[06:55:53]] [INFO] 8umPSX0vrr=running
[[06:55:53]] [INFO] Executing action 82/641: Tap on image: banner-close-updated.png
[[06:55:53]] [SUCCESS] Screenshot refreshed successfully
[[06:55:53]] [SUCCESS] Screenshot refreshed successfully
[[06:55:53]] [SUCCESS] Screenshot refreshed
[[06:55:53]] [INFO] Refreshing screenshot...
[[06:55:53]] [INFO] pr9o8Zsm5p=pass
[[06:55:49]] [INFO] pr9o8Zsm5p=running
[[06:55:49]] [INFO] Executing action 81/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[06:55:48]] [SUCCESS] Screenshot refreshed successfully
[[06:55:48]] [SUCCESS] Screenshot refreshed successfully
[[06:55:48]] [SUCCESS] Screenshot refreshed
[[06:55:48]] [INFO] Refreshing screenshot...
[[06:55:48]] [INFO] XCynRs6gJ3=pass
[[06:55:40]] [INFO] XCynRs6gJ3=running
[[06:55:40]] [INFO] Executing action 80/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[06:55:40]] [SUCCESS] Screenshot refreshed successfully
[[06:55:40]] [SUCCESS] Screenshot refreshed successfully
[[06:55:40]] [SUCCESS] Screenshot refreshed
[[06:55:40]] [INFO] Refreshing screenshot...
[[06:55:40]] [INFO] UnxZdeLmYu=pass
[[06:55:29]] [INFO] UnxZdeLmYu=running
[[06:55:29]] [INFO] Executing action 79/641: Wait for 10 ms
[[06:55:28]] [SUCCESS] Screenshot refreshed successfully
[[06:55:28]] [SUCCESS] Screenshot refreshed successfully
[[06:55:28]] [SUCCESS] Screenshot refreshed
[[06:55:28]] [INFO] Refreshing screenshot...
[[06:55:28]] [INFO] qjj0i3rcUh=pass
[[06:55:24]] [INFO] qjj0i3rcUh=running
[[06:55:24]] [INFO] Executing action 78/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[06:55:24]] [SUCCESS] Screenshot refreshed successfully
[[06:55:24]] [SUCCESS] Screenshot refreshed successfully
[[06:55:23]] [SUCCESS] Screenshot refreshed
[[06:55:23]] [INFO] Refreshing screenshot...
[[06:55:23]] [INFO] 42Jm6o7r1t=pass
[[06:55:11]] [INFO] 42Jm6o7r1t=running
[[06:55:11]] [INFO] Executing action 77/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[06:55:11]] [SUCCESS] Screenshot refreshed successfully
[[06:55:11]] [SUCCESS] Screenshot refreshed successfully
[[06:55:10]] [SUCCESS] Screenshot refreshed
[[06:55:10]] [INFO] Refreshing screenshot...
[[06:55:10]] [INFO] lWIRxRm6HE=pass
[[06:55:07]] [INFO] lWIRxRm6HE=running
[[06:55:07]] [INFO] Executing action 76/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[06:55:06]] [SUCCESS] Screenshot refreshed successfully
[[06:55:06]] [SUCCESS] Screenshot refreshed successfully
[[06:55:06]] [SUCCESS] Screenshot refreshed
[[06:55:06]] [INFO] Refreshing screenshot...
[[06:55:06]] [INFO] Q0fomJIDoQ=pass
[[06:55:02]] [INFO] Q0fomJIDoQ=running
[[06:55:02]] [INFO] Executing action 75/641: Tap on image: banner-close-updated.png
[[06:55:02]] [SUCCESS] Screenshot refreshed successfully
[[06:55:02]] [SUCCESS] Screenshot refreshed successfully
[[06:55:02]] [SUCCESS] Screenshot refreshed
[[06:55:02]] [INFO] Refreshing screenshot...
[[06:55:02]] [INFO] 7SpDO20tS2=pass
[[06:54:50]] [INFO] 7SpDO20tS2=running
[[06:54:50]] [INFO] Executing action 74/641: Wait for 10 ms
[[06:54:50]] [SUCCESS] Screenshot refreshed successfully
[[06:54:50]] [SUCCESS] Screenshot refreshed successfully
[[06:54:50]] [SUCCESS] Screenshot refreshed
[[06:54:50]] [INFO] Refreshing screenshot...
[[06:54:50]] [INFO] FKZs2qCWoU=pass
[[06:54:46]] [INFO] FKZs2qCWoU=running
[[06:54:46]] [INFO] Executing action 73/641: Tap on Text: "Tarneit"
[[06:54:45]] [SUCCESS] Screenshot refreshed successfully
[[06:54:45]] [SUCCESS] Screenshot refreshed successfully
[[06:54:45]] [SUCCESS] Screenshot refreshed
[[06:54:45]] [INFO] Refreshing screenshot...
[[06:54:45]] [INFO] Qbg9bipTGs=pass
[[06:54:41]] [INFO] Qbg9bipTGs=running
[[06:54:41]] [INFO] Executing action 72/641: Swipe from (50%, 70%) to (50%, 30%)
[[06:54:40]] [SUCCESS] Screenshot refreshed successfully
[[06:54:40]] [SUCCESS] Screenshot refreshed successfully
[[06:54:40]] [SUCCESS] Screenshot refreshed
[[06:54:40]] [INFO] Refreshing screenshot...
[[06:54:40]] [INFO] qjj0i3rcUh=pass
[[06:54:36]] [INFO] qjj0i3rcUh=running
[[06:54:36]] [INFO] Executing action 71/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[06:54:36]] [SUCCESS] Screenshot refreshed successfully
[[06:54:36]] [SUCCESS] Screenshot refreshed successfully
[[06:54:36]] [SUCCESS] Screenshot refreshed
[[06:54:36]] [INFO] Refreshing screenshot...
[[06:54:36]] [INFO] uM5FOSrU5U=pass
[[06:54:33]] [INFO] uM5FOSrU5U=running
[[06:54:33]] [INFO] Executing action 70/641: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[06:54:32]] [SUCCESS] Screenshot refreshed successfully
[[06:54:32]] [SUCCESS] Screenshot refreshed successfully
[[06:54:32]] [SUCCESS] Screenshot refreshed
[[06:54:32]] [INFO] Refreshing screenshot...
[[06:54:32]] [INFO] QB2bKb0SsP=pass
[[06:54:20]] [INFO] QB2bKb0SsP=running
[[06:54:20]] [INFO] Executing action 69/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[06:54:19]] [SUCCESS] Screenshot refreshed successfully
[[06:54:19]] [SUCCESS] Screenshot refreshed successfully
[[06:54:19]] [SUCCESS] Screenshot refreshed
[[06:54:19]] [INFO] Refreshing screenshot...
[[06:54:19]] [INFO] F1olhgKhUt=pass
[[06:54:15]] [INFO] F1olhgKhUt=running
[[06:54:15]] [INFO] Executing action 68/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[06:54:15]] [SUCCESS] Screenshot refreshed successfully
[[06:54:15]] [SUCCESS] Screenshot refreshed successfully
[[06:54:15]] [SUCCESS] Screenshot refreshed
[[06:54:15]] [INFO] Refreshing screenshot...
[[06:54:15]] [INFO] jY0oPjKbuS=pass
[[06:54:12]] [INFO] jY0oPjKbuS=running
[[06:54:12]] [INFO] Executing action 67/641: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[06:54:12]] [SUCCESS] Screenshot refreshed successfully
[[06:54:12]] [SUCCESS] Screenshot refreshed successfully
[[06:54:11]] [SUCCESS] Screenshot refreshed
[[06:54:11]] [INFO] Refreshing screenshot...
[[06:54:11]] [INFO] FnrbyHq7bU=pass
[[06:54:05]] [INFO] FnrbyHq7bU=running
[[06:54:05]] [INFO] Executing action 66/641: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[06:54:05]] [SUCCESS] Screenshot refreshed successfully
[[06:54:05]] [SUCCESS] Screenshot refreshed successfully
[[06:54:04]] [SUCCESS] Screenshot refreshed
[[06:54:04]] [INFO] Refreshing screenshot...
[[06:54:04]] [INFO] nAB6Q8LAdv=pass
[[06:54:01]] [INFO] nAB6Q8LAdv=running
[[06:54:01]] [INFO] Executing action 65/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[06:54:01]] [SUCCESS] Screenshot refreshed successfully
[[06:54:01]] [SUCCESS] Screenshot refreshed successfully
[[06:54:00]] [SUCCESS] Screenshot refreshed
[[06:54:00]] [INFO] Refreshing screenshot...
[[06:54:00]] [INFO] sc2KH9bG6H=pass
[[06:53:57]] [INFO] sc2KH9bG6H=running
[[06:53:57]] [INFO] Executing action 64/641: iOS Function: text - Text: "Uno card"
[[06:53:56]] [SUCCESS] Screenshot refreshed successfully
[[06:53:56]] [SUCCESS] Screenshot refreshed successfully
[[06:53:56]] [SUCCESS] Screenshot refreshed
[[06:53:56]] [INFO] Refreshing screenshot...
[[06:53:56]] [INFO] ZBXCQNlT8z=pass
[[06:53:51]] [INFO] ZBXCQNlT8z=running
[[06:53:51]] [INFO] Executing action 63/641: Tap on Text: "Find"
[[06:53:51]] [SUCCESS] Screenshot refreshed successfully
[[06:53:51]] [SUCCESS] Screenshot refreshed successfully
[[06:53:51]] [SUCCESS] Screenshot refreshed
[[06:53:51]] [INFO] Refreshing screenshot...
[[06:53:51]] [INFO] HYl6Z7Gvqz=pass
[[06:53:37]] [SUCCESS] Screenshot refreshed successfully
[[06:53:37]] [SUCCESS] Screenshot refreshed successfully
[[06:53:37]] [INFO] HYl6Z7Gvqz=running
[[06:53:37]] [INFO] Executing action 62/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[06:53:37]] [SUCCESS] Screenshot refreshed
[[06:53:37]] [INFO] Refreshing screenshot...
[[06:53:37]] [SUCCESS] Screenshot refreshed successfully
[[06:53:37]] [SUCCESS] Screenshot refreshed successfully
[[06:53:36]] [SUCCESS] Screenshot refreshed
[[06:53:36]] [INFO] Refreshing screenshot...
[[06:53:32]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[06:53:32]] [SUCCESS] Screenshot refreshed successfully
[[06:53:32]] [SUCCESS] Screenshot refreshed successfully
[[06:53:31]] [SUCCESS] Screenshot refreshed
[[06:53:31]] [INFO] Refreshing screenshot...
[[06:53:28]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[06:53:27]] [SUCCESS] Screenshot refreshed successfully
[[06:53:27]] [SUCCESS] Screenshot refreshed successfully
[[06:53:27]] [SUCCESS] Screenshot refreshed
[[06:53:27]] [INFO] Refreshing screenshot...
[[06:53:23]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[06:53:22]] [SUCCESS] Screenshot refreshed successfully
[[06:53:22]] [SUCCESS] Screenshot refreshed successfully
[[06:53:22]] [SUCCESS] Screenshot refreshed
[[06:53:22]] [INFO] Refreshing screenshot...
[[06:53:18]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[06:53:18]] [SUCCESS] Screenshot refreshed successfully
[[06:53:18]] [SUCCESS] Screenshot refreshed successfully
[[06:53:18]] [SUCCESS] Screenshot refreshed
[[06:53:18]] [INFO] Refreshing screenshot...
[[06:53:12]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[06:53:12]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[06:53:12]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[06:53:12]] [INFO] El6k4IPZly=running
[[06:53:12]] [INFO] Executing action 61/641: Execute Test Case: Kmart-Signin (8 steps)
[[06:53:12]] [SUCCESS] Screenshot refreshed successfully
[[06:53:12]] [SUCCESS] Screenshot refreshed successfully
[[06:53:12]] [SUCCESS] Screenshot refreshed
[[06:53:12]] [INFO] Refreshing screenshot...
[[06:53:12]] [INFO] 3caMBvQX7k=pass
[[06:53:08]] [INFO] 3caMBvQX7k=running
[[06:53:08]] [INFO] Executing action 60/641: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[06:53:08]] [SUCCESS] Screenshot refreshed successfully
[[06:53:08]] [SUCCESS] Screenshot refreshed successfully
[[06:53:08]] [SUCCESS] Screenshot refreshed
[[06:53:08]] [INFO] Refreshing screenshot...
[[06:53:08]] [INFO] yUJyVO5Wev=pass
[[06:53:05]] [INFO] yUJyVO5Wev=running
[[06:53:05]] [INFO] Executing action 59/641: iOS Function: alert_accept
[[06:53:05]] [SUCCESS] Screenshot refreshed successfully
[[06:53:05]] [SUCCESS] Screenshot refreshed successfully
[[06:53:05]] [SUCCESS] Screenshot refreshed
[[06:53:05]] [INFO] Refreshing screenshot...
[[06:53:05]] [INFO] rkL0oz4kiL=pass
[[06:52:58]] [INFO] rkL0oz4kiL=running
[[06:52:58]] [INFO] Executing action 58/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[06:52:58]] [SUCCESS] Screenshot refreshed successfully
[[06:52:58]] [SUCCESS] Screenshot refreshed successfully
[[06:52:57]] [SUCCESS] Screenshot refreshed
[[06:52:57]] [INFO] Refreshing screenshot...
[[06:52:57]] [INFO] HotUJOd6oB=pass
[[06:52:44]] [SUCCESS] Screenshot refreshed successfully
[[06:52:44]] [SUCCESS] Screenshot refreshed successfully
[[06:52:44]] [INFO] HotUJOd6oB=running
[[06:52:44]] [INFO] Executing action 57/641: Restart app: env[appid]
[[06:52:44]] [SUCCESS] Screenshot refreshed
[[06:52:44]] [INFO] Refreshing screenshot...
[[06:52:44]] [SUCCESS] Screenshot refreshed successfully
[[06:52:44]] [SUCCESS] Screenshot refreshed successfully
[[06:52:43]] [SUCCESS] Screenshot refreshed
[[06:52:43]] [INFO] Refreshing screenshot...
[[06:52:41]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[06:52:41]] [SUCCESS] Screenshot refreshed successfully
[[06:52:41]] [SUCCESS] Screenshot refreshed successfully
[[06:52:41]] [SUCCESS] Screenshot refreshed
[[06:52:41]] [INFO] Refreshing screenshot...
[[06:52:28]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[06:52:28]] [SUCCESS] Screenshot refreshed successfully
[[06:52:28]] [SUCCESS] Screenshot refreshed successfully
[[06:52:28]] [SUCCESS] Screenshot refreshed
[[06:52:28]] [INFO] Refreshing screenshot...
[[06:52:24]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[06:52:24]] [SUCCESS] Screenshot refreshed successfully
[[06:52:24]] [SUCCESS] Screenshot refreshed successfully
[[06:52:24]] [SUCCESS] Screenshot refreshed
[[06:52:24]] [INFO] Refreshing screenshot...
[[06:52:20]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[06:52:20]] [SUCCESS] Screenshot refreshed successfully
[[06:52:20]] [SUCCESS] Screenshot refreshed successfully
[[06:52:19]] [SUCCESS] Screenshot refreshed
[[06:52:19]] [INFO] Refreshing screenshot...
[[06:52:13]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[06:52:13]] [SUCCESS] Screenshot refreshed successfully
[[06:52:13]] [SUCCESS] Screenshot refreshed successfully
[[06:52:12]] [SUCCESS] Screenshot refreshed
[[06:52:12]] [INFO] Refreshing screenshot...
[[06:52:07]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[06:52:07]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[06:52:07]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[06:52:07]] [INFO] vKo6Ox3YrP=running
[[06:52:07]] [INFO] Executing action 56/641: cleanupSteps action
[[06:52:07]] [SUCCESS] Screenshot refreshed successfully
[[06:52:07]] [SUCCESS] Screenshot refreshed successfully
[[06:52:06]] [SUCCESS] Screenshot refreshed
[[06:52:06]] [INFO] Refreshing screenshot...
[[06:52:06]] [INFO] x4yLCZHaCR=pass
[[06:52:04]] [INFO] x4yLCZHaCR=running
[[06:52:04]] [INFO] Executing action 55/641: Terminate app: env[appid]
[[06:52:04]] [SUCCESS] Screenshot refreshed successfully
[[06:52:04]] [SUCCESS] Screenshot refreshed successfully
[[06:52:03]] [SUCCESS] Screenshot refreshed
[[06:52:03]] [INFO] Refreshing screenshot...
[[06:52:03]] [INFO] 2p13JoJbbA=pass
[[06:51:59]] [INFO] 2p13JoJbbA=running
[[06:51:59]] [INFO] Executing action 54/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[06:51:59]] [SUCCESS] Screenshot refreshed successfully
[[06:51:59]] [SUCCESS] Screenshot refreshed successfully
[[06:51:59]] [SUCCESS] Screenshot refreshed
[[06:51:59]] [INFO] Refreshing screenshot...
[[06:51:59]] [INFO] 2p13JoJbbA=pass
[[06:51:55]] [INFO] 2p13JoJbbA=running
[[06:51:55]] [INFO] Executing action 53/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[06:51:55]] [SUCCESS] Screenshot refreshed successfully
[[06:51:55]] [SUCCESS] Screenshot refreshed successfully
[[06:51:54]] [SUCCESS] Screenshot refreshed
[[06:51:54]] [INFO] Refreshing screenshot...
[[06:51:54]] [INFO] nyBidG0kHp=pass
[[06:51:47]] [INFO] nyBidG0kHp=running
[[06:51:47]] [INFO] Executing action 52/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[06:51:47]] [SUCCESS] Screenshot refreshed successfully
[[06:51:47]] [SUCCESS] Screenshot refreshed successfully
[[06:51:47]] [SUCCESS] Screenshot refreshed
[[06:51:47]] [INFO] Refreshing screenshot...
[[06:51:47]] [INFO] w7I4F66YKQ=pass
[[06:51:35]] [INFO] w7I4F66YKQ=running
[[06:51:35]] [INFO] Executing action 51/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[06:51:34]] [SUCCESS] Screenshot refreshed successfully
[[06:51:34]] [SUCCESS] Screenshot refreshed successfully
[[06:51:34]] [SUCCESS] Screenshot refreshed
[[06:51:34]] [INFO] Refreshing screenshot...
[[06:51:34]] [INFO] F4NGh9HrLw=pass
[[06:51:29]] [INFO] F4NGh9HrLw=running
[[06:51:29]] [INFO] Executing action 50/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[06:51:29]] [SUCCESS] Screenshot refreshed successfully
[[06:51:29]] [SUCCESS] Screenshot refreshed successfully
[[06:51:29]] [SUCCESS] Screenshot refreshed
[[06:51:29]] [INFO] Refreshing screenshot...
[[06:51:29]] [INFO] VtMfqK1V9t=pass
[[06:51:11]] [INFO] VtMfqK1V9t=running
[[06:51:11]] [INFO] Executing action 49/641: Tap on element with accessibility_id: Add to bag
[[06:51:11]] [SUCCESS] Screenshot refreshed successfully
[[06:51:11]] [SUCCESS] Screenshot refreshed successfully
[[06:51:11]] [SUCCESS] Screenshot refreshed
[[06:51:11]] [INFO] Refreshing screenshot...
[[06:51:11]] [INFO] NOnuFzXy63=pass
[[06:51:07]] [INFO] NOnuFzXy63=running
[[06:51:07]] [INFO] Executing action 48/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[06:51:07]] [SUCCESS] Screenshot refreshed successfully
[[06:51:07]] [SUCCESS] Screenshot refreshed successfully
[[06:51:06]] [SUCCESS] Screenshot refreshed
[[06:51:06]] [INFO] Refreshing screenshot...
[[06:51:06]] [INFO] kz9lnCdwoH=pass
[[06:51:02]] [INFO] kz9lnCdwoH=running
[[06:51:02]] [INFO] Executing action 47/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[06:51:02]] [SUCCESS] Screenshot refreshed successfully
[[06:51:02]] [SUCCESS] Screenshot refreshed successfully
[[06:51:02]] [SUCCESS] Screenshot refreshed
[[06:51:02]] [INFO] Refreshing screenshot...
[[06:51:02]] [INFO] kz9lnCdwoH=pass
[[06:50:58]] [INFO] kz9lnCdwoH=running
[[06:50:58]] [INFO] Executing action 46/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[06:50:58]] [SUCCESS] Screenshot refreshed successfully
[[06:50:58]] [SUCCESS] Screenshot refreshed successfully
[[06:50:58]] [SUCCESS] Screenshot refreshed
[[06:50:58]] [INFO] Refreshing screenshot...
[[06:50:58]] [INFO] qIF9CVPc56=pass
[[06:50:54]] [INFO] qIF9CVPc56=running
[[06:50:54]] [INFO] Executing action 45/641: iOS Function: text - Text: "mat"
[[06:50:54]] [SUCCESS] Screenshot refreshed successfully
[[06:50:54]] [SUCCESS] Screenshot refreshed successfully
[[06:50:54]] [SUCCESS] Screenshot refreshed
[[06:50:54]] [INFO] Refreshing screenshot...
[[06:50:54]] [INFO] yEga5MkcRe=pass
[[06:50:50]] [INFO] yEga5MkcRe=running
[[06:50:50]] [INFO] Executing action 44/641: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[06:50:50]] [SUCCESS] Screenshot refreshed successfully
[[06:50:50]] [SUCCESS] Screenshot refreshed successfully
[[06:50:49]] [SUCCESS] Screenshot refreshed
[[06:50:49]] [INFO] Refreshing screenshot...
[[06:50:49]] [INFO] F4NGh9HrLw=pass
[[06:50:46]] [INFO] F4NGh9HrLw=running
[[06:50:46]] [INFO] Executing action 43/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[06:50:45]] [SUCCESS] Screenshot refreshed successfully
[[06:50:45]] [SUCCESS] Screenshot refreshed successfully
[[06:50:45]] [SUCCESS] Screenshot refreshed
[[06:50:45]] [INFO] Refreshing screenshot...
[[06:50:45]] [INFO] kz9lnCdwoH=pass
[[06:50:41]] [INFO] kz9lnCdwoH=running
[[06:50:41]] [INFO] Executing action 42/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[06:50:41]] [SUCCESS] Screenshot refreshed successfully
[[06:50:41]] [SUCCESS] Screenshot refreshed successfully
[[06:50:40]] [SUCCESS] Screenshot refreshed
[[06:50:40]] [INFO] Refreshing screenshot...
[[06:50:40]] [INFO] kz9lnCdwoH=pass
[[06:50:37]] [INFO] kz9lnCdwoH=running
[[06:50:37]] [INFO] Executing action 41/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[06:50:37]] [SUCCESS] Screenshot refreshed successfully
[[06:50:37]] [SUCCESS] Screenshot refreshed successfully
[[06:50:36]] [SUCCESS] Screenshot refreshed
[[06:50:36]] [INFO] Refreshing screenshot...
[[06:50:36]] [INFO] JRheDTvpJf=pass
[[06:50:33]] [INFO] JRheDTvpJf=running
[[06:50:33]] [INFO] Executing action 40/641: iOS Function: text - Text: "Kid toy"
[[06:50:32]] [SUCCESS] Screenshot refreshed successfully
[[06:50:32]] [SUCCESS] Screenshot refreshed successfully
[[06:50:32]] [SUCCESS] Screenshot refreshed
[[06:50:32]] [INFO] Refreshing screenshot...
[[06:50:32]] [INFO] yEga5MkcRe=pass
[[06:50:29]] [INFO] yEga5MkcRe=running
[[06:50:29]] [INFO] Executing action 39/641: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[06:50:28]] [SUCCESS] Screenshot refreshed successfully
[[06:50:28]] [SUCCESS] Screenshot refreshed successfully
[[06:50:28]] [SUCCESS] Screenshot refreshed
[[06:50:28]] [INFO] Refreshing screenshot...
[[06:50:28]] [INFO] F4NGh9HrLw=pass
[[06:50:25]] [INFO] F4NGh9HrLw=running
[[06:50:25]] [INFO] Executing action 38/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[06:50:24]] [SUCCESS] Screenshot refreshed successfully
[[06:50:24]] [SUCCESS] Screenshot refreshed successfully
[[06:50:24]] [SUCCESS] Screenshot refreshed
[[06:50:24]] [INFO] Refreshing screenshot...
[[06:50:24]] [INFO] XPEr3w6Zof=pass
[[06:50:19]] [INFO] XPEr3w6Zof=running
[[06:50:19]] [INFO] Executing action 37/641: Restart app: env[appid]
[[06:50:19]] [SUCCESS] Screenshot refreshed successfully
[[06:50:19]] [SUCCESS] Screenshot refreshed successfully
[[06:50:19]] [SUCCESS] Screenshot refreshed
[[06:50:19]] [INFO] Refreshing screenshot...
[[06:50:19]] [INFO] PiQRBWBe3E=pass
[[06:50:15]] [INFO] PiQRBWBe3E=running
[[06:50:15]] [INFO] Executing action 36/641: Tap on image: env[device-back-img]
[[06:50:15]] [SUCCESS] Screenshot refreshed successfully
[[06:50:15]] [SUCCESS] Screenshot refreshed successfully
[[06:50:15]] [SUCCESS] Screenshot refreshed
[[06:50:15]] [INFO] Refreshing screenshot...
[[06:50:15]] [INFO] GWoppouz1l=pass
[[06:50:12]] [INFO] GWoppouz1l=running
[[06:50:12]] [INFO] Executing action 35/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[06:50:12]] [SUCCESS] Screenshot refreshed successfully
[[06:50:12]] [SUCCESS] Screenshot refreshed successfully
[[06:50:12]] [SUCCESS] Screenshot refreshed
[[06:50:12]] [INFO] Refreshing screenshot...
[[06:50:12]] [INFO] B6GDXWAmWp=pass
[[06:50:07]] [INFO] B6GDXWAmWp=running
[[06:50:07]] [INFO] Executing action 34/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[06:50:07]] [SUCCESS] Screenshot refreshed successfully
[[06:50:07]] [SUCCESS] Screenshot refreshed successfully
[[06:50:07]] [SUCCESS] Screenshot refreshed
[[06:50:07]] [INFO] Refreshing screenshot...
[[06:50:07]] [INFO] mtYqeDttRc=pass
[[06:50:03]] [INFO] mtYqeDttRc=running
[[06:50:03]] [INFO] Executing action 33/641: Tap on image: env[paypal-close-img]
[[06:50:03]] [SUCCESS] Screenshot refreshed successfully
[[06:50:03]] [SUCCESS] Screenshot refreshed successfully
[[06:50:02]] [SUCCESS] Screenshot refreshed
[[06:50:02]] [INFO] Refreshing screenshot...
[[06:50:02]] [INFO] q6cKxgMAIn=pass
[[06:49:55]] [INFO] q6cKxgMAIn=running
[[06:49:55]] [INFO] Executing action 32/641: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[06:49:55]] [SUCCESS] Screenshot refreshed successfully
[[06:49:55]] [SUCCESS] Screenshot refreshed successfully
[[06:49:55]] [SUCCESS] Screenshot refreshed
[[06:49:55]] [INFO] Refreshing screenshot...
[[06:49:55]] [INFO] KRQDBv2D3A=pass
[[06:49:51]] [INFO] KRQDBv2D3A=running
[[06:49:51]] [INFO] Executing action 31/641: Tap on image: env[device-back-img]
[[06:49:51]] [SUCCESS] Screenshot refreshed successfully
[[06:49:51]] [SUCCESS] Screenshot refreshed successfully
[[06:49:51]] [SUCCESS] Screenshot refreshed
[[06:49:51]] [INFO] Refreshing screenshot...
[[06:49:51]] [INFO] P4b2BITpCf=pass
[[06:49:48]] [INFO] P4b2BITpCf=running
[[06:49:48]] [INFO] Executing action 30/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[06:49:48]] [SUCCESS] Screenshot refreshed successfully
[[06:49:48]] [SUCCESS] Screenshot refreshed successfully
[[06:49:47]] [SUCCESS] Screenshot refreshed
[[06:49:47]] [INFO] Refreshing screenshot...
[[06:49:47]] [INFO] inrxgdWzXr=pass
[[06:49:41]] [INFO] inrxgdWzXr=running
[[06:49:41]] [INFO] Executing action 29/641: Tap on element with accessibility_id: Learn more about Zip
[[06:49:40]] [SUCCESS] Screenshot refreshed successfully
[[06:49:40]] [SUCCESS] Screenshot refreshed successfully
[[06:49:40]] [SUCCESS] Screenshot refreshed
[[06:49:40]] [INFO] Refreshing screenshot...
[[06:49:40]] [INFO] Et3kvnFdxh=pass
[[06:49:37]] [INFO] Et3kvnFdxh=running
[[06:49:37]] [INFO] Executing action 28/641: Tap on image: env[device-back-img]
[[06:49:37]] [INFO] Skipping disabled action 27/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[06:49:36]] [SUCCESS] Screenshot refreshed successfully
[[06:49:36]] [SUCCESS] Screenshot refreshed successfully
[[06:49:36]] [SUCCESS] Screenshot refreshed
[[06:49:36]] [INFO] Refreshing screenshot...
[[06:49:36]] [INFO] pk2DLZFBmx=pass
[[06:49:29]] [INFO] pk2DLZFBmx=running
[[06:49:29]] [INFO] Executing action 26/641: Tap on element with accessibility_id: Learn more about AfterPay
[[06:49:29]] [SUCCESS] Screenshot refreshed successfully
[[06:49:29]] [SUCCESS] Screenshot refreshed successfully
[[06:49:29]] [SUCCESS] Screenshot refreshed
[[06:49:29]] [INFO] Refreshing screenshot...
[[06:49:29]] [INFO] ShJSdXvmVL=pass
[[06:49:20]] [INFO] ShJSdXvmVL=running
[[06:49:20]] [INFO] Executing action 25/641: Swipe up till element accessibility_id: "Learn more about AfterPay" is visible
[[06:49:19]] [SUCCESS] Screenshot refreshed successfully
[[06:49:19]] [SUCCESS] Screenshot refreshed successfully
[[06:49:19]] [SUCCESS] Screenshot refreshed
[[06:49:19]] [INFO] Refreshing screenshot...
[[06:49:19]] [INFO] eShagNJmzI=pass
[[06:49:15]] [INFO] eShagNJmzI=running
[[06:49:15]] [INFO] Executing action 24/641: Wait for 3 ms
[[06:49:15]] [SUCCESS] Screenshot refreshed successfully
[[06:49:15]] [SUCCESS] Screenshot refreshed successfully
[[06:49:14]] [SUCCESS] Screenshot refreshed
[[06:49:14]] [INFO] Refreshing screenshot...
[[06:49:14]] [INFO] sHQtYzpI4s=pass
[[06:49:10]] [INFO] sHQtYzpI4s=running
[[06:49:10]] [INFO] Executing action 23/641: Tap on image: env[closebtnimage]
[[06:49:10]] [SUCCESS] Screenshot refreshed successfully
[[06:49:10]] [SUCCESS] Screenshot refreshed successfully
[[06:49:10]] [SUCCESS] Screenshot refreshed
[[06:49:10]] [INFO] Refreshing screenshot...
[[06:49:10]] [INFO] 83tV9A4NOn=pass
[[06:49:06]] [INFO] 83tV9A4NOn=running
[[06:49:06]] [INFO] Executing action 22/641: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[06:49:06]] [SUCCESS] Screenshot refreshed successfully
[[06:49:06]] [SUCCESS] Screenshot refreshed successfully
[[06:49:05]] [SUCCESS] Screenshot refreshed
[[06:49:05]] [INFO] Refreshing screenshot...
[[06:49:05]] [INFO] dCqKBG3e7u=pass
[[06:49:02]] [INFO] dCqKBG3e7u=running
[[06:49:02]] [INFO] Executing action 21/641: Tap on image: env[product-share-img]
[[06:49:01]] [SUCCESS] Screenshot refreshed successfully
[[06:49:01]] [SUCCESS] Screenshot refreshed successfully
[[06:49:01]] [SUCCESS] Screenshot refreshed
[[06:49:01]] [INFO] Refreshing screenshot...
[[06:49:01]] [INFO] kAQ1yIIw3h=pass
[[06:48:57]] [INFO] kAQ1yIIw3h=running
[[06:48:57]] [INFO] Executing action 20/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[06:48:57]] [SUCCESS] Screenshot refreshed successfully
[[06:48:57]] [SUCCESS] Screenshot refreshed successfully
[[06:48:57]] [SUCCESS] Screenshot refreshed
[[06:48:57]] [INFO] Refreshing screenshot...
[[06:48:57]] [INFO] OmKfD9iBjD=pass
[[06:48:53]] [INFO] OmKfD9iBjD=running
[[06:48:53]] [INFO] Executing action 19/641: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[06:48:53]] [SUCCESS] Screenshot refreshed successfully
[[06:48:53]] [SUCCESS] Screenshot refreshed successfully
[[06:48:52]] [SUCCESS] Screenshot refreshed
[[06:48:52]] [INFO] Refreshing screenshot...
[[06:48:52]] [INFO] dMl1PH9Dlc=pass
[[06:48:41]] [INFO] dMl1PH9Dlc=running
[[06:48:41]] [INFO] Executing action 18/641: Wait for 10 ms
[[06:48:41]] [SUCCESS] Screenshot refreshed successfully
[[06:48:41]] [SUCCESS] Screenshot refreshed successfully
[[06:48:41]] [SUCCESS] Screenshot refreshed
[[06:48:41]] [INFO] Refreshing screenshot...
[[06:48:41]] [INFO] eHLWiRoqqS=pass
[[06:48:36]] [INFO] eHLWiRoqqS=running
[[06:48:36]] [INFO] Executing action 17/641: Swipe from (50%, 70%) to (50%, 30%)
[[06:48:36]] [SUCCESS] Screenshot refreshed successfully
[[06:48:36]] [SUCCESS] Screenshot refreshed successfully
[[06:48:36]] [SUCCESS] Screenshot refreshed
[[06:48:36]] [INFO] Refreshing screenshot...
[[06:48:36]] [INFO] huUnpMMjVR=pass
[[06:48:32]] [INFO] huUnpMMjVR=running
[[06:48:32]] [INFO] Executing action 16/641: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[06:48:31]] [SUCCESS] Screenshot refreshed successfully
[[06:48:31]] [SUCCESS] Screenshot refreshed successfully
[[06:48:31]] [SUCCESS] Screenshot refreshed
[[06:48:31]] [INFO] Refreshing screenshot...
[[06:48:31]] [INFO] XmAxcBtFI0=pass
[[06:48:28]] [INFO] XmAxcBtFI0=running
[[06:48:28]] [INFO] Executing action 15/641: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[06:48:28]] [SUCCESS] Screenshot refreshed successfully
[[06:48:28]] [SUCCESS] Screenshot refreshed successfully
[[06:48:28]] [SUCCESS] Screenshot refreshed
[[06:48:28]] [INFO] Refreshing screenshot...
[[06:48:28]] [INFO] ktAufkDJnF=pass
[[06:48:24]] [INFO] ktAufkDJnF=running
[[06:48:24]] [INFO] Executing action 14/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[06:48:24]] [SUCCESS] Screenshot refreshed successfully
[[06:48:24]] [SUCCESS] Screenshot refreshed successfully
[[06:48:23]] [SUCCESS] Screenshot refreshed
[[06:48:23]] [INFO] Refreshing screenshot...
[[06:48:23]] [INFO] dMl1PH9Dlc=pass
[[06:48:17]] [INFO] dMl1PH9Dlc=running
[[06:48:17]] [INFO] Executing action 13/641: Wait for 5 ms
[[06:48:17]] [SUCCESS] Screenshot refreshed successfully
[[06:48:17]] [SUCCESS] Screenshot refreshed successfully
[[06:48:17]] [SUCCESS] Screenshot refreshed
[[06:48:17]] [INFO] Refreshing screenshot...
[[06:48:17]] [INFO] a50JhCx0ir=pass
[[06:48:13]] [INFO] a50JhCx0ir=running
[[06:48:13]] [INFO] Executing action 12/641: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[06:48:13]] [SUCCESS] Screenshot refreshed successfully
[[06:48:13]] [SUCCESS] Screenshot refreshed successfully
[[06:48:13]] [SUCCESS] Screenshot refreshed
[[06:48:13]] [INFO] Refreshing screenshot...
[[06:48:13]] [INFO] Y1O1clhMSJ=pass
[[06:48:09]] [INFO] Y1O1clhMSJ=running
[[06:48:09]] [INFO] Executing action 11/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[06:48:08]] [SUCCESS] Screenshot refreshed successfully
[[06:48:08]] [SUCCESS] Screenshot refreshed successfully
[[06:48:08]] [SUCCESS] Screenshot refreshed
[[06:48:08]] [INFO] Refreshing screenshot...
[[06:48:08]] [INFO] lYPskZt0Ya=pass
[[06:48:05]] [INFO] lYPskZt0Ya=running
[[06:48:05]] [INFO] Executing action 10/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[06:48:04]] [SUCCESS] Screenshot refreshed successfully
[[06:48:04]] [SUCCESS] Screenshot refreshed successfully
[[06:48:04]] [SUCCESS] Screenshot refreshed
[[06:48:04]] [INFO] Refreshing screenshot...
[[06:48:04]] [INFO] xUbWFa8Ok2=pass
[[06:48:00]] [INFO] xUbWFa8Ok2=running
[[06:48:00]] [INFO] Executing action 9/641: Tap on Text: "Latest"
[[06:48:00]] [SUCCESS] Screenshot refreshed successfully
[[06:48:00]] [SUCCESS] Screenshot refreshed successfully
[[06:48:00]] [SUCCESS] Screenshot refreshed
[[06:48:00]] [INFO] Refreshing screenshot...
[[06:48:00]] [INFO] RbNtEW6N9T=pass
[[06:47:56]] [INFO] RbNtEW6N9T=running
[[06:47:56]] [INFO] Executing action 8/641: Tap on Text: "Toys"
[[06:47:55]] [SUCCESS] Screenshot refreshed successfully
[[06:47:55]] [SUCCESS] Screenshot refreshed successfully
[[06:47:55]] [SUCCESS] Screenshot refreshed
[[06:47:55]] [INFO] Refreshing screenshot...
[[06:47:55]] [INFO] ltDXyWvtEz=pass
[[06:47:52]] [INFO] ltDXyWvtEz=running
[[06:47:52]] [INFO] Executing action 7/641: Tap on image: env[device-back-img]
[[06:47:51]] [SUCCESS] Screenshot refreshed successfully
[[06:47:51]] [SUCCESS] Screenshot refreshed successfully
[[06:47:51]] [SUCCESS] Screenshot refreshed
[[06:47:51]] [INFO] Refreshing screenshot...
[[06:47:51]] [INFO] QPKR6jUF9O=pass
[[06:47:48]] [INFO] QPKR6jUF9O=running
[[06:47:48]] [INFO] Executing action 6/641: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[06:47:48]] [SUCCESS] Screenshot refreshed successfully
[[06:47:48]] [SUCCESS] Screenshot refreshed successfully
[[06:47:48]] [SUCCESS] Screenshot refreshed
[[06:47:48]] [INFO] Refreshing screenshot...
[[06:47:48]] [INFO] vfwUVEyq6X=pass
[[06:47:45]] [INFO] vfwUVEyq6X=running
[[06:47:45]] [INFO] Executing action 5/641: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[06:47:45]] [SUCCESS] Screenshot refreshed successfully
[[06:47:45]] [SUCCESS] Screenshot refreshed successfully
[[06:47:45]] [SUCCESS] Screenshot refreshed
[[06:47:45]] [INFO] Refreshing screenshot...
[[06:47:45]] [INFO] Xr6F8gdd8q=pass
[[06:47:41]] [INFO] Xr6F8gdd8q=running
[[06:47:41]] [INFO] Executing action 4/641: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[06:47:41]] [SUCCESS] Screenshot refreshed successfully
[[06:47:41]] [SUCCESS] Screenshot refreshed successfully
[[06:47:40]] [SUCCESS] Screenshot refreshed
[[06:47:40]] [INFO] Refreshing screenshot...
[[06:47:40]] [INFO] Xr6F8gdd8q=pass
[[06:47:37]] [INFO] Xr6F8gdd8q=running
[[06:47:37]] [INFO] Executing action 3/641: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[06:47:37]] [SUCCESS] Screenshot refreshed successfully
[[06:47:37]] [SUCCESS] Screenshot refreshed successfully
[[06:47:37]] [SUCCESS] Screenshot refreshed
[[06:47:37]] [INFO] Refreshing screenshot...
[[06:47:37]] [INFO] F4NGh9HrLw=pass
[[06:47:33]] [INFO] F4NGh9HrLw=running
[[06:47:33]] [INFO] Executing action 2/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[06:47:33]] [SUCCESS] Screenshot refreshed successfully
[[06:47:33]] [SUCCESS] Screenshot refreshed successfully
[[06:47:33]] [SUCCESS] Screenshot refreshed
[[06:47:33]] [INFO] Refreshing screenshot...
[[06:47:33]] [INFO] H9fy9qcFbZ=pass
[[06:47:28]] [INFO] H9fy9qcFbZ=running
[[06:47:28]] [INFO] Executing action 1/641: Restart app: env[appid]
[[06:47:28]] [INFO] ExecutionManager: Starting execution of 641 actions...
[[06:47:28]] [SUCCESS] Cleared 0 screenshots from database
[[06:47:28]] [INFO] Clearing screenshots from database before execution...
[[06:47:28]] [SUCCESS] All screenshots deleted successfully
[[06:47:28]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[06:47:28]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250726_064728/screenshots
[[06:47:28]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250726_064728
[[06:47:28]] [SUCCESS] Report directory initialized successfully
[[06:47:28]] [INFO] Initializing report directory and screenshots folder for test suite...
[[06:47:20]] [INFO] Collapsed all test cases
[[06:47:17]] [SUCCESS] All screenshots deleted successfully
[[06:47:17]] [INFO] All actions cleared
[[06:47:17]] [INFO] Cleaning up screenshots...
[[06:47:11]] [SUCCESS] Screenshot refreshed successfully
[[06:47:11]] [SUCCESS] Screenshot refreshed
[[06:47:11]] [INFO] Refreshing screenshot...
[[06:47:10]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[06:47:10]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[06:47:07]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[06:47:06]] [SUCCESS] Found 1 device(s)
[[06:47:05]] [INFO] Refreshing device list...
