Action Log - 2025-07-26 10:39:13
================================================================================

[[10:39:13]] [INFO] Generating execution report...
[[10:39:13]] [SUCCESS] All tests passed successfully!
[[10:39:13]] [INFO] N9lUf5Qlrv=fail
[[10:39:13]] [ERROR] Action 44 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:38:48]] [INFO] N9lUf5Qlrv=running
[[10:38:48]] [INFO] Executing action 44/44: Action: ifThenSteps
[[10:38:48]] [INFO] OyUowAaBzD=fail
[[10:38:48]] [ERROR] Action 43 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:38:23]] [INFO] OyUowAaBzD=running
[[10:38:23]] [INFO] Executing action 43/44: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[10:38:23]] [INFO] Ob26qqcA0p=fail
[[10:38:23]] [ERROR] Action 42 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:37:58]] [INFO] Ob26qqcA0p=running
[[10:37:58]] [INFO] Executing action 42/44: Swipe from (50%, 70%) to (50%, 30%)
[[10:37:58]] [INFO] F1olhgKhUt=fail
[[10:37:58]] [ERROR] Action 41 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:37:33]] [INFO] F1olhgKhUt=running
[[10:37:33]] [INFO] Executing action 41/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[10:37:33]] [INFO] yhmzeynQyu=fail
[[10:37:33]] [ERROR] Action 40 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:37:08]] [INFO] yhmzeynQyu=running
[[10:37:08]] [INFO] Executing action 40/44: Tap on Text: "Remove"
[[10:37:08]] [INFO] AJXVWhoBUt=fail
[[10:37:08]] [ERROR] Action 39 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:36:43]] [INFO] AJXVWhoBUt=running
[[10:36:43]] [INFO] Executing action 39/44: Action: ifThenSteps
[[10:36:43]] [INFO] yhmzeynQyu=fail
[[10:36:43]] [ERROR] Action 38 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:36:18]] [INFO] yhmzeynQyu=running
[[10:36:18]] [INFO] Executing action 38/44: Tap on Text: "Remove"
[[10:36:18]] [INFO] LCxISjrRBu=fail
[[10:36:18]] [ERROR] Action 37 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:35:54]] [INFO] LCxISjrRBu=running
[[10:35:54]] [INFO] Executing action 37/44: Action: ifThenSteps
[[10:35:54]] [INFO] F1olhgKhUt=fail
[[10:35:54]] [ERROR] Action 36 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:35:29]] [INFO] F1olhgKhUt=running
[[10:35:29]] [INFO] Executing action 36/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[10:35:29]] [INFO] lWIRxRm6HE=fail
[[10:35:29]] [ERROR] Action 35 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:35:04]] [INFO] lWIRxRm6HE=running
[[10:35:04]] [INFO] Executing action 35/44: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[10:35:04]] [INFO] uOt2cFGhGr=fail
[[10:35:04]] [ERROR] Action 34 failed: Device session is not active and recovery failed. Please reconnect the device.
[[10:34:39]] [SUCCESS] Screenshot refreshed successfully
[[10:34:39]] [SUCCESS] Screenshot refreshed successfully
[[10:34:39]] [INFO] uOt2cFGhGr=running
[[10:34:39]] [INFO] Executing action 34/44: Tap on element with xpath: //android.widget.Button[@text="Move to wishlist"]
[[10:34:38]] [SUCCESS] Screenshot refreshed
[[10:34:38]] [INFO] Refreshing screenshot...
[[10:34:38]] [INFO] bGqhW1Kciz=pass
[[10:29:49]] [SUCCESS] Screenshot refreshed successfully
[[10:29:49]] [SUCCESS] Screenshot refreshed successfully
[[10:29:48]] [SUCCESS] Screenshot refreshed
[[10:29:48]] [INFO] Refreshing screenshot...
[[10:28:04]] [SUCCESS] Screenshot refreshed successfully
[[10:28:04]] [SUCCESS] Screenshot refreshed successfully
[[10:28:04]] [INFO] bGqhW1Kciz=running
[[10:28:04]] [INFO] Executing action 33/44: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[10:28:03]] [SUCCESS] Screenshot refreshed
[[10:28:03]] [INFO] Refreshing screenshot...
[[10:28:03]] [INFO] F1olhgKhUt=pass
[[10:28:00]] [SUCCESS] Screenshot refreshed successfully
[[10:28:00]] [SUCCESS] Screenshot refreshed successfully
[[10:28:00]] [INFO] F1olhgKhUt=running
[[10:28:00]] [INFO] Executing action 32/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[10:27:59]] [SUCCESS] Screenshot refreshed
[[10:27:59]] [INFO] Refreshing screenshot...
[[10:27:59]] [INFO] yhmzeynQyu=pass
[[10:27:56]] [SUCCESS] Screenshot refreshed successfully
[[10:27:56]] [SUCCESS] Screenshot refreshed successfully
[[10:27:56]] [INFO] yhmzeynQyu=running
[[10:27:56]] [INFO] Executing action 31/44: Tap on Text: "Remove"
[[10:27:55]] [SUCCESS] Screenshot refreshed
[[10:27:55]] [INFO] Refreshing screenshot...
[[10:27:55]] [INFO] Q0fomJIDoQ=pass
[[10:27:53]] [SUCCESS] Screenshot refreshed successfully
[[10:27:53]] [SUCCESS] Screenshot refreshed successfully
[[10:27:53]] [INFO] Q0fomJIDoQ=running
[[10:27:53]] [INFO] Executing action 30/44: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[10:27:52]] [SUCCESS] Screenshot refreshed
[[10:27:52]] [INFO] Refreshing screenshot...
[[10:27:52]] [INFO] y4i304JeJj=pass
[[10:27:49]] [SUCCESS] Screenshot refreshed successfully
[[10:27:49]] [SUCCESS] Screenshot refreshed successfully
[[10:27:48]] [INFO] y4i304JeJj=running
[[10:27:48]] [INFO] Executing action 29/44: Tap on Text: "Move"
[[10:27:48]] [SUCCESS] Screenshot refreshed
[[10:27:48]] [INFO] Refreshing screenshot...
[[10:27:48]] [INFO] Q0fomJIDoQ=pass
[[10:27:45]] [SUCCESS] Screenshot refreshed successfully
[[10:27:45]] [SUCCESS] Screenshot refreshed successfully
[[10:27:44]] [INFO] Q0fomJIDoQ=running
[[10:27:44]] [INFO] Executing action 28/44: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[10:27:44]] [SUCCESS] Screenshot refreshed
[[10:27:44]] [INFO] Refreshing screenshot...
[[10:27:44]] [INFO] F1olhgKhUt=pass
[[10:27:42]] [SUCCESS] Screenshot refreshed successfully
[[10:27:42]] [SUCCESS] Screenshot refreshed successfully
[[10:27:41]] [INFO] F1olhgKhUt=running
[[10:27:41]] [INFO] Executing action 27/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[10:27:41]] [SUCCESS] Screenshot refreshed
[[10:27:41]] [INFO] Refreshing screenshot...
[[10:27:41]] [INFO] WbxRVpWtjw=pass
[[10:27:39]] [SUCCESS] Screenshot refreshed successfully
[[10:27:39]] [SUCCESS] Screenshot refreshed successfully
[[10:27:38]] [INFO] WbxRVpWtjw=running
[[10:27:38]] [INFO] Executing action 26/44: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[10:27:38]] [SUCCESS] Screenshot refreshed
[[10:27:38]] [INFO] Refreshing screenshot...
[[10:27:38]] [INFO] H3IAmq3r3i=pass
[[10:27:33]] [SUCCESS] Screenshot refreshed successfully
[[10:27:33]] [SUCCESS] Screenshot refreshed successfully
[[10:27:33]] [INFO] H3IAmq3r3i=running
[[10:27:33]] [INFO] Executing action 25/44: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[10:27:32]] [SUCCESS] Screenshot refreshed
[[10:27:32]] [INFO] Refreshing screenshot...
[[10:27:32]] [INFO] eLxHVWKeDQ=pass
[[10:27:29]] [SUCCESS] Screenshot refreshed successfully
[[10:27:29]] [SUCCESS] Screenshot refreshed successfully
[[10:27:29]] [INFO] eLxHVWKeDQ=running
[[10:27:29]] [INFO] Executing action 24/44: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[10:27:28]] [SUCCESS] Screenshot refreshed
[[10:27:28]] [INFO] Refreshing screenshot...
[[10:27:28]] [INFO] nAB6Q8LAdv=pass
[[10:27:25]] [SUCCESS] Screenshot refreshed successfully
[[10:27:25]] [SUCCESS] Screenshot refreshed successfully
[[10:27:25]] [INFO] nAB6Q8LAdv=running
[[10:27:25]] [INFO] Executing action 23/44: Wait till xpath=//android.widget.Button[@text="Filter"]
[[10:27:24]] [SUCCESS] Screenshot refreshed
[[10:27:24]] [INFO] Refreshing screenshot...
[[10:27:24]] [INFO] H3IAmq3r3i=pass
[[10:27:22]] [SUCCESS] Screenshot refreshed successfully
[[10:27:22]] [SUCCESS] Screenshot refreshed successfully
[[10:27:22]] [INFO] H3IAmq3r3i=running
[[10:27:22]] [INFO] Executing action 22/44: Input text: "P_42999157"
[[10:27:21]] [SUCCESS] Screenshot refreshed
[[10:27:21]] [INFO] Refreshing screenshot...
[[10:27:21]] [INFO] wzxrm7WwXv=pass
[[10:27:05]] [SUCCESS] Screenshot refreshed successfully
[[10:27:05]] [SUCCESS] Screenshot refreshed successfully
[[10:27:05]] [INFO] wzxrm7WwXv=running
[[10:27:05]] [INFO] Executing action 21/44: Tap on image: search-glassimage-android.png
[[10:27:04]] [SUCCESS] Screenshot refreshed
[[10:27:04]] [INFO] Refreshing screenshot...
[[10:27:04]] [INFO] F1olhgKhUt=pass
[[10:27:01]] [SUCCESS] Screenshot refreshed successfully
[[10:27:01]] [SUCCESS] Screenshot refreshed successfully
[[10:27:01]] [INFO] F1olhgKhUt=running
[[10:27:01]] [INFO] Executing action 20/44: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[10:27:00]] [SUCCESS] Screenshot refreshed
[[10:27:00]] [INFO] Refreshing screenshot...
[[10:27:00]] [INFO] SxKlojIFRq=pass
[[10:26:58]] [SUCCESS] Screenshot refreshed successfully
[[10:26:58]] [SUCCESS] Screenshot refreshed successfully
[[10:26:58]] [INFO] SxKlojIFRq=running
[[10:26:58]] [INFO] Executing action 19/44: Android Function: send_key_event - Key Event: BACK
[[10:26:58]] [SUCCESS] Screenshot refreshed
[[10:26:58]] [INFO] Refreshing screenshot...
[[10:26:58]] [INFO] WbxRVpWtjw=pass
[[10:26:55]] [SUCCESS] Screenshot refreshed successfully
[[10:26:55]] [SUCCESS] Screenshot refreshed successfully
[[10:26:55]] [INFO] WbxRVpWtjw=running
[[10:26:55]] [INFO] Executing action 18/44: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[10:26:54]] [SUCCESS] Screenshot refreshed
[[10:26:54]] [INFO] Refreshing screenshot...
[[10:26:54]] [INFO] H3IAmq3r3i=pass
[[10:26:42]] [INFO] H3IAmq3r3i=running
[[10:26:42]] [INFO] Executing action 17/44: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[10:26:42]] [INFO] ITHvSyXXmu=fail
[[10:26:42]] [ERROR] Action 16 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[10:26:10]] [SUCCESS] Screenshot refreshed successfully
[[10:26:10]] [SUCCESS] Screenshot refreshed successfully
[[10:26:10]] [INFO] ITHvSyXXmu=running
[[10:26:10]] [INFO] Executing action 16/44: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[10:26:09]] [SUCCESS] Screenshot refreshed
[[10:26:09]] [INFO] Refreshing screenshot...
[[10:26:09]] [INFO] WbxRVpWtjw=pass
[[10:26:07]] [SUCCESS] Screenshot refreshed successfully
[[10:26:07]] [SUCCESS] Screenshot refreshed successfully
[[10:26:07]] [INFO] WbxRVpWtjw=running
[[10:26:07]] [INFO] Executing action 15/44: Tap on element with xpath: //android.widget.ListView[contains(@resource-id,"swiper-wrapper")]/android.view.View[1]//android.widget.TextView
[[10:26:06]] [SUCCESS] Screenshot refreshed
[[10:26:06]] [INFO] Refreshing screenshot...
[[10:26:06]] [INFO] H3IAmq3r3i=pass
[[10:26:03]] [SUCCESS] Screenshot refreshed successfully
[[10:26:03]] [SUCCESS] Screenshot refreshed successfully
[[10:26:03]] [INFO] H3IAmq3r3i=running
[[10:26:03]] [INFO] Executing action 14/44: Swipe up till element xpath: "//android.widget.ListView[contains(@resource-id,"swiper-wrapper")]/android.view.View[1]//android.widget.TextView" is visible
[[10:26:02]] [SUCCESS] Screenshot refreshed
[[10:26:02]] [INFO] Refreshing screenshot...
[[10:26:02]] [INFO] Ob26qqcA0p=pass
[[10:25:57]] [SUCCESS] Screenshot refreshed successfully
[[10:25:57]] [SUCCESS] Screenshot refreshed successfully
[[10:25:57]] [INFO] Ob26qqcA0p=running
[[10:25:57]] [INFO] Executing action 13/44: Swipe from (50%, 70%) to (50%, 30%)
[[10:25:56]] [SUCCESS] Screenshot refreshed
[[10:25:56]] [INFO] Refreshing screenshot...
[[10:25:56]] [INFO] WbxRVpWtjw=pass
[[10:25:54]] [SUCCESS] Screenshot refreshed successfully
[[10:25:54]] [SUCCESS] Screenshot refreshed successfully
[[10:25:54]] [INFO] WbxRVpWtjw=running
[[10:25:54]] [INFO] Executing action 12/44: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[10:25:53]] [SUCCESS] Screenshot refreshed
[[10:25:53]] [INFO] Refreshing screenshot...
[[10:25:53]] [INFO] H3IAmq3r3i=pass
[[10:25:47]] [SUCCESS] Screenshot refreshed successfully
[[10:25:47]] [SUCCESS] Screenshot refreshed successfully
[[10:25:47]] [INFO] H3IAmq3r3i=running
[[10:25:47]] [INFO] Executing action 11/44: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[10:25:46]] [SUCCESS] Screenshot refreshed
[[10:25:46]] [INFO] Refreshing screenshot...
[[10:25:46]] [INFO] ITHvSyXXmu=pass
[[10:25:43]] [SUCCESS] Screenshot refreshed successfully
[[10:25:43]] [SUCCESS] Screenshot refreshed successfully
[[10:25:43]] [INFO] ITHvSyXXmu=running
[[10:25:43]] [INFO] Executing action 10/44: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[10:25:43]] [SUCCESS] Screenshot refreshed
[[10:25:43]] [INFO] Refreshing screenshot...
[[10:25:43]] [INFO] eLxHVWKeDQ=pass
[[10:25:40]] [SUCCESS] Screenshot refreshed successfully
[[10:25:40]] [SUCCESS] Screenshot refreshed successfully
[[10:25:39]] [INFO] eLxHVWKeDQ=running
[[10:25:39]] [INFO] Executing action 9/44: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[10:25:39]] [SUCCESS] Screenshot refreshed
[[10:25:39]] [INFO] Refreshing screenshot...
[[10:25:39]] [INFO] nAB6Q8LAdv=pass
[[10:25:32]] [SUCCESS] Screenshot refreshed successfully
[[10:25:32]] [SUCCESS] Screenshot refreshed successfully
[[10:25:32]] [INFO] nAB6Q8LAdv=running
[[10:25:32]] [INFO] Executing action 8/44: Wait till xpath=//android.widget.Button[@text="Filter"]
[[10:25:32]] [SUCCESS] Screenshot refreshed
[[10:25:32]] [INFO] Refreshing screenshot...
[[10:25:32]] [INFO] sc2KH9bG6H=pass
[[10:25:30]] [SUCCESS] Screenshot refreshed successfully
[[10:25:30]] [SUCCESS] Screenshot refreshed successfully
[[10:25:30]] [INFO] sc2KH9bG6H=running
[[10:25:30]] [INFO] Executing action 7/44: Input text: "Uno card"
[[10:25:29]] [SUCCESS] Screenshot refreshed
[[10:25:29]] [INFO] Refreshing screenshot...
[[10:25:29]] [INFO] rqLJpAP0mA=pass
[[10:25:25]] [SUCCESS] Screenshot refreshed successfully
[[10:25:25]] [SUCCESS] Screenshot refreshed successfully
[[10:25:23]] [INFO] rqLJpAP0mA=running
[[10:25:23]] [INFO] Executing action 6/44: Tap on Text: "Find"
[[10:25:23]] [SUCCESS] Screenshot refreshed
[[10:25:23]] [INFO] Refreshing screenshot...
[[10:25:23]] [INFO] yiKyF5FJwN=pass
[[10:25:21]] [SUCCESS] Screenshot refreshed successfully
[[10:25:21]] [SUCCESS] Screenshot refreshed successfully
[[10:25:16]] [INFO] yiKyF5FJwN=running
[[10:25:16]] [INFO] Executing action 5/44: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[10:25:16]] [SUCCESS] Screenshot refreshed
[[10:25:16]] [INFO] Refreshing screenshot...
[[10:25:16]] [SUCCESS] Screenshot refreshed
[[10:25:16]] [INFO] Refreshing screenshot...
[[10:25:13]] [SUCCESS] Screenshot refreshed successfully
[[10:25:13]] [SUCCESS] Screenshot refreshed successfully
[[10:25:13]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[10:25:13]] [SUCCESS] Screenshot refreshed
[[10:25:13]] [INFO] Refreshing screenshot...
[[10:25:10]] [SUCCESS] Screenshot refreshed successfully
[[10:25:10]] [SUCCESS] Screenshot refreshed successfully
[[10:25:10]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[10:25:10]] [SUCCESS] Screenshot refreshed
[[10:25:10]] [INFO] Refreshing screenshot...
[[10:25:06]] [SUCCESS] Screenshot refreshed successfully
[[10:25:06]] [SUCCESS] Screenshot refreshed successfully
[[10:25:06]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[10:25:06]] [SUCCESS] Screenshot refreshed
[[10:25:06]] [INFO] Refreshing screenshot...
[[10:25:03]] [SUCCESS] Screenshot refreshed successfully
[[10:25:03]] [SUCCESS] Screenshot refreshed successfully
[[10:25:03]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[10:25:03]] [SUCCESS] Screenshot refreshed
[[10:25:03]] [INFO] Refreshing screenshot...
[[10:25:00]] [SUCCESS] Screenshot refreshed successfully
[[10:25:00]] [SUCCESS] Screenshot refreshed successfully
[[10:25:00]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[10:25:00]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[10:25:00]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[10:25:00]] [INFO] rUH3kvaEH9=running
[[10:25:00]] [INFO] Executing action 4/44: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[10:24:59]] [SUCCESS] Screenshot refreshed
[[10:24:59]] [INFO] Refreshing screenshot...
[[10:24:59]] [INFO] rkL0oz4kiL=pass
[[10:24:52]] [SUCCESS] Screenshot refreshed successfully
[[10:24:52]] [SUCCESS] Screenshot refreshed successfully
[[10:24:52]] [INFO] rkL0oz4kiL=running
[[10:24:52]] [INFO] Executing action 3/44: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:24:51]] [SUCCESS] Screenshot refreshed
[[10:24:51]] [INFO] Refreshing screenshot...
[[10:24:51]] [INFO] HotUJOd6oB=pass
[[10:24:50]] [SUCCESS] Screenshot refreshed successfully
[[10:24:50]] [SUCCESS] Screenshot refreshed successfully
[[10:24:48]] [INFO] HotUJOd6oB=running
[[10:24:48]] [INFO] Executing action 2/44: Launch app: au.com.kmart
[[10:24:48]] [SUCCESS] Screenshot refreshed
[[10:24:48]] [INFO] Refreshing screenshot...
[[10:24:48]] [INFO] HotUJOd6oB=pass
[[10:24:42]] [INFO] HotUJOd6oB=running
[[10:24:42]] [INFO] Executing action 1/44: Terminate app: au.com.kmart
[[10:24:42]] [INFO] ExecutionManager: Starting execution of 44 actions...
[[10:24:42]] [SUCCESS] Cleared 1 screenshots from database
[[10:24:42]] [INFO] Clearing screenshots from database before execution...
[[10:24:42]] [SUCCESS] All screenshots deleted successfully
[[10:24:42]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:24:42]] [INFO] Skipping report initialization - single test case execution
[[10:24:37]] [SUCCESS] All screenshots deleted successfully
[[10:24:37]] [SUCCESS] Loaded test case "WishList_AU-Android" with 44 actions
[[10:24:37]] [SUCCESS] Added action: ifThenSteps
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: swipe
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tapOnText
[[10:24:37]] [SUCCESS] Added action: ifThenSteps
[[10:24:37]] [SUCCESS] Added action: tapOnText
[[10:24:37]] [SUCCESS] Added action: ifThenSteps
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tapIfLocatorExists
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tapOnText
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tapOnText
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: swipeTillVisible
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: waitTill
[[10:24:37]] [SUCCESS] Added action: text
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: androidFunctions
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: swipeTillVisible
[[10:24:37]] [SUCCESS] Added action: waitTill
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: swipeTillVisible
[[10:24:37]] [SUCCESS] Added action: swipe
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: swipeTillVisible
[[10:24:37]] [SUCCESS] Added action: waitTill
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: waitTill
[[10:24:37]] [SUCCESS] Added action: text
[[10:24:37]] [SUCCESS] Added action: tapOnText
[[10:24:37]] [SUCCESS] Added action: exists
[[10:24:37]] [SUCCESS] Added action: multiStep
[[10:24:37]] [SUCCESS] Added action: tap
[[10:24:37]] [SUCCESS] Added action: launchApp
[[10:24:37]] [SUCCESS] Added action: terminateApp
[[10:24:37]] [INFO] All actions cleared
[[10:24:37]] [INFO] Cleaning up screenshots...
[[10:24:32]] [SUCCESS] Screenshot refreshed successfully
[[10:24:30]] [SUCCESS] Screenshot refreshed
[[10:24:30]] [INFO] Refreshing screenshot...
[[10:24:29]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[10:24:29]] [INFO] Device info updated: RMX2151
[[10:23:52]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[10:23:49]] [SUCCESS] Found 1 device(s)
[[10:23:46]] [INFO] Refreshing device list...
